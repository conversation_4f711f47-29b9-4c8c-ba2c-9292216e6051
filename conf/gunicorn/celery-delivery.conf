# Start celery as daemon with Upstart
# sudo nano /etc/init/celery-delivery.conf
# sudo service celery-delivery reload
# sudo less /var/log/upstart/celery-delivery.log
# pip install flower
# celery -A run0km flower
description "Celery worker to delvieryrun"

start on runlevel [2345]
stop on runlevel [!2345]

respawn
setuid ubuntu
setgid www-data
chdir /var/www/master/deliveryrun/run0km

env LD_LIBRARY_PATH=/usr/local/ssl/lib:$LD_LIBRARY_PATH
exec /var/www/master/deliveryrun/venv3.8/bin/celery -A run0km worker --beat --schedule=/tmp/celerybeat-schedule --loglevel=INFO