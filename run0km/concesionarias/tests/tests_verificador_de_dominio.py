from mock import patch
from django.contrib.auth import get_user_model
from django.urls import reverse

from concesionarias.models import Sitio
from concesionarias.verificacion_de_dominio import VerificadorDeDominioPorConcesionaria
from testing.base import BaseFixturedTest


class VerificadorDeDominioTest(BaseFixturedTest):

    def test_concesionaria_correspondiente_al_dominio_sin_concesionaria(self):
        user = self.fixture['usr_1']
        request = MockRequest(user, 'concesionaria.uno.com', '/')
        verificador = VerificadorDeDominioPorConcesionaria()
        sitio, concesionarias = verificador.sitio_y_concesionarias_para_personalizacion(request)
        self.assertIsNone(sitio)
        self.assertIsNotNone(concesionarias)
        self.assertEqual(user.concesionaria(), concesionarias[0])

    def test_concesionaria_correspondiente_al_dominio_con_concesionaria(self):
        user = self.fixture['usr_1']
        concesionaria_usr = user.concesionaria()
        sitio = Sitio(url='concesionaria.uno.com')
        sitio.save()
        concesionaria_usr.sitio = sitio
        concesionaria_usr.save()
        request = MockRequest(user, 'concesionaria.uno.com', '/')
        verificador = VerificadorDeDominioPorConcesionaria()
        sitio, concesionarias = verificador.sitio_y_concesionarias_para_personalizacion(request)
        self.assertEqual(sitio, sitio)
        self.assertEqual(concesionarias[0], user.concesionaria())

    @patch.object(get_user_model(), 'is_authenticated', return_value=False)
    def test_concesionaria_para_personalizacion_sin_loguear_sin_concesionaria(self, is_authenticated_mock):
        user = self.fixture['usr_1']
        request = MockRequest(user, 'concesionaria.uno.com', '/')
        verificador = VerificadorDeDominioPorConcesionaria()
        sitio, concesionarias = verificador.sitio_y_concesionarias_para_personalizacion(request)
        self.assertIsNone(sitio)
        self.assertIsNone(concesionarias)

    @patch.object(get_user_model(), 'is_authenticated', return_value=True)
    def test_concesionaria_para_personalizacion_logueado_sin_concesionaria(self, is_authenticated_mock):
        user = self.fixture['usr_1']
        request = MockRequest(user, 'concesionaria.uno.com', '/')
        verificador = VerificadorDeDominioPorConcesionaria()
        sitio, concesionarias = verificador.sitio_y_concesionarias_para_personalizacion(request)
        self.assertEqual(user.concesionaria(), concesionarias[0])
        self.assertEqual(user.concesionaria(), concesionarias[0])

    @patch.object(get_user_model(), 'is_authenticated', return_value=False)
    def test_concesionaria_para_personalizacion_sin_loguear_con_concesionaria(self, is_authenticated_mock):
        user = self.fixture['usr_1']
        concesionaria = self.fixture['conce_2']
        sitio = Sitio(url='concesionaria.dos.com')
        sitio.save()
        concesionaria.sitio = sitio
        concesionaria.save()
        request = MockRequest(user, 'concesionaria.dos.com', '/')
        verificador = VerificadorDeDominioPorConcesionaria()
        sitio, concesionarias = verificador.sitio_y_concesionarias_para_personalizacion(request)
        self.assertEqual(sitio, concesionaria.sitio)
        self.assertEqual(concesionaria, concesionarias[0])

    def test_dominio_valido_para_usuario_de_otra_concesionaria_es_falso(self):
        user = self.fixture['usr_1']
        concesionaria = self.fixture['conce_2']
        sitio = Sitio(url='concesionaria.dos.com')
        sitio.save()
        concesionaria.sitio = sitio
        concesionaria.save()
        request = MockRequest(user, 'concesionaria.dos.com', '/')
        verificador = VerificadorDeDominioPorConcesionaria()
        self.assertFalse(verificador.dominio_valido_para_usuario(user, request))

    def test_dominio_valido_para_usuario_de_la_concesionaria(self):
        user = self.fixture['usr_1']
        concesionaria = user.concesionaria()
        sitio = Sitio(url='concesionaria.uno.com')
        sitio.save()
        concesionaria.sitio = sitio
        concesionaria.save()
        request = MockRequest(user, 'concesionaria.uno.com', '/')
        verificador = VerificadorDeDominioPorConcesionaria()
        self.assertTrue(verificador.dominio_valido_para_usuario(user, request))

    def test_dominio_valido_para_usuario_es_falso_si_su_concesionaria_tiene_dominio(self):
        user = self.fixture['usr_1']
        concesionaria = user.concesionaria()
        sitio = Sitio(url='concesionaria.uno.com')
        sitio.save()
        concesionaria.sitio = sitio
        concesionaria.save()
        request = MockRequest(user, 'delivery.run0km.com', reverse('resumen'))
        verificador = VerificadorDeDominioPorConcesionaria()
        self.assertFalse(verificador.dominio_valido_para_usuario(user, request))


####
"""
  Sitio1: pre.sitio.com
  Sitio2: sitio.com


"""
####


class MockRequest(object):
    def __init__(self, user, dominio, path):
        self.path = path
        self.user = user
        self.dominio = dominio

    def get_host(self):
        return self.dominio


def mock_true():
    return True


def mock_false():
    return False