# -*- coding: utf-8 -*-
from django import forms
from django.core.exceptions import ValidationError
from django.forms import widgets

from occ.campania_envios_sms import EmisorDeCampaniasDeSMS
from occ.models import Medio


class CampaniaDeComunicacionForm(forms.Form):
    medio = forms.ModelChoiceField(queryset=Medio.objects.all(), required=True, initial=0)
    mensaje = forms.CharField(max_length=145, required=True, widget=widgets.Textarea)
    cantidad = forms.IntegerField(min_value=1, required=True)

    def __init__(self, maxima_cantidad=None, *args, **kwargs):
        self.maxima_cantidad = maxima_cantidad
        super(CampaniaDeComunicacionForm, self).__init__(*args, **kwargs)

    def clean_cantidad(self):
        if 'cantidad' in self.cleaned_data and self.cleaned_data['cantidad'] > self.maxima_cantidad:
            raise ValidationError('Debe ingresar una cantidad válida')
        return self.cleaned_data.get('cantidad', None)

    def clean_mensaje(self):
        if 'mensaje' in self.cleaned_data and \
                not EmisorDeCampaniasDeSMS.tiene_caracteres_validos(self.cleaned_data['mensaje']):
            raise ValidationError('Debe ingresar caracteres válidos')
        return self.cleaned_data.get('mensaje', None)


class MensajeForm(forms.Form):
    mensaje = forms.CharField(required=True, widget=widgets.Textarea)

    def __init__(self, validar_caracteres=False, maxima_cantidad=None, *args, **kwargs):
        self.validar_caracteres = validar_caracteres
        super(MensajeForm, self).__init__(*args, **kwargs)
        max_length = maxima_cantidad or 235
        mensaje = forms.CharField(max_length=max_length, required=True, widget=widgets.Textarea)
        self.fields['mensaje'] = mensaje

    def clean_mensaje(self):
        if self.validar_caracteres and not self._tiene_mensaje_valido():
            raise ValidationError('Debe ingresar caracteres válidos')
        return self.cleaned_data.get('mensaje', None)

    def _tiene_mensaje_valido(self):
        mensaje = self.cleaned_data.get('mensaje', '')
        return EmisorDeCampaniasDeSMS.tiene_caracteres_validos(mensaje)


class ConfigurarDirectaCuentaPropiaForm(forms.Form):
    tipo = forms.CharField(widget=forms.HiddenInput())
    email = forms.CharField(required=True)
    password = forms.CharField(required=True)

    def __init__(self, deshabilitada, *args, **kwargs):
        super(ConfigurarDirectaCuentaPropiaForm, self).__init__(*args, **kwargs)
        self.fields['email'].widget.attrs['readonly'] = deshabilitada
        self.fields['password'].widget.attrs['readonly'] = deshabilitada


class ConfigurarCuentaPropiaForm(forms.Form):
    tipo = forms.CharField(widget=forms.HiddenInput())
    url = forms.URLField(required=True)

    def __init__(self, deshabilitada, *args, **kwargs):
        super(ConfigurarCuentaPropiaForm, self).__init__(*args, **kwargs)
        self.fields['url'].widget.attrs['readonly'] = deshabilitada


class ConfigurarCuentaInternaForm(forms.Form):
    # ToDo: falta tomar las choices segun el model
    tipo = forms.CharField(widget=forms.HiddenInput())
    nombre = forms.CharField(required=True, label='')
    dominio = forms.ChoiceField(choices=(('1', 'runokm.com'),), initial='runokm', label='@')

    def __init__(self, deshabilitada, *args, **kwargs):
        super(ConfigurarCuentaInternaForm, self).__init__(*args, **kwargs)
        self.fields['nombre'].widget.attrs['readonly'] = deshabilitada
        self.fields['dominio'].widget.attrs['readonly'] = deshabilitada
