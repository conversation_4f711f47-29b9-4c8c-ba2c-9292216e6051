# coding=utf-8
from django.core.exceptions import ValidationError
from django.db import models
from django.utils.timezone import now
from embed_video.fields import EmbedVideo<PERSON>ield

from occ.novedades.contenido import Contenido
from occ.querysets.novedades import NovedadQueryset, VisualizacionesDeNovedadesPorUsuarioQueryset


class Novedad(models.Model):
    _nombre = models.CharField("Nombre", max_length=255)
    _imagen = models.ImageField("Imagen", upload_to='novedades', null=True, blank=True)
    _url = models.URLField("Link/URL extra", max_length=255, blank=True, null=True)
    _video_url = EmbedVideoField("URL a video", blank=True, null=True)
    _activa = models.BooleanField("Activa", default=False, db_index=True)
    _para_vendedores = models.BooleanField("Para vendedores", default=False)
    _para_supervisores = models.BooleanField("Para supervisores", default=False)
    _para_gerentes = models.BooleanField("Para gerentes", default=False)
    _fecha_de_creacion = models.DateTimeField("Fecha de creación", auto_now_add=True)

    objects = NovedadQueryset.as_manager()

    @classmethod
    def nueva_para(cls, imagen=None, url='', video_url='', nombre="Sin nombre", para_vendedores=False,
                   para_supervisores=False,
                   para_gerentes=False, activa=False):
        novedad = cls(_imagen=imagen, _url=url, _video_url=video_url, _para_vendedores=para_vendedores,
                      _para_gerentes=para_gerentes, _para_supervisores=para_supervisores, _activa=activa,
                      _nombre=nombre)
        novedad.full_clean()
        novedad.save()
        return novedad

    @classmethod
    def mensaje_de_error_url_sin_imagen(cls):
        return "No puede existir un link/URL extra sin una imagen"

    @classmethod
    def mensaje_de_error_imagen_y_video_al_mismo_tiempo(cls):
        return "La novedad puede tener solo imagen o solo video"

    def nombre(self):
        return self._nombre

    def imagen(self):
        return self._imagen

    def url(self):
        return self._url

    def video_url(self):
        return self._video_url

    def para_vendedores(self):
        return self._para_vendedores

    def para_supervisores(self):
        return self._para_supervisores

    def para_gerentes(self):
        return self._para_gerentes

    def activa(self):
        return self._activa

    def clean(self):
        super(Novedad, self).clean()
        if self._video_url and self._imagen:
            error_message = self.mensaje_de_error_imagen_y_video_al_mismo_tiempo()
            raise ValidationError({'_video_url': error_message, '_imagen': error_message})
        if self._url and not self._imagen:
            raise ValidationError({'_url': self.mensaje_de_error_url_sin_imagen()})

    def tiene_imagen(self):
        return self._imagen and hasattr(self._imagen, 'url')

    def contenido(self):
        return Contenido.para(self)

    def __str__(self):
        return "Novedad: %s" % (self._nombre or 'Sin nombre %s' % self.id)

    class Meta:
        verbose_name_plural = "Novedades"


class VisualizacionesDeNovedadesPorUsuario(models.Model):
    _usuario = models.OneToOneField('users.User', related_name="visualizaciones_de_novedades")
    _fecha_de_ultima_visualizacion = models.DateField("Fecha de última visualización", blank=True, null=True)
    _cantidad = models.PositiveSmallIntegerField(default=0)

    objects = VisualizacionesDeNovedadesPorUsuarioQueryset.as_manager()

    def usuario(self):
        return self._usuario

    def fecha_de_ultima_visualizacion(self):
        return self._fecha_de_ultima_visualizacion

    def cantidad(self):
        return self._cantidad

    @classmethod
    def nuevo_para(cls, usuario, fecha_de_ultima_visualizacion=None):
        visualizacion_de_novedades = cls(_usuario=usuario, _fecha_de_ultima_visualizacion=fecha_de_ultima_visualizacion)
        visualizacion_de_novedades.full_clean()
        visualizacion_de_novedades.save()
        return visualizacion_de_novedades

    def resetear_contador_y_fecha(self):
        self._fecha_de_ultima_visualizacion = None
        self._cantidad = 0
        self.full_clean()
        self.save()

    def sumar_una_visualizacion(self):
        self._cantidad += 1
        self._fecha_de_ultima_visualizacion = now()
        self.full_clean()
        self.save()

    def __str__(self):
        return "Visualizaciones de novedades para %s" % self._usuario.get_username()

    class Meta:
        verbose_name_plural = "Visualizaciones de novedades por usuario"
