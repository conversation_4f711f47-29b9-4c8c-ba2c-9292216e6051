class MensajeAProspectoWrapper(object):
    def __init__(self, prospecto, mensaje):
        self._prospecto = prospecto
        self._mensaje = mensaje

    def prospecto(self):
        return self._prospecto

    def __getattr__(self, name):
        return getattr(self._mensaje, name)

    @classmethod
    def nuevo_para(cls, prospecto, mensaje):
        return cls(prospecto, mensaje)


class MensajeDePropuestaWrapper(object):
    def __init__(self, propuesta, mensaje):
        self._propuesta = propuesta
        self._mensaje = mensaje

    def propuesta(self):
        return self._propuesta

    def __getattr__(self, name):
        return getattr(self._mensaje, name)

    @classmethod
    def nuevo_para(cls, propuesta, mensaje):
        return cls(propuesta, mensaje)
