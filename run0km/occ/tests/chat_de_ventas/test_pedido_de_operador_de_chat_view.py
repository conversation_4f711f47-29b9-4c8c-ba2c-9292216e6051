# coding=utf-8
import json

import mock
from django.urls import reverse
from django.test.utils import override_settings
from rest_framework import status

from campanias.models import CategoriaDeCampania, Campania
from occ.models import Compulsa
from occ.tests.chat_de_ventas.test_creacion_pedidos import PedidoBuilder
from occ.tests.servicio_de_notificacion_mock import ServicioDeComunicacionSuccessMock
from testing.creador_de_contexto import CreadorDeContexto
from testing.base import BaseFixturedTest
from vendedores.control_de_actividad import ControladorDeActividad
from vendedores.models import Vendedor


@override_settings(
    CHAT_SELECTOR_PARTICIPANTES_CLASS_NAME='occ.chat_seleccion_de_participantes.SelectorParticipantesCompulsa')
@mock.patch("occ.servicio_de_notificaciones.ServicioDeNotificaciones.enviar_participacion_a_compulsa",
            side_effect=ServicioDeComunicacionSuccessMock().enviar_participacion_a_compulsa)
class OCCPedidoDeOperadorParaChatDeVentaViewTest(BaseFixturedTest):
    def _agregar_actividad_a_vendedores(self):
        for vendedor in Vendedor.objects.all():
            ControladorDeActividad().registrar_actividad(vendedor.user)

    def _crear_pedido_de_prospecto_para(self, campania):
        pedido_con_creditos = CreadorDeContexto(
            supervisor=self.fixture['sup_1'],
            fixture=self.fixture).agregar_pedido_para_todos_los_vendedores(credito=5000,
                                                                           campania=campania)
        return pedido_con_creditos

    def _assert_creacion_exitosa_de_compulsa(self, pedido_con_credito, response):
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(Compulsa.objects.count(), 1)
        self.assertEqual(set(Compulsa.objects.first().participantes()), set(pedido_con_credito.vendedores()))

    def _crear_campania_externa_con_nombre(self, nombre):
        origen = self.fixture['tipo_s']
        concesionaria = self.fixture['conce_1']
        try:
            categoria = CategoriaDeCampania.objects.externas().get(tipo_de_origen=origen)
        except CategoriaDeCampania.DoesNotExist:
            categoria = CategoriaDeCampania.nueva_externa('externa', origen)
        campania = Campania.objects.create(concesionaria=concesionaria, nombre=nombre, categoria=categoria)
        return campania

    def _post_pedido(self, datos, content_type='application/json'):
        url = reverse('pedido_operador_chat')
        response = self.client.post(url, json.dumps(datos), content_type=content_type)
        return response

    def test_pedido_vacio_debe_reponder_request_erroneo_y_no_generar_compulsa(self, mock_enviar_notificacion):
        pedido = {}
        response = self._post_pedido(datos=pedido)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(Compulsa.objects.exists())

    def test_pedido_sin_campo_campania_debe_reponder_request_erroneo_y_no_generar_compulsa(
            self, mock_enviar_notificacion):
        pedido = {"Token": "07ceee86-6306-4782-8186-f68c1f49135e"}
        response = self._post_pedido(datos=pedido)
        self.assertContains(response=response,
                            text='Invalid JSON - \'Campaign\' is a required property',
                            status_code=status.HTTP_400_BAD_REQUEST)
        self.assertFalse(Compulsa.objects.exists())

    def test_pedido_para_campania_y_marca_con_prospectos_pedidos_debe_responder_OK_e_iniciar_compulsa(
            self, mock_enviar_notificacion):
        self._agregar_actividad_a_vendedores()
        campania = self.fixture['camp_1']
        pedido_con_credito = self._crear_pedido_de_prospecto_para(campania)
        pedido_ford_json = PedidoBuilder.pedido_de_operador(token='07ceee86-6306-4782-8186-f68c1f49135e',
                                                            nombre_campania=campania.nombre,
                                                            marca='Ford')
        response = self._post_pedido(datos=pedido_ford_json)
        self._assert_creacion_exitosa_de_compulsa(pedido_con_credito, response)

    def test_pedido_para_campania_con_externa_con_mismo_nombre_debe_responder_OK_e_iniciar_compulsa(
            self, mock_enviar_notificacion):
        self._agregar_actividad_a_vendedores()
        campania = self.fixture['camp_1']
        self._crear_campania_externa_con_nombre(campania.nombre)
        pedido_con_credito = self._crear_pedido_de_prospecto_para(campania)
        pedido_ford_json = PedidoBuilder.pedido_de_operador(token='07ceee86-6306-4782-8186-f68c1f49135e',
                                                            nombre_campania=campania.nombre,
                                                            marca='Ford')
        response = self._post_pedido(datos=pedido_ford_json)
        self._assert_creacion_exitosa_de_compulsa(pedido_con_credito, response)

    def test_pedido_para_varias_marcas_sin_vendedores_deberia_responder_error_y_crear_compulsa_finalizada(
            self, mock_enviar_notificacion):
        pedido_ford_json = PedidoBuilder.pedido_de_operador(token='07ceee86-6306-4782-8186-f68c1f49135e',
                                                            nombre_campania=self.fixture['camp_1'].nombre,
                                                            marca='Peugeot|Peugeot|Ford')
        response = self._post_pedido(datos=pedido_ford_json)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(Compulsa.objects.count(), 1)
        compulsa = Compulsa.objects.first()
        self.assertTrue(compulsa.esta_finalizada_por_timeout())