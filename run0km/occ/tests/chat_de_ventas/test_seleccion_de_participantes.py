from datetime import timedelta

import mock
from django.test import override_settings
from django.utils import timezone
from freezegun import freeze_time

from core.models import Sistema
from occ.chat_criterios_de_seleccion_de_participantes import CriterioPorConversiones, CriterioPorVentasDeChat, \
    CriterioPorUltimaActividad
from occ.errores_de_chat import CantidadDeReintentosSuperadaError
from occ.models import Compulsa, ChatDeVentas, ChatDeVentasConvertido
from occ.chat_seleccion_de_participantes import SelectorParticipantesCompulsa
from occ.soporte_chat import PedidoDeOperador
from occ.tests.chat_de_ventas.test_creacion_pedidos import PedidoBuilder
from prospectos.models import FiltroDePedido
from testing.creador_de_contexto import CreadorDeContexto
from testing.base import BaseFixturedTest
from testing.factories import ConcesionariasFactory
from testing.test_utils import reload_model
from vendedores.control_de_actividad import ControladorDeActividad
from vendedores.gestor import GestorDeVendedores


class SelectorDeCriteriosDeParticipantesStup(object):
    def __init__(self, criterio):
        super(SelectorDeCriteriosDeParticipantesStup, self).__init__()
        self._criterio = criterio

    def siguiente(self):
        return self._criterio

    @classmethod
    def por_ultima_actividad(cls):
        return cls(CriterioPorUltimaActividad())

    @classmethod
    def por_ventas(cls):
        return cls(CriterioPorVentasDeChat())

    @classmethod
    def por_conversiones(cls):
        return cls(CriterioPorConversiones())


class AdministradorDePedidosStub(object):
    def __init__(self, pedidos):
        self.pedidos = pedidos

    def pedidos_que_aplican_para(self, campania, marca=None, provincia=None, localidad=None):
        return self.pedidos


class ParticipantesTest(BaseFixturedTest):
    def setUp(self):
        super(ParticipantesTest, self).setUp()
        self.campania = self.fixture['camp_1']
        self.creador_de_contexto = CreadorDeContexto(fixture=self.fixture, supervisor=self.fixture['sup_1'])

    def _asignar_a(self, vendedor, cantidad, cantidad_convertidos, cantidad_de_ventas_convertidos=0,
                   cantidad_de_ventas_no_convertidos=0):
        prospectos = self.creador_de_contexto.asignar_prospectos_nuevos_para(
            vendedores=[vendedor], cantidad=cantidad + cantidad_convertidos)
        prospectos_a_convertir = prospectos[0:cantidad_convertidos]
        prospectos_no_convertidos = prospectos[cantidad_convertidos:]
        self._configurar_como_convertidos(prospectos=prospectos_a_convertir)
        self._crear_ventas(cantidad=cantidad_de_ventas_convertidos, prospectos=prospectos_a_convertir,
                           vendedor=vendedor)
        self._crear_ventas(cantidad=cantidad_de_ventas_no_convertidos, prospectos=prospectos_no_convertidos,
                           vendedor=vendedor)
        return prospectos

    def _crear_ventas(self, cantidad, prospectos, vendedor):
        assert (len(prospectos) >= cantidad)
        for indice in range(0, cantidad):
            prospecto = prospectos[indice]
            self.creador_de_contexto.asignar_venta_a(vendedor=vendedor, prospecto=prospecto)

    def _configurar_como_convertidos(self, prospectos):
        for prospecto in prospectos:
            chat = ChatDeVentas.nuevo_con_envio_con(vendedor=prospecto.vendedor, token=prospecto.pk, texto='Hello')
            ChatDeVentasConvertido.nuevo(prospecto=prospecto, chat=chat)

    def _crear_chats(self, cantidad_sin_convertir, cantidad_convertidos, vendedor):
        cantidad = ChatDeVentas.objects.filter(vendedor=vendedor).count()
        for index in range(0, cantidad_sin_convertir):
            ChatDeVentas.nuevo_con_envio_con(
                vendedor=vendedor, token='%s-%s' % (vendedor.pk, cantidad + index), texto='Hello')
        self._asignar_a(vendedor, cantidad=0, cantidad_convertidos=cantidad_convertidos)

    def _crear_ventas_de_chats(self, vendedor, cantidad_de_chats_sin_ventas, cantidad_de_ventas_de_chat):
        self._crear_chats(
            cantidad_convertidos=cantidad_de_chats_sin_ventas, cantidad_sin_convertir=0, vendedor=vendedor)
        self._asignar_a(vendedor=vendedor, cantidad=0, cantidad_convertidos=cantidad_de_ventas_de_chat,
                        cantidad_de_ventas_convertidos=cantidad_de_ventas_de_chat)


@override_settings(CHAT_SELECTOR_PARTICIPANTES_CLASS_NAME=
                   'occ.chat_seleccion_de_participantes.SelectorParticipantesCompulsa')
class CompulsaSeleccionDeParticipantesTest(ParticipantesTest):
    def setUp(self):
        super(CompulsaSeleccionDeParticipantesTest, self).setUp()
        self.gestor_de_vendedores = GestorDeVendedores.nuevo()
        self.supervisor_uno = self.fixture['sup_1']
        self.supervisor_uno.vendedores.all().delete()
        self.supervisor_dos = self.fixture['sup_2']
        self.supervisor_dos.vendedores.all().delete()
        for i in range(0, 5):
            vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
            vendedor._ultima_actividad = timezone.now() - timezone.timedelta(minutes=i)
            vendedor.save()

        self.pedido_ford_json = PedidoBuilder.pedido_de_operador(token='123asd',
                                                                 nombre_campania=self.campania.nombre,
                                                                 marca='Ford')
        self.pedido_de_prospecto_para_ford = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor_uno, campania=self.campania)
        self.creador_de_contexto.crear_filtro_para_marca(
            pedido=self.pedido_de_prospecto_para_ford, nombre_de_marca='Ford')

    def _assert_participantes_seleccionados(self, participantes, indices_de_participantes_esperados):
        vendedores = self.supervisor_uno.vendedores.all()
        vendedores_elegidos = [vendedores[indice] for indice in indices_de_participantes_esperados]
        self.assertEqual(set(participantes), set(vendedores_elegidos))

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_ventana_sin_resto_de_participantes_debe_continuar_con_los_faltantes(self, mock_criterio):
        """
            Tatal de participantes 5, en el primer intento se selecciona a 0
        """
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(self.pedido_ford_json),
                                  tamanio_ventana_vendedores=2)
        # Reitento 1
        self._assert_participantes_seleccionados(
            participantes=compulsa.proximos_vendedores(), indices_de_participantes_esperados=[0, 1])

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_ventana_con_resto_de_participantes_debe_tomar_los_faltantes_y_continuar_con_los_primeros(
            self, mock_criterio):
        """
            Tatal de participantes 5, en el tercer intento se seleccion el ultimo y el primero.

        """
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(self.pedido_ford_json),
                                  tamanio_ventana_vendedores=2)

        # Reitento 1
        self._assert_participantes_seleccionados(
            participantes=compulsa.proximos_vendedores(), indices_de_participantes_esperados=[0, 1])
        # Reitento 2
        self._assert_participantes_seleccionados(
            participantes=compulsa.proximos_vendedores(), indices_de_participantes_esperados=[2, 3])
        # Reitento 3
        self._assert_participantes_seleccionados(
            participantes=compulsa.proximos_vendedores(), indices_de_participantes_esperados=[4, 0])

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_ventana_mayor_a_la_cantidad_de_vendedores_deben_participar_todos(self, mock_criterio):
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(self.pedido_ford_json),
                                  tamanio_ventana_vendedores=8)
        self.assertEqual(set(compulsa.proximos_vendedores()),
                         set(self.supervisor_uno.vendedores.all()))

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_al_eliminar_participante_no_debe_estar_entre_los_proximos_participantes(self, mock_criterio):
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(self.pedido_ford_json),
                                  tamanio_ventana_vendedores=5)

        self.assertEqual(set(compulsa.proximos_vendedores()),
                         set(self.supervisor_uno.vendedores.all()))
        vendedor_a_borrar = self.supervisor_uno.vendedores.first()
        compulsa.eliminar_participante(vendedor_a_borrar)

        participantes = compulsa.proximos_vendedores()
        self.assertNotIn(vendedor_a_borrar, participantes)
        self.assertEqual(len(participantes), self.supervisor_uno.vendedores.count() - 1)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedor_de_otra_marca_no_debe_participar(self, mock_criterio):
        vendedor_toyota = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_dos)
        vendedor_toyota._ultima_actividad = timezone.now()
        vendedor_toyota.save()
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(supervisor=self.supervisor_uno,
                                                                             vendedor=vendedor_toyota,
                                                                             campania=self.campania)
        self.creador_de_contexto.crear_filtro_para_marca(pedido, nombre_de_marca='Toyota')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(self.pedido_ford_json),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertNotIn(vendedor_toyota, participantes)
        self.assertEqual(len(participantes), self.supervisor_uno.vendedores.count())

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedor_con_factor_de_asignacion_cero_no_debe_participar_de_la_compulsa(self, mock_criterio):
        vendedor = self.supervisor_uno.vendedores.all()[0]
        self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=vendedor, factor_de_asignacion=0)
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(self.pedido_ford_json),
                                  tamanio_ventana_vendedores=self.supervisor_uno.vendedores.count())
        self.assertNotIn(vendedor, compulsa.proximos_vendedores())

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedores_con_factor_de_asignacion_mayor_a_cero_deben_ser_participantes_de_la_compulsa(
            self, mock_criterio):
        for vendedor in self.supervisor_uno.vendedores.all():
            self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=vendedor, factor_de_asignacion=10)
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(self.pedido_ford_json),
                                  tamanio_ventana_vendedores=self.supervisor_uno.vendedores.count())
        self.assertEqual(set(compulsa.proximos_vendedores()),
                         set(self.supervisor_uno.vendedores.all()))

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_participacion_para_una_marca_superados_los_reitentos_debe_notificar_excepcion_y_finalizar_compulsa(
            self, mock_criterio):
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(self.pedido_ford_json),
                                  tamanio_ventana_vendedores=5)
        compulsa.cantidad_de_reintentos = Sistema.instance().cantidad_de_reintentos_compulsa
        compulsa.marca_actual = 'Ford'
        compulsa.save()
        self.assertRaises(CantidadDeReintentosSuperadaError, compulsa.proximos_vendedores)
        compulsa = reload_model(compulsa)
        self.assertTrue(compulsa.esta_finalizada_por_timeout())

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedor_con_pedido_para_la_provincia_de_la_compulsa_debe_participar(self, mock_criterio):
        vendedor_de_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(
            self.supervisor_dos, ultima_actividad=timezone.now())

        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            supervisor=self.supervisor_dos, vendedor=vendedor_de_dos, campania=self.campania)

        provincia = 'Tierra del fuego'
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido=pedido, campo='provincia', selector=FiltroDePedido.CONTIENE, valor=provincia)

        pedido_de_compulsa = PedidoBuilder.pedido_de_operador(
            token='123asd', nombre_campania=self.campania.nombre, marca='Toyota', provincia=provincia)

        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_de_compulsa),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertEqual(len(participantes), 1)
        self.assertIn(vendedor_de_dos, participantes)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedor_con_pedido_para_localidad_de_la_compulsa_pero_no_provincia_no_debe_participar(self, mock_criterio):
        vendedor_de_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(
            self.supervisor_dos, ultima_actividad=timezone.now())
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            supervisor=self.supervisor_uno, vendedor=vendedor_de_dos, campania=self.campania)

        localidad = 'Mercedes'
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido=pedido, campo='provincia', selector=FiltroDePedido.CONTIENE, valor='Buenos Aires')
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido=pedido, campo='localidad', selector=FiltroDePedido.CONTIENE, valor=localidad)
        pedido_de_compulsa = PedidoBuilder.pedido_de_operador(
            token='123asd', nombre_campania=self.campania.nombre, marca='Toyota',
            provincia='Corrientes', location=localidad)

        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_de_compulsa),
                                  tamanio_ventana_vendedores=8)

        self.assertRaises(CantidadDeReintentosSuperadaError, compulsa.proximos_vendedores)
        compulsa = reload_model(compulsa)
        self.assertTrue(compulsa.esta_finalizada_por_timeout())


    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedor_con_pedido_para_una_provincia_no_debe_participar_en_compulsa_de_otra_provincia(
            self, mock_criterio):
        vendedor_de_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(
            self.supervisor_dos, ultima_actividad=timezone.now())
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            supervisor=self.supervisor_uno, vendedor=vendedor_de_dos, campania=self.campania)
        self.creador_de_contexto.crear_filtro_para_marca(pedido, nombre_de_marca='Ford')
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido=pedido, campo='provincia', selector=FiltroDePedido.CONTIENE, valor='Tierra del fuego')

        pedido_de_compulsa = PedidoBuilder.pedido_de_operador(
            token='123asd', nombre_campania=self.campania.nombre, marca='Ford', provincia='La pampa')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_de_compulsa),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertNotIn(vendedor_de_dos, participantes)
        self.assertEqual(set(participantes), set(self.supervisor_uno.vendedores.all()))

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_participacion_para_varias_marcas_superado_intetos_de_una_marca_deben_participar_vendedores_de_la_siguiente(
            self, mock_criterio):
        vendedor_toyota = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_dos)
        vendedor_toyota._ultima_actividad = timezone.now()
        vendedor_toyota.save()
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(supervisor=self.supervisor_dos,
                                                                             vendedor=vendedor_toyota,
                                                                             campania=self.campania)
        FiltroDePedido.nuevo_para_inclusion(pedido=pedido, campo='marca',
                                            selector=FiltroDePedido.CONTIENE, valor='Toyota')

        pedido_varias_marcas_json = PedidoBuilder.pedido_de_operador(token='123asd',
                                                                     nombre_campania=self.campania.nombre,
                                                                     marca='Ford|Toyota')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_varias_marcas_json),
                                  tamanio_ventana_vendedores=8)
        sistema = Sistema.instance()
        sistema.cantidad_de_reintentos_compulsa = 1
        sistema.save()

        # Participacion para la siguiente marca: Ford
        participantes = compulsa.proximos_vendedores()
        self.assertEqual(len(participantes), self.supervisor_uno.vendedores.count())
        self.assertNotIn(vendedor_toyota, participantes)

        # Participacion para la siguiente marca: Toyota
        participantes = compulsa.proximos_vendedores()
        self.assertEqual(len(participantes), 1)
        self.assertIn(vendedor_toyota, participantes)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_participacion_para_varias_marcas_superados_intentos_de_todas_debe_notificar_excepcion_y_finalizar_compulsa(
            self, mock_criterio):
        vendedor_toyota = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_dos)
        vendedor_toyota._ultima_actividad = timezone.now()
        vendedor_toyota.save()
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(supervisor=self.supervisor_dos,
                                                                             vendedor=vendedor_toyota,
                                                                             campania=self.campania)
        FiltroDePedido.nuevo_para_inclusion(pedido=pedido, campo='marca',
                                            selector=FiltroDePedido.CONTIENE, valor='Toyota')

        pedido_varias_marcas_json = PedidoBuilder.pedido_de_operador(token='123asd',
                                                                     nombre_campania=self.campania.nombre,
                                                                     marca='Ford|Toyota')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_varias_marcas_json),
                                  tamanio_ventana_vendedores=8)
        sistema = Sistema.instance()
        sistema.cantidad_de_reintentos_compulsa = 1
        sistema.save()

        compulsa.marca_actual = 'Toyota'
        compulsa.save()
        self.assertRaises(CantidadDeReintentosSuperadaError, compulsa.proximos_vendedores)
        compulsa = reload_model(compulsa)
        self.assertTrue(compulsa.esta_finalizada_por_timeout())

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_criterio_por_conversiones_deben__debe_priorizar_participaciones_de_aquellos_con_mas_prospectos_generados(
            self, mock_criterio):
        vendedores = self.supervisor_uno.vendedores.all()
        # Vendedor cero no tiene chats, se le da factor 1 por defecto
        # Vendedor 1, factor 1 = 6 de 6
        self._crear_chats(vendedor=vendedores[1], cantidad_sin_convertir=0, cantidad_convertidos=6)
        # Vendedor 3, factor 0.6 = 6 de 9
        self._crear_chats(vendedor=vendedores[3], cantidad_sin_convertir=3, cantidad_convertidos=6)
        # Vendedor 2, factor 0.3 = 3 de 9
        self._crear_chats(vendedor=vendedores[2], cantidad_sin_convertir=6, cantidad_convertidos=3)
        # Vendedor 4, factor 0.25 = 3 de 12
        self._crear_chats(vendedor=vendedores[4], cantidad_sin_convertir=9, cantidad_convertidos=3)

        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(self.pedido_ford_json),
                                  tamanio_ventana_vendedores=2)
        self._assert_participantes_seleccionados(  # Reintento 1
            participantes=compulsa.proximos_vendedores(), indices_de_participantes_esperados=[0, 1])
        self._assert_participantes_seleccionados(  # Reintento 2
            participantes=compulsa.proximos_vendedores(), indices_de_participantes_esperados=[3, 2])
        self._assert_participantes_seleccionados(  # Reintento 3
            participantes=compulsa.proximos_vendedores(), indices_de_participantes_esperados=[4, 0])

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_criterio_por_ventas_debe_priorizar_participaciones_de_aquellos_que_mas_prospectos_vendieron(
            self, mock_criterio):
        vendedores = self.supervisor_uno.vendedores.all()
        # Vendedor cero no tiene chats, se le da factor 1 por defecto
        # Vendedor 3, factor 1 = 6 de 6
        self._crear_ventas_de_chats(
            vendedor=vendedores[3], cantidad_de_chats_sin_ventas=0, cantidad_de_ventas_de_chat=6)
        # Vendedor 1, factor 0.6 = 6 de 9
        self._crear_ventas_de_chats(
            vendedor=vendedores[1], cantidad_de_chats_sin_ventas=3, cantidad_de_ventas_de_chat=6)
        # Vendedor 4, factor 0.3 = 3 de 9
        self._crear_ventas_de_chats(
            vendedor=vendedores[4], cantidad_de_chats_sin_ventas=6, cantidad_de_ventas_de_chat=3)
        # Vendedor 2, factor 0.25 = 3 de 12
        self._crear_ventas_de_chats(
            vendedor=vendedores[2], cantidad_de_chats_sin_ventas=9, cantidad_de_ventas_de_chat=3)

        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(self.pedido_ford_json),
                                  tamanio_ventana_vendedores=2)
        self._assert_participantes_seleccionados(  # Reintento 1
            participantes=compulsa.proximos_vendedores(), indices_de_participantes_esperados=[0, 3])
        self._assert_participantes_seleccionados(  # Reintento 2
            participantes=compulsa.proximos_vendedores(), indices_de_participantes_esperados=[1, 4])
        self._assert_participantes_seleccionados(  # Reintento 3
            participantes=compulsa.proximos_vendedores(), indices_de_participantes_esperados=[2, 0])

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedores_de_pedidos_con_filtro_por_un_telefono_diferente_no_debe_participar(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_dos)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(supervisor=self.supervisor_dos,
                                                                             vendedor=vendedor,
                                                                             campania=self.campania)

        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido,'telefono', selector=FiltroDePedido.PREFIJO,
                                                                valor='54911')

        pedido_telefono = PedidoBuilder.pedido_de_operador(token='123asd',
                                                                 nombre_campania=self.campania.nombre,
                                                                 marca='Ford',
                                                           telefono='549232' + '22334455')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_telefono),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertNotIn(vendedor, participantes)
        self.assertEqual(len(participantes), self.supervisor_uno.vendedores_a_cargo().count())

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedores_de_pedidos_con_filtro_por_un_prefijo_diferente_no_debe_participar(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_dos)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(supervisor=self.supervisor_dos,
                                                                             vendedor=vendedor,
                                                                             campania=self.campania)

        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido,'prefijo', selector=FiltroDePedido.PREFIJO,
                                                                valor='011')

        pedido_telefono = PedidoBuilder.pedido_de_operador(token='123asd',
                                                                 nombre_campania=self.campania.nombre,
                                                                 marca='Ford',
                                                           prefijo='0232')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_telefono),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertNotIn(vendedor, participantes)
        self.assertEqual(len(participantes), self.supervisor_uno.vendedores_a_cargo().count())


    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedores_de_pedidos_con_filtro_por_un_modelo_diferente_no_debe_participar(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_dos)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(supervisor=self.supervisor_dos,
                                                                             vendedor=vendedor,
                                                                             campania=self.campania)

        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido,'modelo', selector=FiltroDePedido.CONTIENE,
                                                                valor='Ranger')

        pedido_telefono = PedidoBuilder.pedido_de_operador(token='123asd',
                                                                 nombre_campania=self.campania.nombre,
                                                                 marca='Ford',
                                                           modelo='Fiesta')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_telefono),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertNotIn(vendedor, participantes)
        self.assertEqual(len(participantes), self.supervisor_uno.vendedores_a_cargo().count())

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedores_de_pedidos_con_filtro_por_un_modelo_diferente_no_debe_participar(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_dos)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(supervisor=self.supervisor_dos,
                                                                             vendedor=vendedor,
                                                                             campania=self.campania)

        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido,'modelo', selector=FiltroDePedido.CONTIENE,
                                                                valor='Ranger')

        pedido_telefono = PedidoBuilder.pedido_de_operador(token='123asd',
                                                                 nombre_campania=self.campania.nombre,
                                                                 marca='Ford',
                                                           modelo='Fiesta|Ranger')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_telefono),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertIn(vendedor, participantes)
        self.assertEqual(len(participantes), self.supervisor_uno.vendedores_a_cargo().count() + 1)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedores_de_pedidos_con_varios_filtros_debe_cumplir_todos_los_filtros(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_dos)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(supervisor=self.supervisor_dos,
                                                                             vendedor=vendedor,
                                                                             campania=self.campania)


        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido,'prefijo', selector=FiltroDePedido.CONTIENE,
                                                                valor='0232')
        self.creador_de_contexto.crear_filtro_de_inclusion_para(pedido,'modelo', selector=FiltroDePedido.CONTIENE,
                                                                valor='Ranger')

        pedido_telefono = PedidoBuilder.pedido_de_operador(token='123asd',
                                                                 nombre_campania=self.campania.nombre,
                                                                 marca='Ford',
                                                           modelo='Fiesta|Ranger', prefijo='011')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_telefono),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertNotIn(vendedor, participantes)
        self.assertEqual(len(participantes), self.supervisor_uno.vendedores_a_cargo().count())

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_compulsa_sin_modelo_no_debe_participar_si_filtra_por_modelo(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()

        self.creador_de_contexto.crear_filtro_de_inclusion_para(self.pedido_de_prospecto_para_ford, 'modelo',
                                                                selector=FiltroDePedido.CONTIENE,
                                                                valor='Ranger')

        pedido_compulsa = PedidoBuilder.pedido_de_operador(token='123asd',
                                                           nombre_campania=self.campania.nombre,
                                                           marca='Ford')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_compulsa),
                                  tamanio_ventana_vendedores=8)

        # Como no encontro ningun participante, se lanza la excepcion de reintentos superados
        self.assertRaises(CantidadDeReintentosSuperadaError, compulsa.proximos_vendedores)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_compulsa_que_excluye_no_debe_participar(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()

        self.creador_de_contexto.crear_filtro_de_exclusion_para(self.pedido_de_prospecto_para_ford, 'modelo',
                                                                selector=FiltroDePedido.CONTIENE,
                                                                valor='Ranger')

        pedido_compulsa = PedidoBuilder.pedido_de_operador(token='123asd',
                                                           nombre_campania=self.campania.nombre,
                                                           marca='Ford',
                                                           modelo="Ranger")
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_compulsa),
                                  tamanio_ventana_vendedores=8)

        # Como no encontro ningun participante, se lanza la excepcion de reintentos superados
        self.assertRaises(CantidadDeReintentosSuperadaError, compulsa.proximos_vendedores)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_compulsa_que_excluye_modelo_debe_participar_si_no_contiene_modelo(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()

        self.creador_de_contexto.crear_filtro_de_exclusion_para(self.pedido_de_prospecto_para_ford, 'modelo',
                                                                selector=FiltroDePedido.CONTIENE,
                                                                valor='Ranger')

        pedido_compulsa = PedidoBuilder.pedido_de_operador(token='123asd',
                                                           nombre_campania=self.campania.nombre,
                                                           marca='Ford')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_compulsa),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertIn(vendedor, participantes)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_compulsa_que_excluye_modelo_no_debe_participar_si_contiene_y_excluye_modelo(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()

        self.creador_de_contexto.crear_filtro_de_exclusion_para(self.pedido_de_prospecto_para_ford, 'modelo',
                                                                selector=FiltroDePedido.CONTIENE,
                                                                valor='xlt')

        self.creador_de_contexto.crear_filtro_de_inclusion_para(self.pedido_de_prospecto_para_ford, 'modelo',
                                                                selector=FiltroDePedido.CONTIENE,
                                                                valor='Ranger')

        pedido_compulsa = PedidoBuilder.pedido_de_operador(token='123asd',
                                                           nombre_campania=self.campania.nombre,
                                                           marca='Ford',
                                                           modelo='Ranger XLT')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_compulsa),
                                  tamanio_ventana_vendedores=8)

        self.assertRaises(CantidadDeReintentosSuperadaError, compulsa.proximos_vendedores)


    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_compulsa_que_excluye_provincia_debe_participar_si_no_contiene_provincia(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()

        self.creador_de_contexto.crear_filtro_de_exclusion_para(self.pedido_de_prospecto_para_ford, 'provincia',
                                                                selector=FiltroDePedido.CONTIENE,
                                                                valor='Buenos Aires')

        pedido_compulsa = PedidoBuilder.pedido_de_operador(token='123asd',
                                                           nombre_campania=self.campania.nombre,
                                                           marca='Ford')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_compulsa),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertIn(vendedor, participantes)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_compulsa_que_excluye_provincia_no_debe_participar_si_contiene_y_excluye_provincia(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()

        self.creador_de_contexto.crear_filtro_de_exclusion_para(self.pedido_de_prospecto_para_ford, 'provincia',
                                                                selector=FiltroDePedido.CONTIENE,
                                                                valor='aires')

        self.creador_de_contexto.crear_filtro_de_inclusion_para(self.pedido_de_prospecto_para_ford, 'modelo',
                                                                selector=FiltroDePedido.CONTIENE,
                                                                valor='buenos')

        pedido_compulsa = PedidoBuilder.pedido_de_operador(token='123asd',
                                                           nombre_campania=self.campania.nombre,
                                                           marca='Ford',
                                                           provincia='Buenos Aires')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_compulsa),
                                  tamanio_ventana_vendedores=8)

        self.assertRaises(CantidadDeReintentosSuperadaError, compulsa.proximos_vendedores)


    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_compulsa_que_excluye_localidad_debe_participar_si_no_contiene_localidad(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()

        self.creador_de_contexto.crear_filtro_de_exclusion_para(self.pedido_de_prospecto_para_ford, 'localidad',
                                                                selector=FiltroDePedido.CONTIENE,
                                                                valor='santa teresita')

        pedido_compulsa = PedidoBuilder.pedido_de_operador(token='123asd',
                                                           nombre_campania=self.campania.nombre,
                                                           marca='Ford')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_compulsa),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertIn(vendedor, participantes)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_compulsa_que_excluye_localidad_no_debe_participar_si_contiene_y_excluye_localidad(self, mock_criterio):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor._ultima_actividad = timezone.now()
        vendedor.save()

        self.creador_de_contexto.crear_filtro_de_exclusion_para(self.pedido_de_prospecto_para_ford, 'localidad',
                                                                selector=FiltroDePedido.CONTIENE,
                                                                valor='teresita')

        self.creador_de_contexto.crear_filtro_de_inclusion_para(self.pedido_de_prospecto_para_ford, 'localidad',
                                                                selector=FiltroDePedido.CONTIENE,
                                                                valor='santa')

        pedido_compulsa = PedidoBuilder.pedido_de_operador(token='123asd',
                                                           nombre_campania=self.campania.nombre,
                                                           marca='Ford',
                                                           localidad='Santa Teresita')
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_compulsa),
                                  tamanio_ventana_vendedores=8)

        self.assertRaises(CantidadDeReintentosSuperadaError, compulsa.proximos_vendedores)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedor_con_pedido_para_prefijo_debe_aceptar_prefijos_formados_con_un_ceros(self, mock_criterio):
        vendedor_de_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(
            self.supervisor_dos, ultima_actividad=timezone.now())

        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            supervisor=self.supervisor_dos, vendedor=vendedor_de_dos, campania=self.campania)

        prefijo = '11'
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido=pedido, campo='prefijo', selector=FiltroDePedido.PREFIJO, valor=prefijo)

        pedido_de_compulsa = PedidoBuilder.pedido_de_operador(
            token='123asd', nombre_campania=self.campania.nombre, marca='Toyota', prefijo='011')

        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_de_compulsa),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertEqual(len(participantes), 1)
        self.assertIn(vendedor_de_dos, participantes)


    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedor_con_pedido_para_prefijo_debe_aceptar_prefijos_formados_con_varios_ceros(self, mock_criterio):
        vendedor_de_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(
            self.supervisor_dos, ultima_actividad=timezone.now())

        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            supervisor=self.supervisor_dos, vendedor=vendedor_de_dos, campania=self.campania)

        prefijo = '11'
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido=pedido, campo='prefijo', selector=FiltroDePedido.PREFIJO, valor=prefijo)

        pedido_de_compulsa = PedidoBuilder.pedido_de_operador(
            token='123asd', nombre_campania=self.campania.nombre, marca='Toyota', prefijo='00011')

        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_de_compulsa),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertEqual(len(participantes), 1)
        self.assertIn(vendedor_de_dos, participantes)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedores_con_pedido_con_filtro_participan_cuando_no_hay_prefijo_y_el_telefono_cumple_con_el_filtro_de_prefijo_con_selector_contiene(self, mock_criterio):
        vendedor_de_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(
            self.supervisor_dos, ultima_actividad=timezone.now())

        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            supervisor=self.supervisor_dos, vendedor=vendedor_de_dos, campania=self.campania)

        prefijo = '11'
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido=pedido, campo='prefijo', selector=FiltroDePedido.CONTIENE, valor=prefijo)

        pedido_de_compulsa = PedidoBuilder.pedido_de_operador(
            token='123asd', nombre_campania=self.campania.nombre, marca='Toyota', telefono='005491122334455')

        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_de_compulsa),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertEqual(len(participantes), 1)
        self.assertIn(vendedor_de_dos, participantes)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedores_con_pedido_con_filtro_participan_cuando_no_hay_prefijo_y_el_telefono_cumple_con_el_filtro_prefijo_con_selector_de_comienza_con(self, mock_criterio):
        vendedor_de_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(
            self.supervisor_dos, ultima_actividad=timezone.now())

        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            supervisor=self.supervisor_dos, vendedor=vendedor_de_dos, campania=self.campania)

        prefijo = '11'
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido=pedido, campo='prefijo', selector=FiltroDePedido.PREFIJO, valor=prefijo)

        pedido_de_compulsa = PedidoBuilder.pedido_de_operador(
            token='123asd', nombre_campania=self.campania.nombre, marca='Toyota', telefono='005491122334455')

        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_de_compulsa),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertEqual(len(participantes), 1)
        self.assertIn(vendedor_de_dos, participantes)

    @mock.patch('occ.chat_seleccion_de_participantes.FlujoDeParticipacionDeCompulsa._selector_de_criterios',
                return_value=SelectorDeCriteriosDeParticipantesStup.por_ventas())
    def test_vendedores_con_pedido_con_filtro_participan_cuando_no_hay_prefijo_y_utiliza_el_telefono_ignora_caracteres_especiales_y_ceros_a_izquierda(self, mock_criterio):
        vendedor_de_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(
            self.supervisor_dos, ultima_actividad=timezone.now())

        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            supervisor=self.supervisor_dos, vendedor=vendedor_de_dos, campania=self.campania)

        prefijo = '11'
        self.creador_de_contexto.crear_filtro_de_inclusion_para(
            pedido=pedido, campo='prefijo', selector=FiltroDePedido.PREFIJO, valor=prefijo)

        pedido_de_compulsa = PedidoBuilder.pedido_de_operador(
            token='123asd', nombre_campania=self.campania.nombre, marca='Toyota', telefono='+54 9 0011-2233-4455')

        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_de_compulsa),
                                  tamanio_ventana_vendedores=8)

        participantes = compulsa.proximos_vendedores()
        self.assertEqual(len(participantes), 1)
        self.assertIn(vendedor_de_dos, participantes)


@override_settings(CHAT_SELECTOR_PARTICIPANTES_CLASS_NAME=
                   'occ.chat_seleccion_de_participantes.SelectorParticipantesCompulsa')
class SelectorParticipantesCompulsaTest(ParticipantesTest):
    def una_compulsa_con_muchos_vendedores(self):
        supervisor, vendedores = self.supervisor_y_muchos_vendedores()
        return self.crear_pedido_de_prospectos_y_compulsa_para(supervisor)

    def crear_pedido_de_prospectos_y_compulsa_para(self, supervisor):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=supervisor,
                                                                                   campania=self.campania)

        pedido_json = PedidoBuilder.pedido_de_operador(token='123asd', nombre_campania=self.campania.nombre,
                                                       marca='Ford')
        cantidad_de_vendedores_por_intento = Sistema.instance().cantidad_de_participantes_compulsa
        compulsa = Compulsa.nueva(campania=self.campania,
                                  pedido=PedidoDeOperador.nuevo_para(pedido_json),
                                  tamanio_ventana_vendedores=cantidad_de_vendedores_por_intento)
        return compulsa, pedido

    def _vendedor_con_ultima_actividad_en(self, vendedores):
        mejor_vendedor = min(vendedores, key=lambda x: x._ultima_actividad)
        return mejor_vendedor

    def supervisor_y_vendedores_con_prospectos(self, cantidad, limite_de_datos_diarios_en_pedidos=None):
        vendedores = []
        concesionaria = ConcesionariasFactory(nombre='conce', dia_inicio_periodos=28, dia_fin_periodos=20)
        supervisor = self.creador_de_contexto.nuevo_supervisor(concesionaria)

        for i in range(0, cantidad):
            vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(
                supervisor, limite_de_datos_diarios_en_pedidos=limite_de_datos_diarios_en_pedidos)
            vendedor._ultima_actividad = timezone.now()
            vendedor.save()
            self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor, tiempo_de_respuesta=i)
            vendedores.append(vendedor)
        return supervisor, vendedores

    def supervisor_y_muchos_vendedores(self):
        return self.supervisor_y_vendedores_con_prospectos(Sistema.instance().cantidad_de_participantes_compulsa + 5)

    def supervisor_y_pocos_vendedores(self):
        return self.supervisor_y_vendedores_con_prospectos(Sistema.instance().cantidad_de_participantes_compulsa - 5)

    def _registrar_actividad_a_todos(self, vendedores, minutos=0):
        ultima_actividad = timezone.now() + timedelta(minutes=minutos)
        with freeze_time(ultima_actividad):
            for vendedor in vendedores:
                ControladorDeActividad().registrar_actividad(vendedor.user)

    def _configurar_compulsa_fuera_de_tiempo_de_validez(self, compulsa):
        self._configurar_fecha_de_compulsa_segun_tiempo_de_validez(compulsa, segundos=2)

    def _configurar_compulsa_dentro_de_tiempo_de_validez(self, compulsa):
        self._configurar_fecha_de_compulsa_segun_tiempo_de_validez(compulsa, segundos=-2)

    def _configurar_fecha_de_compulsa_segun_tiempo_de_validez(self, compulsa, segundos):
        tiempo = Compulsa.tiempo_de_validez()
        compulsa.fecha = timezone.now() - timezone.timedelta(seconds=tiempo + segundos)
        compulsa.save()

    def _configurar_exceso_de_datos_diarios_a_vendedor(self, vendedor):
        vendedor.limite_de_datos_diarios_en_pedidos = 0
        vendedor.save()

    def _ranking_de_conversiones_de_chat_segun(self, vendedores, cantidad_participantes):
        """
            Al primero: 7 chats, 6 convertidos
            Al segundo: 10 chats, 6 convertidos
            Al tercero: 10 chats, 3 convertidos
            Al resto de los vendedores 1 chat sin convertir
        """
        primero = vendedores[cantidad_participantes + 1]
        segundo = vendedores[cantidad_participantes + 2]
        tercero = vendedores[cantidad_participantes + 3]

        for vendedor in vendedores:
            self._crear_chats(vendedor=vendedor, cantidad_sin_convertir=1, cantidad_convertidos=0)
        self._crear_chats(vendedor=primero, cantidad_sin_convertir=0, cantidad_convertidos=6)
        self._crear_chats(vendedor=segundo, cantidad_sin_convertir=3, cantidad_convertidos=6)
        self._crear_chats(vendedor=tercero, cantidad_sin_convertir=6, cantidad_convertidos=3)

        return primero, segundo, tercero

    def _ranking_ventas_de_chat_segun(self, vendedores, cantidad_participantes):
        """
            Al primero: 6 chats, 6 vendidos
            Al segundo: 9 chats, 6 vendidos
            Al tercero: 9 chats, 3 vendidos
            Al resto de los vendedores 1 chat sin convertir
        """

        primero = vendedores[cantidad_participantes + 1]
        segundo = vendedores[cantidad_participantes + 2]
        tercero = vendedores[cantidad_participantes + 3]

        for vendedor in vendedores:
            self._crear_chats(vendedor=vendedor, cantidad_sin_convertir=1, cantidad_convertidos=0)

        self._crear_ventas_de_chats(
            vendedor=tercero, cantidad_de_chats_sin_ventas=6, cantidad_de_ventas_de_chat=3)
        self._crear_ventas_de_chats(
            vendedor=segundo, cantidad_de_chats_sin_ventas=3, cantidad_de_ventas_de_chat=6)

        self._crear_ventas_de_chats(
            vendedor=primero, cantidad_de_chats_sin_ventas=0, cantidad_de_ventas_de_chat=6)

        return primero, segundo, tercero

    def _assert_posiciones(self, primero, segundo, vendedores, tercero=None, ultimo=None):
        self.assertEqual(vendedores[0], primero)
        self.assertEqual(vendedores[1], segundo)

        if tercero is not None:
            self.assertEqual(vendedores[2], tercero)

        if ultimo is not None:
            self.assertEqual(
                vendedores[len(vendedores) - 1], ultimo,
                'el vendedor con limite diario superado debe estar al final de la lista')

    def test_cantidad_de_vendedores_que_aplican_no_supera_la_cantidad_maxima_de_participantes_debe_seleccionar_a_todos(
            self):
        # Dado
        supervisor, vendedores = self.supervisor_y_pocos_vendedores()
        self._registrar_actividad_a_todos(vendedores)
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=supervisor)
        selector = SelectorParticipantesCompulsa(
            selector_de_criterios=SelectorDeCriteriosDeParticipantesStup.por_ultima_actividad(),
            administrador=AdministradorDePedidosStub(pedidos=[pedido]))

        # Cuando
        vendedores_seleccionados = selector.seleccionar_participantes_para(campania=self.campania)

        # Entonces
        self.assertEqual(set(vendedores_seleccionados), set(vendedores))

    def test_participante_de_otra_compulsa_vigente_no_debe_ser_seleccionado(self):
        """
            Para cualquier criterio, vendedores que participando en otro compulsa no son seleccionados
        """
        # Dado
        supervisor, vendedores = self.supervisor_y_muchos_vendedores()
        compulsa, pedido = self.crear_pedido_de_prospectos_y_compulsa_para(supervisor)
        mejor_vendedor = self._vendedor_con_ultima_actividad_en(vendedores)
        self._registrar_actividad_a_todos([mejor_vendedor])
        compulsa.proximos_vendedores()

        selector = SelectorParticipantesCompulsa(
            selector_de_criterios=SelectorDeCriteriosDeParticipantesStup.por_ultima_actividad(),
            administrador=AdministradorDePedidosStub(pedidos=[pedido]))

        # Cuando
        vendedores_seleccionados = selector.seleccionar_participantes_para(campania=self.campania)

        # Entonces
        self.assertNotIn(mejor_vendedor, vendedores_seleccionados)

    def test_participante_de_otra_compulsa_no_finalizada_y_no_vigente_debe_ser_seleccionado(self):
        """
            Para cualquier criterio, vendedores que participando en otro compulsa no vigente (que expiro)
            son seleccionados
        """
        # Dado
        compulsa, pedido = self.una_compulsa_con_muchos_vendedores()
        participantes = compulsa.proximos_vendedores()
        mejor_vendedor = self._vendedor_con_ultima_actividad_en(participantes)
        self._registrar_actividad_a_todos([mejor_vendedor])
        self._configurar_compulsa_fuera_de_tiempo_de_validez(compulsa)

        selector = SelectorParticipantesCompulsa(
            selector_de_criterios=SelectorDeCriteriosDeParticipantesStup.por_ultima_actividad(),
            administrador=AdministradorDePedidosStub(pedidos=[pedido]))

        # Cuando
        vendedores_seleccionados = selector.seleccionar_participantes_para(campania=self.campania)

        # Entonces
        self.assertIn(mejor_vendedor, vendedores_seleccionados)

    def test_participantes_de_otra_compulsa_finalizada_debe_ser_seleccionado(self):
        """
            Para cualquier criterio, vendedores que participando en otro compulsa, pero ya finalizada son seleccionados
        """
        # Dado
        compulsa, pedido = self.una_compulsa_con_muchos_vendedores()
        participantes = compulsa.proximos_vendedores()
        mejor_vendedor = self._vendedor_con_ultima_actividad_en(participantes)
        self._registrar_actividad_a_todos([mejor_vendedor])
        self._configurar_compulsa_dentro_de_tiempo_de_validez(compulsa)
        compulsa.finalizar_por_timeout()

        selector = SelectorParticipantesCompulsa(
            selector_de_criterios=SelectorDeCriteriosDeParticipantesStup.por_ultima_actividad(),
            administrador=AdministradorDePedidosStub(pedidos=[pedido]))

        # Cuando
        vendedores_seleccionados = selector.seleccionar_participantes_para(campania=self.campania)

        # Entonces
        self.assertIn(mejor_vendedor, vendedores_seleccionados)

    def test_vendedores_sin_actividad_reciente_no_deben_ser_seleccionados(self):
        """
            Para cualquier criterio, vendedores que "no esta utilizando el sistema", es decir no tienen actividad
            reciente, no son seleccionados
        """
        # Dado
        supervisor, vendedores = self.supervisor_y_pocos_vendedores()
        self._registrar_actividad_a_todos(vendedores, minutos=-35)
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=supervisor)
        selector = SelectorParticipantesCompulsa(
            selector_de_criterios=SelectorDeCriteriosDeParticipantesStup.por_conversiones(),
            administrador=AdministradorDePedidosStub(pedidos=[pedido]))

        # Cuando
        vendedores_seleccionados = selector.seleccionar_participantes_para(campania=self.campania)

        # Entonces
        self.assertEqual(len(vendedores_seleccionados), 0)

    def test_con_criterio_por_conversiones_vendedores_con_mas_conversiones_deben_ser_priorizados(self):
        # Dado
        cantidad_participantes = Sistema.instance().cantidad_de_participantes_compulsa
        supervisor, vendedores = self.supervisor_y_vendedores_con_prospectos(cantidad_participantes + 5)
        self._registrar_actividad_a_todos(vendedores)

        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=supervisor)
        selector = SelectorParticipantesCompulsa(
            selector_de_criterios=SelectorDeCriteriosDeParticipantesStup.por_conversiones(),
            administrador=AdministradorDePedidosStub(pedidos=[pedido]))
        # Cuando
        vendedores_seleccionados = selector.seleccionar_participantes_para(campania=self.campania)

        # Entonces
        primero, segundo, tercero = self._ranking_de_conversiones_de_chat_segun(vendedores, cantidad_participantes)
        self._assert_posiciones(primero=primero, segundo=segundo, tercero=tercero, vendedores=vendedores_seleccionados)

    def test_con_criterio_por_ventas_vendedores_con_mas_ventas_desde_chat_deberia_ser_seleccionados(self):
        # Dado
        cantidad_participantes = Sistema.instance().cantidad_de_participantes_compulsa
        supervisor, vendedores = self.supervisor_y_vendedores_con_prospectos(cantidad_participantes + 5)
        self._registrar_actividad_a_todos(vendedores)

        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=supervisor)
        selector = SelectorParticipantesCompulsa(
            selector_de_criterios=SelectorDeCriteriosDeParticipantesStup.por_ventas(),
            administrador=AdministradorDePedidosStub(pedidos=[pedido]))

        # Cuando
        vendedores_seleccionados = selector.seleccionar_participantes_para(campania=self.campania)

        # Entonces
        primero, segundo, tercero = self._ranking_ventas_de_chat_segun(vendedores, cantidad_participantes)
        self._assert_posiciones(primero=primero, segundo=segundo, tercero=tercero, vendedores=vendedores_seleccionados)

    def test_por_conversiones_vendedor_mejor_rankeado_pero_con_limite_diario_superado_debe_ser_enviado_al_final(
            self):
        # Dado
        cantidad_participantes = Sistema.instance().cantidad_de_participantes_compulsa
        supervisor, vendedores = self.supervisor_y_vendedores_con_prospectos(
            cantidad_participantes + 5, limite_de_datos_diarios_en_pedidos=10)
        self._registrar_actividad_a_todos(vendedores)

        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=supervisor)
        selector = SelectorParticipantesCompulsa(
            selector_de_criterios=SelectorDeCriteriosDeParticipantesStup.por_conversiones(),
            administrador=AdministradorDePedidosStub(pedidos=[pedido]))

        primero, segundo, tercero = self._ranking_de_conversiones_de_chat_segun(vendedores, cantidad_participantes)
        self._configurar_exceso_de_datos_diarios_a_vendedor(primero)

        # Cuando
        vendedores_seleccionados = selector.seleccionar_participantes_para(campania=self.campania)

        # Entonces
        self._assert_posiciones(primero=segundo, segundo=tercero, ultimo=primero, vendedores=vendedores_seleccionados)

    def test_por_ventas_vendedor_mejor_rankeado_pero_con_limite_diario_superado_debe_ser_enviado_al_final(
            self):
        # Dado
        cantidad_participantes = Sistema.instance().cantidad_de_participantes_compulsa
        supervisor, vendedores = self.supervisor_y_vendedores_con_prospectos(
            cantidad_participantes + 5, limite_de_datos_diarios_en_pedidos=20)
        self._registrar_actividad_a_todos(vendedores)

        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=supervisor)
        selector = SelectorParticipantesCompulsa(
            selector_de_criterios=SelectorDeCriteriosDeParticipantesStup.por_ventas(),
            administrador=AdministradorDePedidosStub(pedidos=[pedido]))

        primero, segundo, tercero = self._ranking_ventas_de_chat_segun(vendedores, cantidad_participantes)
        self._configurar_exceso_de_datos_diarios_a_vendedor(primero)

        # Cuando
        vendedores_seleccionados = selector.seleccionar_participantes_para(campania=self.campania)

        # Entonces
        self._assert_posiciones(primero=segundo, segundo=tercero, ultimo=primero, vendedores=vendedores_seleccionados)
