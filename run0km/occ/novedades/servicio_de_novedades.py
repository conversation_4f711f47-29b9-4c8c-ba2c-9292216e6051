from datetime import timed<PERSON><PERSON>

from django.utils.timezone import now

from core.models import Sistema
from occ.models.novedad import Novedad, VisualizacionesDeNovedadesPorUsuario


class ServicioDeNovedades(object):
    def __init__(self):
        super(ServicioDeNovedades, self).__init__()

    def novedades_para(self, usuario):
        if usuario.is_gerente():
            novedades = Novedad.objects.para_gerentes()
        elif usuario.is_supervisor():
            novedades = Novedad.objects.para_supervisores()
        elif usuario.is_vendedor():
            novedades = Novedad.objects.para_vendedores()
        else:
            novedades = Novedad.objects.none()

        novedades = novedades.ordenar_por_mas_reciente()
        return novedades

    def ultima_novedad_para(self, usuario):
        novedades = self.novedades_para(usuario)
        novedad = novedades.ordenar_por_mas_reciente().first()
        return novedad

    def debe_mostrar_novedades_para(self, usuario):
        if not self.puede_ver(usuario):
            return False

        sistema = Sistema.instance()
        cantidad_de_veces_a_mostrar_novedades = sistema.obtener_cantidad_de_veces_a_mostrar_novedades()
        frecuencia_para_mostrar_novedades = sistema.obtener_frecuencia_para_mostrar_novedades()
        visualizaciones_de_novedades = self.obtener_visualizaciones_de_novedades_de(usuario)

        cantidad_de_visualizaciones = visualizaciones_de_novedades.cantidad()
        fecha_de_ult_visualizacion = visualizaciones_de_novedades.fecha_de_ultima_visualizacion()
        fecha_de_hoy = now()

        ya_vio_suficientes_novedades = cantidad_de_visualizaciones >= cantidad_de_veces_a_mostrar_novedades
        ya_vio_las_novedades_dentro_de_la_frecuencia_configurada = \
            fecha_de_ult_visualizacion and \
            (fecha_de_ult_visualizacion + timedelta(days=frecuencia_para_mostrar_novedades)) >= fecha_de_hoy.date()

        if ya_vio_suficientes_novedades or ya_vio_las_novedades_dentro_de_la_frecuencia_configurada:
            return False

        return True

    def visualizar_novedad(self, usuario):
        visualizaciones_de_novedades = self.obtener_visualizaciones_de_novedades_de(usuario)
        visualizaciones_de_novedades.sumar_una_visualizacion()

    def obtener_visualizaciones_de_novedades_de(self, usuario):
        if not hasattr(usuario, "visualizaciones_de_novedades"):
            return VisualizacionesDeNovedadesPorUsuario.nuevo_para(usuario)
        else:
            return usuario.visualizaciones_de_novedades

    def novedad_activada(self, novedad):
        if novedad.para_gerentes():
            VisualizacionesDeNovedadesPorUsuario.objects.de_gerentes().resetear()
        elif novedad.para_supervisores():
            VisualizacionesDeNovedadesPorUsuario.objects.de_supervisores().resetear()
        elif novedad.para_vendedores():
            VisualizacionesDeNovedadesPorUsuario.objects.de_vendedores().resetear()
        else:
            pass

    def puede_ver(self, usuario):
        return usuario.is_authenticated() and not usuario.pertenece_a_concesionaria_con_sitio_propio()
