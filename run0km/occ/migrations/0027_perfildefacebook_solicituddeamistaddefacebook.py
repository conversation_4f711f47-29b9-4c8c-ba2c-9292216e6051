# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2017-07-26 00:15


from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('prospectos', '0094_auto_20170612_1734'),
        ('occ', '0026_remove_enviodemensaje_campania'),
    ]

    operations = [
        migrations.CreateModel(
            name='PerfilDeFacebook',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('foto', models.URLField(blank=True, max_length=140, null=True)),
                ('url', models.URLField(max_length=140)),
                ('nombre', models.TextField(blank=True, max_length=100, null=True)),
                ('prospecto', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='perfil_de_facebook', to='prospectos.Prospecto')),
            ],
        ),
        migrations.CreateModel(
            name='SolicitudDeAmistadDeFacebook',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_del_receptor', models.CharField(max_length=64)),
                ('estado', models.CharField(choices=[(b'P', b'Pendiente'), (b'F', b'Fallida'), (b'E', b'Exitosa')], default=b'P', max_length=1)),
                ('fecha', models.DateTimeField(auto_now_add=True)),
                ('marca', models.CharField(default=b'', max_length=256)),
                ('transaction_id', models.CharField(db_index=True, max_length=100, unique=True)),
                ('registro_id', models.CharField(blank=True, max_length=100, null=True)),
                ('cuenta_utilizada', models.CharField(max_length=100)),
                ('prospecto', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='solicitud_de_amistad', to='prospectos.Prospecto')),
            ],
        ),
    ]
