{% extends "occ_configuracion_layout.html" %}

{% block css %}
    {{ block.super }}
    <link href="{{ STATIC_URL }}css/occ-configuracion-popup.css" type="text/css" rel="stylesheet">
{% endblock %}

{% block js %}
    {{ block.super }}
    <script type="text/javascript" src="{{ STATIC_URL }}js/csrf_token.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/system_unavailable.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/occ-configuracion.js"></script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/validar_caracteres.js"></script>
    <script type="text/javascript">
        var cambiar_mensaje_bienvenida_url = "{% url 'mensaje_bienvenida'%}";
        var cambiar_mensaje_incontactables_url = "{% url 'mensaje_incontactables'%}";

        $(document).ready(function () {
            var configuration = OCCConfiguration();
            configuration.addButton(
                'chat', 'spin_chat', {{chat_habilitado|yesno:"true,false"}}, "{% url 'occ-chat' %}");
            configuration.addButtonWithDependents(
                'notificaciones', 'spin_notificaciones',
                {{notificaciones_habilitadas|yesno:"true,false"}}, "{% url 'occ-notificaciones' %}",
                [$("#link-datos-de-contacto")]);
            configuration.addButtonWithDependents(
                'mensajeBienvenida', 'spin_mensaje_bienvenida',
                {{mensaje_bienvenida_habilitado|yesno:"true,false"}}, "{% url 'occ-mensaje-bienvenida' %}",
                [$("#link-bienvenida-popup")]);
            configuration.addButtonWithDependents(
                'mensajeIncontactables', 'spin_incontactables',
                {{mensaje_incontactables_habilitado|yesno:"true,false"}}, "{% url 'occ-mensaje-incontactables' %}",
                [$("#link-incontactables-popup")]);
        });
    </script>
    <script type="text/javascript" src="{{ STATIC_URL }}js/configuracion-mensaje.js"></script>

{% endblock %}

{% block solapas %}
    {% if puede_crear_propuestas %}
        <a href="{% url 'proposals-index' %}" id="proposals-index" title="Propuestas">Propuestas</a>
    {% endif %}
    {% if puede_modificar_servicio_de_chat %}
        <a href="{% url 'occ-mis-chats' %}">Mis Chats</a>
    {% endif %}
    {% if user.vendedor.configuracion_de_servicios.tiene_sms_habilitado or user.vendedor.tiene_al_menos_una_campania %}
        <a href="{% url 'occ-campanias' %}">Campañas</a>
    {% endif %}
    <a href="{% url 'occ-configuracion' %}" class="activo">Configuración</a>
{% endblock %}

{% block configuracion_servicios %}
    <div id="overlay" style="display:none"></div>
    <li id="chat" class="chat">
        <h1>Incrementa tus ventas con el nuevo servicio de Chat.<br/>Activalo ahora!</h1>
        {% if puede_modificar_servicio_de_chat %}
            <button id="boton-habilitar-chat"><label><span></span> Servicio de Chat</label></button>
            <input type="hidden" value="0"/>
        {% else %}
            <button id="boton-habilitar-chat" disabled><label><span></span> Servicio de Chat</label></button>
            <input type="hidden" value="0"/>
        {% endif %}

        <input type="hidden" value="0"/>
        <div id="spin_chat" class='spin-holder'></div>
    </li>
    <li id="notificaciones" class="notificaciones">
        <h1>Recibí notificaciones del sistema.<br/>Activalo ahora!</h1>
        <button id="boton-habilitar-notificaciones"><label><span></span> Notificaciones</label></button>
        <input type="hidden" value="0"/>
        <a id="link-datos-de-contacto" class="link-popup" href="{% url 'datos-de-contacto' %}" style="display: none;">
                Configurar mis datos
        </a>
        <div id="spin_notificaciones" class='spin-holder'></div>
    </li>

    <li id="mensajeBienvenida" class="contacto-rapido">
        <h1>Servicio de WhatsApp de bienvenida.<br/>Activalo ahora!</h1>
        {% if puede_modificar_servicio_mensaje_bienvenida %}
            <button id="boton-mensaje-bienvenida"><label><span></span> Mensaje de bienvenida</label></button>
            <input type="hidden" value="0"/>
            <a id="link-bienvenida-popup" class="link-popup"
               href="javascript:cambiarConfiguracion(cambiar_mensaje_bienvenida_url, 'spin_mensaje_bienvenida');">
                Configurar mensaje
            </a>
        {% else %}
            <button id="boton-mensaje-bienvenida" disabled><label><span></span> Mensaje de bienvenida</label></button>
            <input type="hidden" value="0"/>
            <a id="link-bienvenida-popup" class="link-popup"
               href="javascript:cambiarConfiguracion(cambiar_mensaje_bienvenida_url, 'spin_mensaje_bienvenida');"
               style="display: none">
                Configurar mensaje
            </a>
        {% endif %}
        <div id="spin_mensaje_bienvenida" class='spin-holder'></div>
    </li>
    <li id="mensajeIncontactables" class="incontactable">
        <h1>Servicio de mensajes para incontactables.<br/>Activalo ahora!</h1>
        {% if puede_modificar_servicio_incontactables %}
            <button id="boton-mensaje-incontactables"><label><span></span> Mensaje para incontactables</label></button>
            <input type="hidden" value="0"/>
            <a id="link-incontactables-popup" class="link-popup"
               href="javascript:cambiarConfiguracion(cambiar_mensaje_incontactables_url, 'spin_mensaje_incontactables')">
                Configurar mensaje
            </a>
        {% else %}
            <button id="boton-mensaje-incontactables" disabled><label><span></span> Mensaje para incontactables</label></button>
            <input type="hidden" value="0"/>
            <a id="link-incontactables-popup" class="link-popup"
               href="javascript:cambiarConfiguracion(cambiar_mensaje_incontactables_url, 'spin_mensaje_incontactables')" style="display: none">
                Configurar mensaje
            </a>
        {% endif %}
        <div id="spin_mensaje_incontactables" class='spin-holder'></div>
    </li>


    {% endblock %}