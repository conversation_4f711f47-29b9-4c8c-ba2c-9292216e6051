from django.db.models import Q

from prospectos.forms import InformacionAdicionalDeProspectoForm
from prospectos.models import InformacionDeRedesSociales, InformacionAdicionalDeProspecto


class FormaDeVer(object):
    @classmethod
    def nueva(cls, vendedor, prospecto):
        return cls(vendedor, prospecto)

    def __init__(self, vendedor, prospecto):
        self.prospecto = prospecto
        self.vendedor = vendedor
        self._contexto = {}

    def agregar_datos_de_prospecto_a_contexto(self):
        raise NotImplementedError("Subclass Responsibility")

    def nombres_de_campos_asociados(self):
        raise NotImplementedError("Subclass Responsibility")

    def contexto(self):
        return self._contexto

    def _obtener_nombre_facebook(self):
        nombre = self.prospecto.perfil_facebook().obtener_nombre() if self.prospecto.perfil_facebook() else ''
        return nombre

    def _obtener_url_facebook(self):
        return self.prospecto.facebook_url()

    def _obtener_celular(self, info_redes_sociales):
        return info_redes_sociales.filter(tipo=InformacionDeRedesSociales.CELULAR)

    def _obtener_telefono(self, info_redes_sociales):
        return info_redes_sociales.filter(tipo=InformacionDeRedesSociales.TELEFONO)

    def _obtener_twitter(self, info_redes_sociales):
        return info_redes_sociales.filter(tipo=InformacionDeRedesSociales.TWITTER)

    def _obtener_info_redes_sociales_filtrada(self):
        return InformacionDeRedesSociales.objects.filter(~Q(tipo=InformacionDeRedesSociales.TWITTER),
                                                         ~Q(tipo=InformacionDeRedesSociales.FACEBOOK),
                                                         ~Q(tipo=InformacionDeRedesSociales.CELULAR),
                                                         ~Q(tipo=InformacionDeRedesSociales.TELEFONO),
                                                         prospecto=self.prospecto).all()

    def _obtener_datos_de_redes_sociales(self):
        info = InformacionDeRedesSociales.objects.filter(Q(tipo=InformacionDeRedesSociales.TWITTER) |
                                                         Q(tipo=InformacionDeRedesSociales.FACEBOOK) |
                                                         Q(tipo=InformacionDeRedesSociales.CELULAR) |
                                                         Q(tipo=InformacionDeRedesSociales.TELEFONO),
                                                         prospecto=self.prospecto)
        return info

    def _agregar_datos_basicos(self):
        datos_redes_sociales = self._obtener_datos_de_redes_sociales()
        self._contexto['datos_twitter'] = self._obtener_twitter(datos_redes_sociales)
        self._contexto['url_facebook'] = self._obtener_url_facebook()
        self._contexto['nombre_facebook'] = self._obtener_nombre_facebook()
        self._contexto['celulares'] = self._obtener_celular(datos_redes_sociales)
        self._contexto['telefonos'] = self._obtener_telefono(datos_redes_sociales)
        self._contexto['info_redes_sociales_filtrada'] = self._obtener_info_redes_sociales_filtrada()
        self._contexto['prospecto'] = self.prospecto
        self._contexto['comentarios'] = self.prospecto.comentarios.all().order_by('-datetime')
        self._contexto['tarjeta_elegida'] = self.prospecto.marca_de_tarjeta_de_credito()
        self._contexto['tarjetas_elegibles'] = self._tarjetas_elegibles()

    def _tarjetas_elegibles(self):
        from prospectos.models import MarcaDeTarjetaDeCredito
        tarjetas = [{'value': '', 'string': 'Ninguna'}]
        return tarjetas
        for choice in MarcaDeTarjetaDeCredito.OPCIONES:
            tarjetas.append({'value': choice[0], 'string': choice[1]})
        return tarjetas


class FormaDeVerParcial(FormaDeVer):
    def agregar_datos_de_prospecto_a_contexto(self):
        self._agregar_datos_basicos()
        self._agregar_datos_adicionales()
        return self.contexto()

    def nombres_de_campos_asociados(self):
        campos_adicionales = self.vendedor.obtener_concesionaria().campos_adicionales()
        return [campo.nombre() for campo in campos_adicionales]

    def _agregar_datos_adicionales(self):
        nombres_de_campos_asociados = self.nombres_de_campos_asociados()
        if nombres_de_campos_asociados:
            informacion_adicional = self.prospecto.obtener_informacion_adicional()
            self._contexto['informacion_adicional_form'] = InformacionAdicionalDeProspectoForm(
                info_adicional=informacion_adicional, nombres_de_campos=nombres_de_campos_asociados)


class FormaDeVerExtendida(FormaDeVer):
    def agregar_datos_de_prospecto_a_contexto(self):
        self._agregar_datos_basicos()
        self._agregar_datos_adicionales()
        return self.contexto()

    def _agregar_datos_adicionales(self):
        nombres_de_campos_asociados = self.nombres_de_campos_asociados()
        informacion_adicional = self.prospecto.obtener_informacion_adicional()
        self._contexto['informacion_adicional_form'] = InformacionAdicionalDeProspectoForm(
            info_adicional=informacion_adicional, nombres_de_campos=nombres_de_campos_asociados)

    def nombres_de_campos_asociados(self):
        return InformacionAdicionalDeProspecto.nombres_validos_de_carga()


