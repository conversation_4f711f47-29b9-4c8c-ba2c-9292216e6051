import json
from datetime import timedelta

from django.urls import reverse
from django.utils.timezone import now

from prospectos.models import Prospecto, Tag
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from testing.base import BaseLoggedSupervisorTest
from testing.factories import ProspectosFactory
from testing.test_utils import reload_model


class ReasignarProspectosTest(BaseLoggedSupervisorTest):
    """
        Nota: classe movida no es de mi autoria
    """

    def setUp(self):
        super(ReasignarProspectosTest, self).setUp()
        f = self.fixture
        self.repartidor = RepartidorDeProspectos.nuevo()
        self.p1 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'], estado='V')
        self.p2 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'], estado='F')
        self.p3 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'],
                                    fecha_de_asignacion_a_vendedor=now() + timedelta(days=-5))
        self.p4 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'], estado='P',
                                    fecha_de_asignacion_a_vendedor=now() + timedelta(days=-8))
        self.p5 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'])
        self.p6 = ProspectosFactory(campania=f['camp_1'], responsable=f['sup_1'], vendedor=f['sup_1'], estado='P')

        f['vend_1'].equipo = f['equipo_1']
        self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=f['vend_1'], factor_de_asignacion=20)
        f['vend_2'].equipo = f['equipo_1']
        f['vend_2'].save()

    def _get_prospectos_pks_de(self, vendedor, tamanio=2):
        prospectos = vendedor.todos_los_prospectos()
        self.assertGreaterEqual(prospectos.count(), 2, 'El user.vendedor debe tener al menos dos prospectos')
        prospectos_pks = [prospecto.pk for prospecto in prospectos.all()][:tamanio]
        return prospectos_pks

    def assert_reasignacion_de_prospectos(self, vendedor_emisor, vendedor_receptor, prospectos):
        self.assertFalse((vendedor_emisor.prospectos.all() & prospectos.all()).exists(),
                         'El vendedor emisor NO deberia tener asignado los prospectos')
        self.assertEqual((vendedor_receptor.prospectos.all() & prospectos.all()).count(), prospectos.count(),
                         'El vendedor receptor deberia tener asignado los prospectos')

    def _post_reasignar_a(self, forma_de_asignacion, cantidad, vendedores_ids=None, vendedor_id='',
                          equipo_id='', metodo='', seleccion_de_prospectos='{}', asignados='si'):
        if not vendedores_ids:
            vendedores_ids = self.fixture['sup_1'].id
        params = {'tipo_origen': '', 'filter_estado': '', 'filter_llamado': '', 'asignados': asignados,
                  'vendedores': vendedores_ids, 'campania': '', 'accion': 'reasignar',
                  'cantidad': cantidad,
                  'asignar': forma_de_asignacion,
                  'metodo': metodo,
                  'equipo': equipo_id,
                  'vendedor': vendedor_id,
                  'seleccion_de_prospectos': seleccion_de_prospectos
                  }
        url = reverse('administracion')
        response = self.client.post(url, params)
        return response

    def _post_reasignar_a_vendedor(self, cantidad, vendedor, vendedores_ids=None, seleccion_de_prospectos='{}',
                                   asignados='si'):
        response = self._post_reasignar_a(forma_de_asignacion='vendedor',
                                          cantidad=cantidad,
                                          vendedor_id=vendedor.id,
                                          vendedores_ids=vendedores_ids,
                                          seleccion_de_prospectos=seleccion_de_prospectos,
                                          asignados=asignados)
        return response

    def _post_reasignar_a_equipo(self, cantidad, equipo, metodo='uniforme'):
        response = self._post_reasignar_a(forma_de_asignacion='equipo',
                                          cantidad=cantidad,
                                          equipo_id=equipo.id,
                                          metodo=metodo)
        return response

    def _post_reasignar_a_todos(self, cantidad, metodo='uniforme'):
        response = self._post_reasignar_a(forma_de_asignacion='todos',
                                          cantidad=cantidad,
                                          metodo=metodo)
        return response

    def _json_para_selecion_de(self, modo, valores):
        json_prospectos_pks = json.dumps([str(pk) for pk in valores])
        json_prospectos_pks = json_prospectos_pks.replace('\"', '\\\"')
        json_seleccion = '{ "mode": "%s", "values": "%s"}' % (modo, json_prospectos_pks)
        return json_seleccion

    def test_reasignar_a_vendedor(self):
        vendedor = self.fixture['vend_1']
        cant_inicial = vendedor.prospectos.count()
        cantidad = 3
        response = self._post_reasignar_a_vendedor(cantidad, vendedor)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(cant_inicial + cantidad, vendedor.prospectos.count())

    def test_reasignar_por_equipo_de_supervisor_reasigna_6_prospectos_uniformemente(self):
        cant_inicial_1 = self.fixture['vend_1'].prospectos.count()
        cant_inicial_2 = self.fixture['vend_2'].prospectos.count()
        cantidad = self.supervisor.prospectos.count()
        response = self._post_reasignar_a_equipo(cantidad, equipo=self.fixture['equipo_1'])
        self.assertEqual(response.status_code, 200)
        self.assertEqual(cant_inicial_1 + cantidad / 2, self.fixture['vend_1'].prospectos.count())
        self.assertEqual(cant_inicial_2 + cantidad / 2, self.fixture['vend_2'].prospectos.count())

    def test_reasignar_por_equipo_por_factor(self):
        cant_inicial_1 = self.fixture['vend_1'].prospectos.count()
        cant_inicial_2 = self.fixture['vend_2'].prospectos.count()
        cantidad = 6
        response = self._post_reasignar_a_equipo(cantidad, equipo=self.fixture['equipo_1'], metodo='factor')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(cant_inicial_1 + 4, self.fixture['vend_1'].prospectos.count())
        self.assertEqual(cant_inicial_2 + 2, self.fixture['vend_2'].prospectos.count())

    def test_reasignar_a_todos_por_factor(self):
        vendedor_uno = self.fixture['vend_1']
        cant_inicial_1 = vendedor_uno.prospectos.count()
        vendedor_dos = self.fixture['vend_2']
        cant_inicial_2 = vendedor_dos.prospectos.count()
        vendedor_cinco = self.fixture['vend_5']
        cant_inicial_5 = vendedor_cinco.prospectos.count()
        cantidad = 6
        response = self._post_reasignar_a_todos(cantidad, metodo='factor')
        self.assertEqual(response.status_code, 200)
        self.assertGreaterEqual(vendedor_uno.prospectos.count(), cant_inicial_1 + 2)
        self.assertLessEqual(vendedor_uno.prospectos.count(), cant_inicial_1 + 3)

        self.assertGreaterEqual(vendedor_dos.prospectos.count(), cant_inicial_2 + 1)
        self.assertLessEqual(vendedor_dos.prospectos.count(), cant_inicial_2 + 2)

        self.assertGreaterEqual(vendedor_cinco.prospectos.count(), cant_inicial_5 + 1)
        self.assertLessEqual(vendedor_cinco.prospectos.count(), cant_inicial_5 + 2)

    def test_prioridad_a_nuevos_mas_viejos(self):
        # 354621
        vendedor = self.fixture['vend_1']
        response = self._post_reasignar_a_vendedor(cantidad=1, vendedor=vendedor)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(Prospecto.objects.get(id=self.p3.id).vendedor, vendedor)
        self._post_reasignar_a_vendedor(cantidad=1, vendedor=vendedor)
        self.assertEqual(Prospecto.objects.get(id=self.p5.id).vendedor, vendedor)
        self._post_reasignar_a_vendedor(cantidad=1, vendedor=vendedor)
        self.assertEqual(Prospecto.objects.get(id=self.p4.id).vendedor, vendedor)
        self._post_reasignar_a_vendedor(cantidad=1, vendedor=vendedor)
        self.assertEqual(Prospecto.objects.get(id=self.p6.id).vendedor, vendedor)
        self._post_reasignar_a_vendedor(cantidad=1, vendedor=vendedor)
        self.assertEqual(Prospecto.objects.get(id=self.p2.id).vendedor, vendedor)
        self._post_reasignar_a_vendedor(cantidad=1, vendedor=vendedor)
        self.assertEqual(Prospecto.objects.get(id=self.p1.id).vendedor, vendedor)

    def test_reasignar_prospectos_seleccionados_a_vendedor(self):
        # user.vendedor asigna dos prospectos al vendedor 'vend_1'
        vendedor_receptor = self.fixture['vend_1']
        vendedor_emisor = self.user.vendedor
        prospectos_pks = self._get_prospectos_pks_de(vendedor_emisor)
        cantidad = len(prospectos_pks)
        seleccion_de_prospectos = self._json_para_selecion_de('selection', prospectos_pks)

        self.assertFalse(vendedor_receptor.prospectos.filter(
            pk__in=prospectos_pks).exists(), 'el vendedor NO debe tener asignado los prospectos')

        response = self._post_reasignar_a_vendedor(cantidad, vendedor=vendedor_receptor,
                                                   vendedores_ids=vendedor_emisor.id,
                                                   seleccion_de_prospectos=seleccion_de_prospectos)

        self.assertEqual(response.status_code, 200)
        prospectos = Prospecto.objects.filter(pk__in=prospectos_pks)
        self.assert_reasignacion_de_prospectos(vendedor_emisor, vendedor_receptor, prospectos)

    def test_reasignar_prospectos_seleccionados_a_vendedor_debe_actualizar_la_fecha_de_asignacion_a_vendedor(self):
        # Primero obtenemos el vendedor actual de los prospectos a reasignar --> vendedor_emisor
        # Luego obtenemos el vendedor al que vamos a querer reasignar --> vendedor_receptor (y su supervisor)
        vendedor_receptor = self.fixture['vend_1']
        responsable_del_receptor = vendedor_receptor.responsable()
        vendedor_emisor = self.user.vendedor

        # Obtenemos los prospectos a reasignar
        prospectos_pks = self._get_prospectos_pks_de(vendedor_emisor)
        cantidad = len(prospectos_pks)
        prospectos = Prospecto.objects.filter(pk__in=prospectos_pks)
        ahora = now()
        # Reasignamos los prospectos al responsable del vendedor_receptor. Todavia no reasignamos al vendedor_receptor.
        # Como solo reasignamos supervisor, se borra el vendedor y su fecha de asignacion.
        self.repartidor.asignar_prospectos_a_responsable(supervisor=responsable_del_receptor,
                                                         prospectos=prospectos)

        # Testeamos que los prospectos hayan cambiado de supervisor, y tengan vendedor y fecha de asignacion.
        self._assert_prospectos_asignados_con_sus_fechas(prospectos, responsable_del_receptor, None,
                                                         ahora,
                                                         None)

        # Armamos el json de seleccion prospectos (para la pantalla de administracion de supervisores).
        # Reasignamos los prospectos a vendedor_receptor.
        seleccion_de_prospectos = self._json_para_selecion_de('selection', prospectos_pks)
        response = self._post_reasignar_a_vendedor(cantidad, vendedor=vendedor_receptor,
                                                   seleccion_de_prospectos=seleccion_de_prospectos, asignados='no')
        self.assertEqual(response.status_code, 200)
        prospectos = Prospecto.objects.filter(pk__in=prospectos_pks)

        # Revisamos que los prospectos reasignados tengan al vendedor_receptor, su supervisor.
        # Ademas, tenemos que verificar que ambas fechas de asignaciones correspondan al supervisor..
        self._assert_prospectos_asignados_con_sus_fechas(prospectos, responsable_del_receptor, vendedor_receptor,
                                                         prospectos[0].fecha_de_asignacion_a_supervisor(),
                                                         prospectos[0].fecha_de_asignacion_a_supervisor())

    def _assert_prospectos_asignados_con_sus_fechas(self, prospectos, responsable_asignado, vendedor_asignado,
                                                    fecha_de_asignacion_a_responsable, fecha_de_asignacion_a_vendedor):
        for prospecto in prospectos:
            prospecto = reload_model(prospecto)
            self.assertEqual(prospecto.obtener_responsable(), responsable_asignado)
            self.assertEqual(prospecto.obtener_vendedor(), vendedor_asignado)
            fecha_asignacion_supervisor = prospecto.fecha_de_asignacion_a_supervisor()
            self._assert_fechas_de_asignacion_iguales(fecha_asignacion_supervisor, fecha_de_asignacion_a_responsable)
            fecha_asignacion_vendedor = prospecto.fecha_de_asignacion_a_vendedor()
            self._assert_fechas_de_asignacion_iguales(fecha_asignacion_vendedor, fecha_de_asignacion_a_vendedor)

    def _assert_fechas_de_asignacion_iguales(self, fecha1, fecha2):
        if fecha1 is None:
            self.assertIsNone(fecha2)
        elif fecha2 is None:
            self.assertIsNone(fecha1)
        else:
            self.assertEqual(fecha1.year, fecha2.year)
            self.assertEqual(fecha1.month, fecha2.month)
            self.assertEqual(fecha1.day, fecha2.day)
            self.assertEqual(fecha1.hour, fecha2.hour)
            self.assertEqual(fecha1.minute, fecha2.minute)

    def test_reasignar_prospectos_deseleccionados_a_vendedor(self):
        # user.vendedor asigna  al vendedor 'vend_1' todos prospectos menos los que tiene las siguientes pks [12, 14, 16, 17]
        vendedor_receptor = self.fixture['vend_1']
        vendedor_emisor = self.user.vendedor
        prospectos_pks = self._get_prospectos_pks_de(vendedor_emisor)
        prospectos = self.user.vendedor.prospectos_a_cargo.exclude(pk__in=prospectos_pks)
        cantidad = prospectos.count()
        seleccion_de_prospectos = self._json_para_selecion_de('deselection', prospectos_pks)

        response = self._post_reasignar_a_vendedor(cantidad, vendedor=vendedor_receptor,
                                                   vendedores_ids=vendedor_emisor.id,
                                                   seleccion_de_prospectos=seleccion_de_prospectos)
        self.assertEqual(response.status_code, 200)
        self.assert_reasignacion_de_prospectos(vendedor_emisor, vendedor_receptor, prospectos)

    def test_reasignar_prospectos_seleccionando_todos_a_vendedor(self):
        # user.vendedor asigna  al vendedor 'vend_1' todos prospectos
        vendedor_receptor = self.fixture['vend_1']
        vendedor_emisor = self.user.vendedor
        cantidad = vendedor_emisor.prospectos.count()
        seleccion_de_prospectos = self._json_para_selecion_de('deselection', [])

        response = self._post_reasignar_a_vendedor(cantidad, vendedor=vendedor_receptor,
                                                   vendedores_ids=vendedor_emisor.id,
                                                   seleccion_de_prospectos=seleccion_de_prospectos)
        self.assertEqual(response.status_code, 200)
        self.assert_reasignacion_de_prospectos(vendedor_emisor, vendedor_receptor, vendedor_emisor.prospectos)

    def test_reasignar_prospectos_deseleccionando_todos_a_vendedor(self):
        # user.vendedor NO asigna al vendedor 'vend_1' ningun prospecto
        vendedor_receptor = self.fixture['vend_1']
        vendedor_emisor = self.user.vendedor
        prospectos_pks = []
        cantidad = len(prospectos_pks)
        seleccion_de_prospectos = self._json_para_selecion_de('selection', prospectos_pks)

        response = self._post_reasignar_a_vendedor(cantidad, vendedor=vendedor_receptor,
                                                   vendedores_ids=vendedor_emisor.id,
                                                   seleccion_de_prospectos=seleccion_de_prospectos)
        self.assertEqual(response.status_code, 200)
        prospectos = Prospecto.objects.filter(pk__in=prospectos_pks)
        self.assert_reasignacion_de_prospectos(vendedor_emisor, vendedor_receptor, prospectos)

    def test_reasignar_prospectos_con_tags_a_vendedor_debe_copiar_los_tags(self):
        # user.vendedor asigna sus prospectos al vendedor 'vend_1'
        vendedor_receptor = self.fixture['vend_1']
        vendedor_emisor = self.user.vendedor
        Tag.objects.all().delete()
        cantidad = self._agregar_tags_a(cantidad=0, prospectos=vendedor_emisor.prospectos)

        prospectos_pks = self._get_prospectos_pks_de(vendedor_emisor, tamanio=cantidad)
        seleccion_de_prospectos = self._json_para_selecion_de('selection', prospectos_pks)

        response = self._post_reasignar_a_vendedor(cantidad, vendedor=vendedor_receptor,
                                                   vendedores_ids=vendedor_emisor.id,
                                                   seleccion_de_prospectos=seleccion_de_prospectos)

        self.assertEqual(response.status_code, 200)
        prospectos = Prospecto.objects.filter(pk__in=prospectos_pks)
        self.assert_reasignacion_de_prospectos(vendedor_emisor, vendedor_receptor, prospectos)
        self.assertEqual(Tag.objects.filter(vendedor=vendedor_emisor).count(), cantidad)
        self.assertEqual(Tag.objects.filter(vendedor=vendedor_receptor).count(), cantidad)

    def _agregar_tags_a(self, cantidad, prospectos):
        for prospecto in prospectos.all():
            tag = Tag.objects.create(vendedor=prospecto.vendedor, nombre=str(cantidad))
            tag.prospectos.add(prospecto)
            cantidad += 1
        return cantidad

    def test_reasignar_a_todos_deberia_copiar_tags(self):
        Tag.objects.all().delete()
        vendedor_uno = self.fixture['vend_1']
        cant_inicial_1 = vendedor_uno.prospectos.count()
        vendedor_dos = self.fixture['vend_2']
        cant_inicial_2 = vendedor_dos.prospectos.count()
        vendedor_cinco = self.fixture['vend_5']
        cant_inicial_5 = vendedor_cinco.prospectos.count()

        cantidad_tags_vendedor_uno = self._agregar_tags_a(cantidad=0, prospectos=vendedor_uno.prospectos)
        cantidad_tags_vendedor_dos = self._agregar_tags_a(cantidad_tags_vendedor_uno,
                                                          prospectos=vendedor_dos.prospectos)
        cantidad_tags_vendedor_cinco = self._agregar_tags_a(cantidad_tags_vendedor_dos,
                                                            prospectos=vendedor_cinco.prospectos)

        response = self._post_reasignar_a_todos(cantidad=6, metodo='factor')
        self.assertEqual(response.status_code, 200)
        self.assertGreaterEqual(vendedor_uno.prospectos.count(), cant_inicial_1 + 2)
        self.assertLessEqual(vendedor_uno.prospectos.count(), cant_inicial_1 + 3)
        self.assertEqual(Tag.objects.filter(vendedor=vendedor_uno).count(), cantidad_tags_vendedor_uno)

        self.assertGreaterEqual(vendedor_dos.prospectos.count(), cant_inicial_2 + 1)
        self.assertLessEqual(vendedor_dos.prospectos.count(), cant_inicial_2 + 2)
        self.assertEqual(Tag.objects.filter(vendedor=vendedor_dos).count(),
                          cantidad_tags_vendedor_dos - cantidad_tags_vendedor_uno)

        self.assertGreaterEqual(vendedor_cinco.prospectos.count(), cant_inicial_5 + 1)
        self.assertLessEqual(vendedor_cinco.prospectos.count(), cant_inicial_5 + 2)
        self.assertEqual(Tag.objects.filter(vendedor=vendedor_cinco).count(),
                          cantidad_tags_vendedor_cinco - cantidad_tags_vendedor_dos)