from django.test import TestCase

from prospectos.models import <PERSON><PERSON>, <PERSON><PERSON>
from prospectos.utils.normalizador_de_marcas import NormalizadorDeMarcas


class NormalizacionDeMarcasTest(TestCase):

    def setUp(self):
        super(NormalizacionDeMarcasTest, self).setUp()
        Marca.objects.all().delete()
        self._marca = self._crear_marca('1', 'Ford')

    def _crear_marca(self, identificador, nombre):
        marca = Marca.nueva_normalizada(
            identificador=identificador, logo='', codigo=nombre, habilitada=True, nombre=nombre)
        return marca

    def test_se_devuelve_una_marca_normalizada_a_partir_de_su_nombre_si_es_principal_y_lowercase(self):
        normalizador = NormalizadorDeMarcas()
        marca = normalizador.normalizar('ford')
        self.assertTrue(marca.esta_normalizada())

    def test_se_devuelve_una_marca_normalizada_a_partir_de_su_alias(self):
        Alias.nuevo('FORDING', self._marca)
        normalizador = NormalizadorDeMarcas()
        marca = normalizador.normalizar('FORDING')
        self.assertTrue(marca.esta_normalizada())

    def test_se_devuelve_una_marca_normalizada_a_partir_de_su_nombre_si_es_principal_y_titlecase(self):
        normalizador = NormalizadorDeMarcas()
        marca = normalizador.normalizar('Ford')
        self.assertTrue(marca.esta_normalizada())

    def test_se_devuelve_una_marca_no_normalizada_con_alias_a_partir_de_su_nombre_si_no_es_principal(self):
        normalizador = NormalizadorDeMarcas()
        nombre = 'marca_rara_y_sin_sentido'
        marca = normalizador.normalizar(nombre)
        self.assertFalse(marca.esta_normalizada())
        alias = Alias.objects.con_nombre(nombre)
        self.assertEqual(marca, alias.marca())