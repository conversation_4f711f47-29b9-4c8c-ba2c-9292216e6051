from datetime import timedelta

from django.core.exceptions import ValidationError
from freezegun.api import freeze_time

from core.date_helper import DatetimeHelper
from core.models import Sistema
from prospectos.models import Prospecto
from prospectos.models.entrega_de_datos.gestor_de_peticiones_de_prospectos_por_vendedor import \
    GestorDePeticionesDeProspectosPorVendedor
from testing.base import BaseFixturedTest


class ValidacionVendedoresProspectosDisponiblesParaEntrega(BaseFixturedTest):

    def setUp(self):
        super().setUp()

        self.vendedor_uno = self.fixture['vend_1']
        self.supervisor_uno = self.fixture['sup_1']
        self.campania_uno = self.fixture['camp_1']
        Prospecto.objects.all().delete()
        self.gestor_de_peticiones = GestorDePeticionesDeProspectosPorVendedor.nuevo_para(vendedor=self.vendedor_uno)
        self.date_helper = DatetimeHelper()
        self.vendedor_uno.configuracion_servicios.habilitar_pedir_prospecto()
        self.vendedor_uno.obtener_concesionaria().configuracion_servicios.habilitar_pedir_prospecto()

    def test_si_el_permiso_para_pedir_prospecto_indica_que_no_hay_disponibilidad_lanza_error(self):
        # Dado
        self.gestor_de_peticiones.actualizar_permiso_de_peticion_segun_disponibilidad()

        # Cuando / Entonces
        self._assert_se_lanza_una_excepcion_al_pedir_prospecto_cuando_no_hay_disponibles()

    def test_si_no_existe_permiso_para_pedir_prospectos_segun_disponibilidad_lanza_error(self):
        # Dado
        # Cuando / Entonces
        self._assert_se_lanza_una_excepcion_al_pedir_prospecto_cuando_no_hay_disponibles()

    def test_se_puede_actualizar_multiples_veces_el_permiso_para_pedir_prospecto_segun_disponibilidad(self):
        # Dado
        self.gestor_de_peticiones.actualizar_permiso_de_peticion_segun_disponibilidad()
        self.gestor_de_peticiones.actualizar_permiso_de_peticion_segun_disponibilidad()

        # Cuando / Entonces
        self._assert_se_lanza_una_excepcion_al_pedir_prospecto_cuando_no_hay_disponibles()

    def test_si_al_actualizar_el_permiso_para_pedir_prospecto_hay_prospectos_disponibles_encola_la_peticion(self):
        # Dado
        with freeze_time("2024-11-30 09:00:00") as tiempo_freezado:
            self._actualizar_permiso_de_peticion_teniendo_disponibilidad(tiempo_freezado=tiempo_freezado)


            # Cuando
            self.gestor_de_peticiones.pedir_prospecto()

            # Entonces
            self.assertTrue(self.gestor_de_peticiones.tiene_peticiones_pendientes())

    def test_si_al_actualizar_el_permiso_para_pedir_prospecto_no_hay_disponibilidad_por_falta_de_(self):
        # Dado
        with freeze_time("2024-11-30 09:00:00") as tiempo_freezado:
            pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor=self.vendedor_uno, credito=10,
                                                                                 consumido=0,
                                                                                 yapa=0, campania=self.campania_uno)
            self.creador_de_contexto.crear_filtro_para_marca(pedido=pedido, nombre_de_marca='Toyota')
            self.creador_de_contexto.crear_prospecto_nuevo(campania=self.campania_uno, marca='Fiat')
            tiempo_freezado.tick(self._tiempo_de_antiguedad_minimo() + timedelta(seconds=1))
            self.gestor_de_peticiones.actualizar_permiso_de_peticion_segun_disponibilidad()

        # Cuando / Entonces
        self._assert_se_lanza_una_excepcion_al_pedir_prospecto_cuando_no_hay_disponibles()

    def _actualizar_permiso_de_peticion_teniendo_disponibilidad(self, tiempo_freezado):
        self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor=self.vendedor_uno)
        self.creador_de_contexto.crear_prospecto_nuevo(campania=self.campania_uno)
        tiempo_freezado.tick(self._tiempo_de_antiguedad_minimo() + timedelta(seconds=1))
        self.gestor_de_peticiones.actualizar_permiso_de_peticion_segun_disponibilidad()

    def _assert_se_lanza_una_excepcion_al_pedir_prospecto_cuando_no_hay_disponibles(self):
        self.assertRaisesMessage(ValidationError, 'Solo los vendedores habilitados pueden pedir prospectos',
                                 self.gestor_de_peticiones.pedir_prospecto)

    def _tiempo_de_antiguedad_minimo(self):
        tiempo_de_antiguedad_minimo_para_prospectos_disponibles = Sistema.instance().tiempo_de_antiguedad_minimo_para_prospectos_disponibles
        return timedelta(minutes=tiempo_de_antiguedad_minimo_para_prospectos_disponibles)
