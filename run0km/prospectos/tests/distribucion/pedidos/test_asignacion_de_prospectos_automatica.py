from datetime import timed<PERSON>ta

from django.utils import timezone
from freezegun import freeze_time

from core.models import Sistema
from prospectos.models.entrega_de_datos.opciones import MetodosDeAsignacionChoices
from prospectos.tests.distribucion.pedidos.test_pedidos_core import PedidosTest
from vendedores.gestor import GestorDeVendedores


@freeze_time("2024-05-09 17:00:00")
class AsignacionAutomaticaDeProspectoTest(PedidosTest):
    """
        TODO: tenemos duplicados algunos excenarios con:
          - AsignacionDeProspectoAPedidoConRestriccionesTest
          - AsignacionDeProspectoAPedidoTest
    """

    def setUp(self):
        super(AsignacionAutomaticaDeProspectoTest, self).setUp()
        self.supervisor_uno = self._crear_supervisor()
        self.supervisor_dos = self._crear_supervisor()
        self.gestor_de_vendedores = GestorDeVendedores.nuevo()

    # Entrega via pedidos a supervisores
    def test_pedidos_a_supervisor_deberia_asignar_si_cumple_restricciones_sin_tomar_en_cuenta_pedidos_de_sus_vendedores(
            self):
        self.supervisor_uno.cambiar_limite_de_datos_diarios_para_supervisor_por(nuevo_limite=1)
        vendedor_uno = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        pedido_uno = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor_uno,
                                                                                 supervisor=self.supervisor_uno,
                                                                                 credito=10,
                                                                                 yapa=5,
                                                                                 consumido=2,
                                                                                 campania=self.campania_uno)

        prospecto = self._crear_nuevo_prospecto()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(
            prospecto_id=prospecto.id, en_pedido_id=pedido_uno.id,
            a_vendedor=pedido_uno.vendedor,
            credito_consumido=2 + self.valor_de_categoria,
            a_responsable=self.supervisor_uno)
        prospecto_dos = self._crear_nuevo_prospecto(nombre='Prospecto Dos', telefono='9999-8765')
        pedido_para_sup = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(credito=123456, consumido=0, yapa=1234,
                                                                                     campania=prospecto_dos.campania,
                                                                                     restringir_por_datos_diarios=True,
                                                                                     restringir_por_acceso=False,
                                                                                     supervisor=self.supervisor_uno)
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto_dos)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(
            prospecto_id=prospecto_dos.id, en_pedido_id=pedido_para_sup.id,
            a_vendedor=None,
            a_responsable=self.supervisor_uno,
            credito_consumido=0 + self.valor_de_categoria)

    def test_pedidos_a_supervisor_no_deberia_asignarselo_si_no_cumple_las_restriccion_por_acceso(self):
        self._configurar_ultima_actividad_excedida_a(vendedor=self.supervisor)
        prospecto = self._crear_nuevo_prospecto()
        pedido = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(consumido=1, campania=prospecto.campania,
                                                                            restringir_por_datos_diarios=False,
                                                                            restringir_por_acceso=True,
                                                                            supervisor=self.supervisor)
        consumido_antiguo = pedido.consumido
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)
        self._assert_prospecto_no_asignado(prospecto=prospecto, consumido_esperado=consumido_antiguo, pedido=pedido)

    def test_pedidos_a_supervisor_no_deberia_asignarselo_si_no_cumple_las_restriccion_por_datos_diarios(self):
        self._configurar_exceso_de_datos_diarios_a_supervisor(vendedor=self.supervisor)
        prospecto = self._crear_nuevo_prospecto()
        pedido = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(consumido=1, campania=prospecto.campania,
                                                                            restringir_por_datos_diarios=True,
                                                                            restringir_por_acceso=False,
                                                                            supervisor=self.supervisor)
        consumido_antiguo = pedido.consumido
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)
        self._assert_prospecto_no_asignado(prospecto=prospecto, consumido_esperado=consumido_antiguo, pedido=pedido)

    def test_pedidos_a_supervisor_deberia_asignarselo_si_cumple_las_restricciones(self):
        self.supervisor.limite_de_datos_diarios_al_supervisor_en_pedidos = 3200
        self.supervisor._ultima_actividad = timezone.now()
        self.supervisor.save()
        prospecto = self._crear_nuevo_prospecto()
        pedido = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(consumido=1, campania=prospecto.campania,
                                                                            restringir_por_datos_diarios=True,
                                                                            restringir_por_acceso=True,
                                                                            supervisor=self.supervisor)
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(
            prospecto_id=prospecto.id, en_pedido_id=pedido.id, a_vendedor=None,
            a_responsable=self.supervisor, credito_consumido=1 + self.valor_de_categoria)

    # Entrega via pedidos a vendedores

    def test_pedidos_a_vendedor_deberia_asignar_aquel_con_menor_credito_asignado(self):
        vendedor_uno = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_dos)

        pedido_uno = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor_uno,
                                                                                 supervisor=self.supervisor_uno,
                                                                                 credito=10,
                                                                                 yapa=5,
                                                                                 consumido=2,
                                                                                 campania=self.campania_uno)

        pedido_dos = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor_dos,
                                                                                 supervisor=self.supervisor_dos,
                                                                                 credito=10,
                                                                                 yapa=5,
                                                                                 consumido=1,
                                                                                 campania=self.campania_uno)
        prospecto = self._crear_nuevo_prospecto()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(prospecto.id, pedido_dos.id,
                                                                 a_vendedor=pedido_dos.vendedor,
                                                                 credito_consumido=1 + self.valor_de_categoria)

        prospecto = self._crear_nuevo_prospecto()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(prospecto.id, pedido_uno.id,
                                                                 a_vendedor=pedido_uno.vendedor,
                                                                 credito_consumido=2 + self.valor_de_categoria)

    def test_pedidos_con_vendedores_restringidos_deberia_quedar_sin_asignacion(self):
        vendedor_uno = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)

        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor_uno,
            metodo_de_asignacion=MetodosDeAsignacionChoices.UNIFORME,
            restringir_por_acceso=True,
            consumido=1,
            campania=self.campania_uno)

        self._configurar_limite_de_acceso(horas=2)
        self._configurar_ultima_actividad(vendedor_uno, horas=5)
        self._configurar_ultima_actividad(vendedor_dos, horas=2)

        prospecto = self._crear_nuevo_prospecto()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(prospecto.id, pedido.id,
                                                                 a_vendedor=None, a_responsable=None,
                                                                 credito_consumido=1)

    def test_pedido_a_vendedor_con_restriccion_de_datos_diarios_debe_restringir_por_supervisor(self):
        """
            Supervisor, con dos vendedores, el supervisor supera el limite de acceso y el limite de datos diarios.
            Pedido que asigna a vendedor con datos diarios que no supera su limite, pero el supervisor si lo supera,
            no debe asignarsele el prospecto.
        """
        self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)

        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor_uno,
            metodo_de_asignacion=MetodosDeAsignacionChoices.UNIFORME,
            restringir_por_datos_diarios=True,
            restringir_por_acceso=True,
            consumido=1,
            campania=self.campania_uno)

        self._configurar_exceso_de_datos_diarios_a_supervisor(vendedor=self.supervisor_uno)
        self._configurar_limite_de_acceso(horas=2)
        self._configurar_ultima_actividad(self.supervisor_uno, horas=5)

        prospecto = self._crear_nuevo_prospecto()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(
            prospecto.id, pedido.id, a_vendedor=None, a_responsable=None, credito_consumido=1)

    def test_pedido_con_vendedores_restringidos_deberia_asignar_a_pedido_alternativo(self):
        vendedor_uno = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor_tres = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_dos)

        self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor_uno,
            metodo_de_asignacion=MetodosDeAsignacionChoices.UNIFORME,
            restringir_por_acceso=True,
            consumido=1,
            campania=self.campania_uno)

        pedido_dos = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(vendedor_tres,
                                                                                 supervisor=self.supervisor_dos,
                                                                                 credito=10,
                                                                                 yapa=5,
                                                                                 consumido=1,
                                                                                 campania=self.campania_uno)

        self._configurar_limite_de_acceso(horas=2)
        self._configurar_ultima_actividad(vendedor_uno, horas=5)
        self._configurar_ultima_actividad(vendedor_dos, horas=2)
        vendedor_uno.horas_desde_su_ultima_actividad()

        prospecto = self._crear_nuevo_prospecto()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(prospecto.id, pedido_dos.id,
                                                                 a_vendedor=vendedor_tres,
                                                                 a_responsable=self.supervisor_dos,
                                                                 credito_consumido=1 + self.valor_de_categoria)

    def test_pedido_a_vendedores_con_vendedor_sin_ultima_asingacion_deberia_ser_asignado(self):
        vendedor_uno = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)
        vendedor_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(self.supervisor_uno)

        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor_uno,
            metodo_de_asignacion=MetodosDeAsignacionChoices.FACTOR_MANUAL,
            consumido=1,
            campania=self.campania_uno)

        self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor_uno)
        self.gestor_de_vendedores.configurar_factor_de_asignacion_de(vendedor=vendedor_uno,
                                                                     factor_de_asignacion=20)
        prospecto = self._crear_nuevo_prospecto()
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(prospecto.id, pedido.id,
                                                                 a_vendedor=vendedor_dos, a_responsable=None,
                                                                 credito_consumido=1 + self.valor_de_categoria)

    def _configurar_limite_de_acceso(self, horas):
        sistema = Sistema.instance()
        sistema.limite_de_horas_sin_actividad_en_pedidos = horas
        sistema.save()

    def _configurar_ultima_actividad(self, vendedor, horas):
        ahora = timezone.localtime(timezone.now())
        vendedor._ultima_actividad = ahora - timedelta(hours=horas)
        vendedor.save()
