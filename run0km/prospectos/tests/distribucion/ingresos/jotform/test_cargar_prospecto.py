# coding=utf-8
from unittest import skipIf

import mock
from django.utils.timezone import now

from campanias.models import Campania
from lib.client_geolocation.tests import RequestGeolocationMock
from prospectos.models import Prospecto, CampoExtra
from prospectos.models.jotform import CargadorDeProspectoDesdeJotform
from prospectos.tests.distribucion.ingresos.jotform.test_jotform import JotFormTest
from testing.creador_de_contexto import ModificadorDeContextoParaTesting, CreadorDeContexto
from testing.factories import ProveedoresFactory
from testing.test_utils import reload_model


class CargarProspectoDesdeJotformTest(JotFormTest):

    def setUp(self):
        super(CargarProspectoDesdeJotformTest, self).setUp()
        self.modificador_de_contexto = ModificadorDeContextoParaTesting()
        self.creador_de_contexto = CreadorDeContexto(fixture=self.fixture)

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_es_ingresado_exitosamente(self, mocked_geoipplugin):
        proveedor = self._crear_proveedor()
        raw = {
            "q4_nombre4": "  Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": "SMS",
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
        }
        t0 = now()
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)
        self.assertIsNotNone(prospecto)
        prospecto = reload_model(prospecto)
        self.assertEqual(prospecto, Prospecto.objects.get(nombre='Sánaña'))
        self.assertEqual(CampoExtra.objects.filter(valor='El valor del campo extra', prospecto=prospecto).count(), 1)
        self.assertEqual(proveedor, prospecto.proveedor)
        self.assertEqual(prospecto.prefijo, '00123')
        self.assertIsNotNone(prospecto.fecha)
        self.assertTrue(prospecto.fecha > t0)
        self.assertTrue(prospecto.fecha < now())
        self.assertEqual(prospecto.obtener_telefono_sin_normalizar(), '12323423')
        self.assert_geolocalizacion(prospecto=prospecto, ip='***********', latitud=-34.7203, localidad='Quilmes',
                                    longitud=-58.2694, provincia='Buenos Aires')

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_con_vendedor_y_responsable_matchea_con_pedido_y_asigna_el_prospecto(self, mocked_geoipplugin):
        vendedor = self.fixture['vend_1']
        campania = Campania.objects.con_nombre(nombre='SMS')
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(supervisor=vendedor.responsable(),
                                                                             vendedor=vendedor, campania=campania,
                                                                             consumido=0)
        self.assertEqual(pedido.consumido, 0)
        raw = {
            "q4_nombre4": "  Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": campania.obtener_nombre(),
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
            "q44_vendedor": self.fixture['vend_1'].usuario().username,
            "q45_responsable": self.fixture['vend_1'].responsable().usuario().username
        }
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)
        self.assertIsNotNone(prospecto)
        prospecto = reload_model(prospecto)
        pedido = reload_model(pedido)
        self.assertEqual(pedido.consumido, 1)
        self.assertEqual(prospecto.obtener_vendedor(), self.fixture['vend_1'])
        self.assertEqual(prospecto.obtener_responsable(), self.fixture['vend_1'].responsable())

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_con_vendedor_y_responsable_no_matchea_con_pedido_y_crea_el_prospecto_sin_descontar_credito(
            self, mocked_geoipplugin):
        otro_vendedor = self.fixture['vend_3']
        campania = Campania.objects.con_nombre(nombre='SMS')
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(supervisor=otro_vendedor.responsable(),
                                                                             vendedor=otro_vendedor, campania=campania,
                                                                             consumido=0)
        self.assertEqual(pedido.consumido, 0)
        raw = {
            "q4_nombre4": "  Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": "SMS",
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
            "q44_vendedor": self.fixture['vend_1'].usuario().username,
            "q45_responsable": self.fixture['vend_1'].responsable().usuario().username
        }
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)
        self.assertIsNotNone(prospecto)
        prospecto = reload_model(prospecto)
        pedido = reload_model(pedido)
        self.assertEqual(prospecto.obtener_vendedor(), self.fixture['vend_1'])
        self.assertEqual(prospecto.obtener_responsable(), self.fixture['vend_1'].responsable())
        self.assertEqual(pedido.consumido, 0)

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_con_vendedor_y_responsable_no_relacionados_responde_error(self, mocked_geoipplugin):
        self._crear_proveedor()
        raw = {
            "q4_nombre4": "  Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": "SMS",
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
            "q44_vendedor": self.fixture['vend_1'].usuario().username,
            "q45_responsable": self.fixture['vend_1'].usuario().username
        }
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)
        self.assertIsNone(prospecto)
        self._assert_carga_fallida_para_error(
            error_esperado='El Supervisor vend1 vend1 no tiene al Vendedor vend1 vend1 entre sus empleados.')

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_con_vendedor_invalido_responde_error(self, mocked_geoipplugin):
        self._crear_proveedor()
        raw = {
            "q4_nombre4": "  Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": "SMS",
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
            "q44_vendedor": 'Ignacio Inexistente',
            "q45_responsable": self.fixture['vend_1'].usuario().username
        }
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)
        self.assertIsNone(prospecto)
        self._assert_carga_fallida_para_error(
            error_esperado='No se encontro el vendedor con username Ignacio Inexistente')

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_con_vendedor_pero_sin_supervisor_asigna_al_supervisor_del_vendedor(self, mocked_geoipplugin):
        raw = {
            "q4_nombre4": "  Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": "SMS",
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
            "q44_vendedor": self.fixture['vend_1'].usuario().username,
        }
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)
        self.assertIsNotNone(prospecto)
        self.assertEqual(prospecto.obtener_vendedor(), self.fixture['vend_1'])
        self.assertEqual(prospecto.obtener_responsable(), self.fixture['vend_1'].responsable())

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_con_supervisor_pero_sin_vendedor_asigna_solo_al_supervisor(self, mocked_geoipplugin):
        raw = {
            "q4_nombre4": "  Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": "SMS",
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
            "q45_responsable": self.fixture['vend_1'].responsable().usuario().username
        }
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)
        self.assertIsNotNone(prospecto)
        self.assertEqual(prospecto.obtener_vendedor(), None)
        self.assertEqual(prospecto.obtener_responsable(), self.fixture['vend_1'].responsable())

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_con_responsable_invalido_responde_error(self, mocked_geoipplugin):
        self._crear_proveedor()
        raw = {
            "q4_nombre4": "  Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": "SMS",
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
            "q44_vendedor": self.fixture['vend_1'].usuario().username,
            "q45_responsable": 'Ignacio Inexistente'
        }
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)
        self.assertIsNone(prospecto)
        self._assert_carga_fallida_para_error(
            error_esperado='No se encontro el responsable/supervisor con username Ignacio Inexistente')

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_con_vendedor_inactivo_responde_error(self, mocked_geoipplugin):
        vendedor = self.fixture['vend_1']
        usuario_vendedor = self.modificador_de_contexto.modificar_usuario_a_inactivo(vendedor.usuario())
        raw = {
            "q4_nombre4": "Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": "SMS",
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
            "q44_vendedor": usuario_vendedor.username,
            "q45_responsable": vendedor.responsable().usuario().username
        }
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)
        self.assertIsNone(prospecto)
        self._assert_carga_fallida_para_error(
            error_esperado='El usuario con rol {0} no esta activo.'.format(vendedor.full_name()))

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_con_responsable_inactivo_responde_error(self, mocked_geoipplugin):
        responsable = self.fixture['vend_1'].responsable()
        usuario_responsable = self.modificador_de_contexto.modificar_usuario_a_inactivo(responsable.usuario())
        raw = {
            "q4_nombre4": "  Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": "SMS",
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
            "q44_vendedor": self.fixture['vend_1'].usuario().username,
            "q45_responsable": usuario_responsable.username
        }
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)
        self.assertIsNone(prospecto)
        self._assert_carga_fallida_para_error(
            error_esperado='El usuario con rol {0} no esta activo.'.format(responsable.full_name()))

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_para_campania_erronea_responde_error(self, mocked_geoipplugin):
        # Carga con error de campaña erronea:
        raw = {"q4_nombre4": "  Sánaña", "q5_telefono": "23423", "q5_prefijo": "123", "q8_email": "<EMAIL>",
               "q6_mensaje": "El mensaje a meter en el form", "q99_campania9": "inexistente", "q10_campaña": 'ASD',
               "q11_extra11": 'El valor del campo extra', "q12_source": '1234', "q12_ip": '***********'}
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)
        self.assertIsNone(prospecto)
        self._assert_carga_fallida_para_error(error_esperado='Debe indicarse un nombre de campaña válido.')

    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_prospecto_con_parametros_invalidos_desde_ip_no_son_tenidos_en_cuenta(self, mocked_geoipplugin):
        self._crear_proveedor()
        raw = {
            "q4_nombre4": "  Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": "SMS",
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
        }
        parametros_invalidos = {
            "q13_localidad_desde_ip": "localidad_desde_ip",
            "q14_provincia_desde_ip": "provincia_desde_ip"
        }
        raw.update(parametros_invalidos)
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)
        self.assertIsNotNone(prospecto)
        prospecto = reload_model(prospecto)
        self.assertEqual(prospecto, Prospecto.objects.get(nombre='Sánaña'))
        self.assert_geolocalizacion(prospecto=prospecto, ip='***********', latitud=-34.7203, localidad='Quilmes',
                                    longitud=-58.2694, provincia='Buenos Aires')

    @skipIf(True, reason='Deploy incompleto')
    @mock.patch('core.locker.mem_locker.Locker.do_locking')
    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_carga_bloqueada_debe_responder_error(self, mocked_geoipplugin, mock_locker):
        # Dado
        self._locker_helper.configurar_locker_lanzar_recurso_bloqueado(mock_locker)
        raw = {
            "q4_nombre4": "  Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": "SMS",
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
        }

        # Cuando
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)

        # Entonces
        self.assertIsNone(prospecto)
        self._assert_carga_fallida_para_error(
            error_esperado='Los datos de contacto estan momentaneamente bloqueados.')

    @mock.patch('core.locker.mem_locker.Locker.lock_resource')
    @mock.patch('requests.get', return_value=RequestGeolocationMock.success())
    def test_al_tercer_intento_carga_desbloqueada_debe_ingresar_el_prospecto(
            self, mocked_geoipplugin, mock_lock_resource):
        # Dado
        self._locker_con_tercer_reintento_exitoso(mock_lock_resource)
        raw = {
            "q4_nombre4": "  Sánaña",
            "q5_telefono": "23423",
            "q5_prefijo": "123",
            "q8_email": "<EMAIL>",
            "q6_mensaje": "El mensaje a meter en el form",
            "q99_campania9": "SMS",
            "q10_campaña": 'ASD',
            "q11_extra11": 'El valor del campo extra',
            "q12_source": '1234',
            "q12_ip": '***********',
        }

        # Cuando
        prospecto = CargadorDeProspectoDesdeJotform().cargar(raw)

        # Entonces
        self.assertIsNotNone(prospecto)
        prospecto = reload_model(prospecto)
        self.assertEqual(prospecto.obtener_nombre(), 'Sánaña')
        self.assertEqual(prospecto.telefono, '123 23423')
        self.assertEqual(prospecto.email, '<EMAIL>')

    def _locker_con_tercer_reintento_exitoso(self, mock_lock_resource):
        # - Primer Intento: telefono deslockeado, email lockeado.
        # - Segundo intento telefono deslockeado, email lockeado.
        # - Tercer intento: telefono y email deslockeados.

        # Asume que el prospecto tiene telefono y email
        mock_lock_resource.side_effect = [True, False, True, False, True, True]

    def _crear_proveedor(self):
        proveedor = ProveedoresFactory(source_id='1234', nombre='Nombre Proveedor')
        return proveedor
