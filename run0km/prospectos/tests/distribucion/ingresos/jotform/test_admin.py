from prospectos.models import CargaFallidaDeJotform
from testing.base import BaseLoggedAdminTest
from testing.factories import CargaFallidaDeJotformFactory


class AdminJotformTest(BaseLoggedAdminTest):
    def crear_cargas_fallidas(self):
        datos = '{"nombre": "n1", "mensaje": "m1", "campania": "SMS", "email": "<EMAIL>", "telefono": "1"}'
        self.c1 = CargaFallidaDeJotformFactory(datos=datos, error='Algun Error')
        datos = '{"nombre": "n2", "mensaje": "m2", "campania": "", "email": "<EMAIL>", "telefono": "2"}'
        self.c2 = CargaFallidaDeJotformFactory(datos=datos, error='Otro Error')
        data = {'_selected_action': [str(self.c1.id), str(self.c2.id)], 'action': 'exportar_a_csv'}
        return self.client.post('/admin/prospectos/cargafallidadejotform/', data)

    def test_marca_como_exportados(self):
        response = self.crear_cargas_fallidas()
        self.assertEqual(response.status_code, 200)

        c1 = CargaFallidaDeJotform.objects.get(id=self.c1.id)
        self.assertTrue(c1.exportado)
        c2 = CargaFallidaDeJotform.objects.get(id=self.c2.id)
        self.assertTrue(c2.exportado)

    def test_datos_en_archivo_csv(self):
        response = self.crear_cargas_fallidas()
        self.crear_cargas_fallidas()
        self.assertTrue(response.has_header('Content-Type'))
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertTrue(response.has_header('Content-Disposition'))
        self.assertEqual(response['Content-Disposition'], 'attachment; filename=cargas_fallidas.csv')

        # CHEQUEO DE CAMPOS
        campos = ['nombre', 'mensaje', 'campania', 'email', 'telefono', 'fecha', 'error-de-carga']
        for nombre in campos:
            if not nombre == 'campania':
                self.assertTrue(nombre.title() in response.content.decode('utf-8'))

        self.assertIn('n1', response.content.decode('iso-8859-15'))
        self.assertIn('m1', response.content.decode('iso-8859-15'))
        self.assertIn('<EMAIL>', response.content.decode('iso-8859-15'))
        self.assertIn('Algun Error', response.content.decode('iso-8859-15'))
        self.assertIn('SMS', response.content.decode('iso-8859-15'))
