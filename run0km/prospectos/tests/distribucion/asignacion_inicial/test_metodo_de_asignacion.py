from datetime import time

from django.test import override_settings

from core.locker.mem_locker import Locker
from prospectos.models.base import PedidoDeProspecto
from prospectos.models.entrega_de_datos.asignacion import DistribucionDeProspectos, DistribucionPorFactor, \
    SeleccionViaSupervisores, SeleccionViaPedidos
from prospectos.models.entrega_de_datos.metodos_de_seleccion import SeleccionDeTodos, MetodoDeSeleccionUniforme, \
    MetodoDeSeleccionPorFactorManual, MetodoDeSeleccionPorFactorAdministrador, MetodoDeSeleccionPorFactorAsistido
from prospectos.models.entrega_de_datos.opciones import MetodosDeAsignacionChoices, RestriccionLimiteDatosDiario, \
    RestriccionLimiteDatosNuevos
from prospectos.models.entrega_de_datos.restricciones import RestriccionPorUltimoAccesso, \
    ListaDeRestriccionesDeVendedores, AccesoRestriccionesDeVendedores
from prospectos.tests.distribucion.pedidos.test_pedidos_core import PedidosTest
from testing.test_utils import reload_model
from vendedores.models import Vendedor


@override_settings(HORARIOS_LABORALES={'ENTRADA_DIA_SEMANA': time.min, 'SALIDA_DIA_SEMANA': time.max,
                                       'ENTRADA_SABADO': time.min, 'SALIDA_SABADO': time.max})
class MetodoDeAsignacionTest(PedidosTest):
    def setUp(self):
        super().setUp()
        Vendedor.objects.all().delete()
        self.supervisor_uno = self._crear_supervisor()
        self.vendedor_uno_de_supervisor_uno = self.creador_de_contexto.agregar_vendedor_a_supervisor(
            self.supervisor_uno)
        self.vendedor_dos_de_supervisor_uno = self.creador_de_contexto.agregar_vendedor_a_supervisor(
            self.supervisor_uno)
        self.vendedor_tres_de_supervisor_uno = self.creador_de_contexto.agregar_vendedor_a_supervisor(
            self.supervisor_uno)

        self.supervisor_dos = self._crear_supervisor()
        self.vendedor_uno_de_supervisor_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor(
            self.supervisor_dos)

    def test_asignar_directamente_a_vendedor_sin_restricciones(self):
        prospectos = self._prospectos_nuevos(cantidad=3)
        supervisor_uno = self._crear_supervisor(cantidad_de_vendedores=2)
        vendedor = supervisor_uno.vendedores.first()
        distribuidor = DistribucionDeProspectos.asignar_directa_vendedor(vendedor=vendedor)
        resultado = distribuidor.evaluar_para(prospectos)
        for prospecto in prospectos:
            self._assert_asignacion_de_prospecto(prospecto_id=prospecto.id,
                                                 a_vendedor=vendedor,
                                                 a_responsable=supervisor_uno)
            self.assertIsNone(prospecto.pedido)
        self._assert_resultado_de_entrega_a(cantidad=3, supervisor=supervisor_uno, resultado=resultado)

    def test_asignar_directamente_a_vendedor_sin_restricciones_y_debe_descontar_a_pedido(self):
        prospectos = self._prospectos_nuevos(cantidad=3)
        campania = prospectos[0].campania
        supervisor_uno = self._crear_supervisor(cantidad_de_vendedores=2)
        pedido = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_uno,
            campania=campania, consumido=0)
        vendedor = supervisor_uno.vendedores.first()
        distribuidor = DistribucionDeProspectos.asignar_directa_vendedor(vendedor=vendedor, descontar_a=[pedido])
        resultado = distribuidor.evaluar_para(prospectos)
        for prospecto in prospectos:
            prospecto_recargado = reload_model(prospecto)
            self._assert_asignacion_de_prospecto(prospecto_id=prospecto_recargado.id,
                                                 a_vendedor=vendedor,
                                                 a_responsable=supervisor_uno)
            self.assertEqual(prospecto_recargado.pedido, pedido)
        self._assert_resultado_de_entrega_a(cantidad=3, supervisor=supervisor_uno, resultado=resultado)
        self._assert_fue_registrado_consumo_y_resultado_para(pedido, prospectos, resultado=resultado)

    def test_asignar_directamente_a_vendedor_sin_restricciones_y_debe_descontar_a_pedidos_por_partes_iguales(self):
        prospectos = self._prospectos_nuevos(cantidad=4)
        campania = prospectos[0].campania
        supervisor_uno = self._crear_supervisor(cantidad_de_vendedores=2)
        pedido_uno = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_uno,
            campania=campania, consumido=0)
        pedido_dos = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_uno,
            campania=campania, consumido=0)
        vendedor = supervisor_uno.vendedores.first()
        distribuidor = DistribucionDeProspectos.asignar_directa_vendedor(vendedor=vendedor,
                                                                         descontar_a=[pedido_uno, pedido_dos])
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_vendedor_tiene(vendedor, cantidad_de_prospectos=4)
        self._assert_resultado_de_entrega_a(cantidad=4, supervisor=supervisor_uno, resultado=resultado)
        self._assert_fue_registrado_consumo_y_resultado_para(pedido_uno, prospectos[0:2], resultado=resultado)
        self._assert_fue_registrado_consumo_y_resultado_para(pedido_dos, prospectos[2:4], resultado=resultado)

    def test_asignar_directamente_a_vendedor_con_restriccion_datos_diarios_no_superada_debe_asignarse(self):
        prospectos = self._prospectos_nuevos(cantidad=5)
        supervisor_uno = self._crear_supervisor()
        vendedor = self._agregar_vendedor_a_(supervisor_uno, limite_de_datos_diarios=3)
        prospectos_nuevos_del_vendedor = 2
        self.creador_de_contexto.asignar_prospectos_nuevos_para(
            vendedores=[vendedor], cantidad=prospectos_nuevos_del_vendedor, supervisor=supervisor_uno)
        self._crear_supervisor(cantidad_de_vendedores=5)
        self._crear_supervisor(cantidad_de_vendedores=3)

        restricciones = ListaDeRestriccionesDeVendedores.con_filtros([RestriccionLimiteDatosDiario()])
        distribuidor = DistribucionDeProspectos.asignar_directa_vendedor(vendedor=vendedor,
                                                                         restricciones=restricciones)
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_vendedor_tiene(vendedor, cantidad_de_prospectos=prospectos_nuevos_del_vendedor + 1)
        self._assert_resultado_de_entrega_a(cantidad=1, supervisor=supervisor_uno, resultado=resultado)
        self._assert_prospectos_no_asignados_y_resultado(prospectos, cantidad=4, resultado=resultado)

    def test_asignar_directamente_a_vendedor_con_restriccion_datos_diarios_superada_no_debe_asignarse(self):
        self.creador_de_contexto.asignar_prospectos_nuevos_para(
            vendedores=[self.vendedor_uno_de_supervisor_uno], cantidad=2, supervisor=self.supervisor_uno)
        self._configurar_limite_de_datos_diarios_en_sistema(limite=2)
        prospectos = self._prospectos_nuevos(cantidad=1)
        restricciones = ListaDeRestriccionesDeVendedores.con_filtros([RestriccionLimiteDatosDiario()])
        distribuidor = DistribucionDeProspectos.asignar_directa_vendedor(vendedor=self.vendedor_uno_de_supervisor_uno,
                                                                         restricciones=restricciones)
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_prospectos_no_asignados_y_resultado(prospectos, cantidad=1, resultado=resultado)

    def test_asignar_directamente_a_equipo_sin_restricciones(self):
        prospectos = self._prospectos_nuevos(cantidad=4)
        supervisor_uno = self._crear_supervisor()
        vendedor_uno = self._agregar_vendedor_a_(supervisor_uno, limite_de_datos_diarios=1)
        vendedor_dos = self._agregar_vendedor_a_(supervisor_uno, limite_de_datos_diarios=5)
        equipo = self._crear_equipo_para(supervisor_uno, [vendedor_uno, vendedor_dos])

        distribuidor = DistribucionDeProspectos.asignar_directa_equipo(equipo=equipo)
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_vendedor_tiene(vendedor_uno, cantidad_de_prospectos=2)
        self._assert_vendedor_tiene(vendedor_dos, cantidad_de_prospectos=2)
        self._assert_resultado_de_entrega_a(4, supervisor_uno, resultado)

    def test_asignar_directamente_a_todos_sin_restricciones(self):
        prospectos = self._prospectos_nuevos(cantidad=4)
        supervisor_uno = self._crear_supervisor()
        vendedor_uno = self._agregar_vendedor_a_(supervisor_uno)
        vendedor_dos = self._agregar_vendedor_a_(supervisor_uno)

        distribuidor = DistribucionDeProspectos.asignar_directa_a_todos(supervisor=supervisor_uno)
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_vendedor_tiene(vendedor_uno, cantidad_de_prospectos=2)
        self._assert_vendedor_tiene(vendedor_dos, cantidad_de_prospectos=2)
        self._assert_resultado_de_entrega_a(4, supervisor_uno, resultado)

    def test_asignar_directamente_a_equipo_con_restriccion_de_datos_diarios(self):
        prospectos = self._prospectos_nuevos(cantidad=3)
        supervisor_uno = self._crear_supervisor()
        vendedor_uno = self._agregar_vendedor_a_(supervisor_uno, limite_de_datos_diarios=1)
        vendedor_dos = self._agregar_vendedor_a_(supervisor_uno, limite_de_datos_diarios=5)
        equipo = self._crear_equipo_para(supervisor_uno, [vendedor_uno, vendedor_dos])

        restricciones = ListaDeRestriccionesDeVendedores.con_filtros([RestriccionLimiteDatosDiario()])
        distribuidor = DistribucionDeProspectos.asignar_directa_equipo(equipo=equipo, restricciones=restricciones)
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_vendedor_tiene(vendedor_uno, cantidad_de_prospectos=1)
        self._assert_vendedor_tiene(vendedor_dos, cantidad_de_prospectos=2)
        self._assert_resultado_de_entrega_a(3, supervisor_uno, resultado)

    def test_poner_a_cargo_via_supervisores_sin_supervisores_no_deberia_ponerse_a_cargo(self):
        prospectos = self._prospectos_nuevos(cantidad=1)
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaSupervisores.entre(supervisores=Vendedor.objects.none(),
                                                     metodo=DistribucionPorFactor.uniforme()),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_prospectos_no_asignados_y_resultado(prospectos, cantidad=1, resultado=resultado)

    def test_poner_a_cargo_via_supervisores_con_distribucion_uniforme_sin_restricciones(self):
        """
            Dado 2 prospectos y 2 supervisores, debe poner a cargo un prospecto para cada supervisor
        """
        prospectos = self._prospectos_nuevos(cantidad=2)
        supervisor_uno = self._crear_supervisor()
        supervisor_dos = self._crear_supervisor()

        supervisores = Vendedor.objects.filter(id__in=[supervisor_uno.id, supervisor_dos.id])
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaSupervisores.entre(supervisores=supervisores,
                                                     metodo=DistribucionPorFactor.uniforme()),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_uno, cantidad=1, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_dos, cantidad=1, resultado=resultado)

    def test_poner_a_cargo_via_supervisores_con_distribucion_uniforme_con_restriccion_de_datos_diarios(self):
        """
            Dado 13 prospectos y 3 supervisores S1, S2 y S3 con restriccion de 1, 7 y 3 datos diarios respectivamente
              debe poner a cargo 1 prospecto a S1, 7 a S2 y 3 a S3 y 2 sin asignar.
        """
        prospectos = self._prospectos_nuevos(cantidad=13)
        supervisor_uno = self._crear_supervisor(limite_de_datos_diarios_al_supervisor=1)
        supervisor_dos = self._crear_supervisor(limite_de_datos_diarios_al_supervisor=7)
        supervisor_tres = self._crear_supervisor(limite_de_datos_diarios_al_supervisor=3)

        restricciones = ListaDeRestriccionesDeVendedores.con_filtros([RestriccionLimiteDatosDiario()])
        supervisores = Vendedor.objects.filter(id__in=[supervisor_uno.id, supervisor_dos.id, supervisor_tres.id])
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaSupervisores.entre(supervisores=supervisores,
                                                     metodo=DistribucionPorFactor.uniforme(),
                                                     lista_de_restricciones=restricciones),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_uno, cantidad=1, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_dos, cantidad=7, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_tres, cantidad=3, resultado=resultado)
        self._assert_prospectos_no_asignados_y_resultado(prospectos, cantidad=2, resultado=resultado)

    def test_poner_a_cargo_via_supervisores_con_distribucion_uniforme_con_restriccion_de_acceso(self):
        """
            Dado 6 prospectos y 3 supervisores S1, S2 y S3 con restriccion de acceso que no cumple S2
              debe poner a cargo 3 prospecto a S1, 0 a S2 y 3 a S3
        """
        prospectos = self._prospectos_nuevos(cantidad=6)
        supervisor_uno = self._crear_supervisor()
        supervisor_dos = self._crear_supervisor()
        supervisor_tres = self._crear_supervisor()
        self._configurar_ultima_actividad_excedida_a(supervisor_dos)
        restricciones = ListaDeRestriccionesDeVendedores.con_filtros([RestriccionPorUltimoAccesso()])
        supervisores = Vendedor.objects.filter(id__in=[supervisor_uno.id, supervisor_dos.id, supervisor_tres.id])
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaSupervisores.entre(supervisores=supervisores,
                                                     metodo=DistribucionPorFactor.uniforme(),
                                                     lista_de_restricciones=restricciones),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_uno, cantidad=3, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_dos, cantidad=0, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_tres, cantidad=3, resultado=resultado)

    def test_poner_a_cargo_via_supervisores_con_distribucion_por_administrador_sin_restricciones(self):
        """
            Dado 14 prospectos y 3 supervisores S1, S2 y S3
              debe poner a cargo 6 prospecto a S1, 2 a S2 y 6 a S3
        """
        prospectos = self._prospectos_nuevos(cantidad=14)
        supervisor_uno = self._crear_supervisor(ajuste_factor_de_asignacion_administrador=3)
        supervisor_dos = self._crear_supervisor(ajuste_factor_de_asignacion_administrador=1)
        supervisor_tres = self._crear_supervisor(ajuste_factor_de_asignacion_administrador=3)
        supervisores = Vendedor.objects.filter(id__in=[supervisor_uno.id, supervisor_dos.id, supervisor_tres.id])
        metodo = DistribucionPorFactor.para(MetodoDeSeleccionPorFactorAdministrador())
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaSupervisores.entre(supervisores=supervisores, metodo=metodo)
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_uno, cantidad=6, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_dos, cantidad=2, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_tres, cantidad=6, resultado=resultado)

    def test_poner_a_cargo_via_supervisores_con_descuento_de_credito_debe_descontarse(self):
        """
            Dado 2 prospectos y 2 supervisores, debe poner a cargo un prospecto para cada supervisor
        """
        prospectos = self._prospectos_nuevos(cantidad=2)
        supervisor_uno = self._crear_supervisor()
        supervisor_dos = self._crear_supervisor()

        pedido_uno = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_uno, campania=self.campania_uno, consumido=0)

        pedido_dos = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_dos, campania=self.campania_uno, consumido=0)

        supervisores = Vendedor.objects.filter(id__in=[supervisor_uno.id, supervisor_dos.id])
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaSupervisores.entre(supervisores=supervisores,
                                                     metodo=DistribucionPorFactor.uniforme(),
                                                     debe_registrar_consumo=True),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_uno, cantidad=1, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_dos, cantidad=1, resultado=resultado)
        self._assert_fue_registrado_consumo_y_resultado_para(
            pedido_uno, supervisor_uno.prospectos_a_cargo.all(), resultado)
        self._assert_fue_registrado_consumo_y_resultado_para(
            pedido_dos, supervisor_dos.prospectos_a_cargo.all(), resultado)

    def test_poner_a_cargo_via_supervisores_con_descuento_de_credito_sin_pedidos_no_debe_descontarse(self):
        """
            Dado 2 prospectos y 2 supervisores, debe poner a cargo un prospecto para cada supervisor
        """
        prospectos = self._prospectos_nuevos(cantidad=2)
        supervisor_uno = self._crear_supervisor()
        supervisor_dos = self._crear_supervisor()

        supervisores = Vendedor.objects.filter(id__in=[supervisor_uno.id, supervisor_dos.id])
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaSupervisores.entre(supervisores=supervisores,
                                                     metodo=DistribucionPorFactor.uniforme(),
                                                     debe_registrar_consumo=True),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_uno, cantidad=1, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_dos, cantidad=1, resultado=resultado)
        self._assert_prospectos_sin_descuento_de_credito(prospectos)

    def test_poner_a_cargo_via_pedidos_sin_pedidos_no_deberia_ponerse_a_cargo(self):
        prospectos = self._prospectos_nuevos(cantidad=1)
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaPedidos.para(pedidos=PedidoDeProspecto.objects.none()),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_prospectos_no_asignados_y_resultado(prospectos, cantidad=1, resultado=resultado)

    def test_poner_a_cargo_via_pedidos_sin_pedidos_a_cargo_no_deberia_ponerse_a_cargo(self):
        prospectos = self._prospectos_nuevos(cantidad=5)
        prospecto = prospectos[0]
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            vendedor=self.vendedor_uno_de_supervisor_uno, supervisor=self.supervisor_uno,
            campania=prospecto.campania, consumido=1)

        pedidos = PedidoDeProspecto.objects.filter(pk=pedido.pk)
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaPedidos.para(pedidos=pedidos),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_prospectos_no_asignados(prospectos, cantidad=5)
        self._assert_tiene_prospectos_a_cargo_y_resultado(self.supervisor_uno, cantidad=0, resultado=resultado)
        self._assert_cantidad_no_entregada(resultado, cantidad=5)

    def test_poner_a_cargo_via_pedidos_sin_restricciones(self):
        prospectos = self._prospectos_nuevos(cantidad=5)
        prospecto = prospectos[0]
        pedido = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=self.supervisor_uno, campania=prospecto.campania, consumido=1)

        pedidos = PedidoDeProspecto.objects.filter(pk=pedido.pk)
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaPedidos.para(pedidos=pedidos),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_tiene_prospectos_a_cargo_y_resultado(self.supervisor_uno, cantidad=5, resultado=resultado)
        self.assertIsNone(prospecto.pedido)

    def test_poner_a_cargo_via_pedidos_sin_restricciones_con_descuento_debe_asignar_y_descontar(self):
        prospectos = self._prospectos_nuevos(cantidad=5)
        prospecto = prospectos[0]
        pedido = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=self.supervisor_uno, campania=prospecto.campania, consumido=0)

        pedidos = PedidoDeProspecto.objects.filter(pk=pedido.pk)
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaPedidos.para(pedidos=pedidos, debe_registrar_consumo=True),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_tiene_prospectos_a_cargo_y_resultado(self.supervisor_uno, cantidad=5, resultado=resultado)
        self._assert_fue_registrado_consumo_y_resultado_para(pedido, prospectos, resultado)

    def test_poner_a_cargo_via_un_pedido_con_restriccion_de_datos_diarios(self):
        supervisor = self._crear_supervisor(limite_de_datos_diarios_al_supervisor=1, cantidad_de_vendedores=2)
        cantidad_inicial = 1
        self.creador_de_contexto.poner_a_cargo_prospectos_nuevos(supervisor=supervisor, cantidad=cantidad_inicial,
                                                                 proveedor='pprod')
        prospectos = self._prospectos_nuevos(cantidad=1)
        campania = prospectos[0].campania
        pedido = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor,
            campania=campania, consumido=1)

        pedidos = PedidoDeProspecto.objects.filter(pk=pedido.pk)
        restricciones = ListaDeRestriccionesDeVendedores.con_filtros([RestriccionLimiteDatosDiario()])
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaPedidos.para(
                pedidos=pedidos,
                acceso_a_restricciones=restricciones.convertir_a_acceso_preconfigurado()),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_tiene_prospectos_a_cargo(supervisor, cantidad=cantidad_inicial)
        self._assert_resultado_de_entrega_a(cantidad=0, resultado=resultado, supervisor=supervisor)
        self._assert_prospectos_no_asignados_y_resultado(prospectos, cantidad=1, resultado=resultado)

    def test_poner_a_cargo_via_pedidos_para_todo_los_supervisores_con_restriccion_de_datos_diarios(self):
        prospectos = self._prospectos_nuevos(cantidad=13)
        supervisor_uno = self._crear_supervisor(limite_de_datos_diarios_al_supervisor=1)
        supervisor_dos = self._crear_supervisor(limite_de_datos_diarios_al_supervisor=4)
        supervisor_tres = self._crear_supervisor(limite_de_datos_diarios_al_supervisor=6)

        campania = prospectos[0].campania
        pedido_uno = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_uno,
            campania=campania, consumido=1)
        pedido_dos = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_dos,
            campania=campania, consumido=1)
        pedido_tres = self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_tres,
            campania=campania, consumido=1)
        pedidos = PedidoDeProspecto.objects.filter(pk__in=[pedido_uno.pk, pedido_dos.pk, pedido_tres.pk])
        restricciones = ListaDeRestriccionesDeVendedores.con_filtros([RestriccionLimiteDatosDiario()])
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaPedidos.para(
                pedidos=pedidos,
                acceso_a_restricciones=restricciones.convertir_a_acceso_preconfigurado()),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_uno, cantidad=1, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_dos, cantidad=4, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_tres, cantidad=6, resultado=resultado)
        self._assert_prospectos_no_asignados_y_resultado(prospectos, cantidad=2, resultado=resultado)

    def test_poner_a_cargo_via_pedidos_para_lista_de_supervisores_con_restriccion_por_datos_diarios(self):
        prospectos = self._prospectos_nuevos(cantidad=13)
        supervisor_uno = self._crear_supervisor(limite_de_datos_diarios_al_supervisor=1)
        supervisor_dos = self._crear_supervisor(limite_de_datos_diarios_al_supervisor=4)
        supervisor_tres = self._crear_supervisor(limite_de_datos_diarios_al_supervisor=6)

        campania = prospectos[0].campania
        self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_uno,
            campania=campania, consumido=1)
        self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_dos,
            campania=campania, consumido=1)
        self.creador_de_contexto.agregar_pedido_para_poner_a_cargo(
            supervisor=supervisor_tres,
            campania=campania, consumido=1)

        restricciones = ListaDeRestriccionesDeVendedores.con_filtros([RestriccionLimiteDatosDiario()])
        distribuidor = DistribucionDeProspectos.poner_a_cargo(
            seleccion=SeleccionViaPedidos.de_supervisores(
                supervisores=[supervisor_uno, supervisor_dos],
                acceso_a_restricciones=restricciones.convertir_a_acceso_preconfigurado()),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_uno, cantidad=1, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_dos, cantidad=4, resultado=resultado)
        self._assert_tiene_prospectos_a_cargo_y_resultado(supervisor_tres, cantidad=0, resultado=resultado)
        self._assert_prospectos_no_asignados_y_resultado(prospectos, cantidad=8, resultado=resultado)

    def test_asignar_vendedor_via_supervisores_con_seleccion_uniforme_de_vendedor_sin_restricciones(self):
        """
            Dado 6 prospectos y 3 supervisores S1, S2 y S3 con 2, 5 y 3 vendedores a cargo
              debe asignarsele 2 prospecto a dos vendedores de S1, 2 a dos vendedores de S2 y
              2 a un vendedor de S3
        """

        prospectos = self._prospectos_nuevos(cantidad=6)
        supervisor_uno = self._crear_supervisor(cantidad_de_vendedores=2)
        supervisor_dos = self._crear_supervisor(cantidad_de_vendedores=5)
        supervisor_tres = self._crear_supervisor(cantidad_de_vendedores=3)

        supervisores = Vendedor.objects.filter(id__in=[supervisor_uno.id, supervisor_dos.id, supervisor_tres.id])
        distribuidor = DistribucionDeProspectos.asignar_vendedor_via_supervisores(
            supervisores=supervisores,
            tipo_de_seleccion_de_vendedor=SeleccionDeTodos,
            metodo_de_seleccion=MetodoDeSeleccionUniforme())
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_vendedores_tienen(supervisor_uno, cantidad_de_vendedores=2, cantidad_de_prospectos=1)
        self._assert_vendedores_tienen(supervisor_dos, cantidad_de_vendedores=2, cantidad_de_prospectos=1)
        self._assert_vendedores_tienen(supervisor_dos, cantidad_de_vendedores=3, cantidad_de_prospectos=0)
        self._assert_vendedores_tienen(supervisor_tres, cantidad_de_vendedores=2, cantidad_de_prospectos=1)
        self._assert_vendedores_tienen(supervisor_tres, cantidad_de_vendedores=1, cantidad_de_prospectos=0)
        self._assert_resultado_de_entrega_a(cantidad=2, supervisor=supervisor_uno, resultado=resultado)
        self._assert_resultado_de_entrega_a(cantidad=2, supervisor=supervisor_dos, resultado=resultado)
        self._assert_resultado_de_entrega_a(cantidad=2, supervisor=supervisor_tres, resultado=resultado)

    def test_asignar_vendedor_via_supervisores_con_seleccion_via_factor_de_vendedor_sin_restricciones(self):
        """
            Dado 6 prospectos y 3 supervisores S1, S2 y S3 con 2, 2 y 3 vendedores a cargo,
              con factores 2 y 1 para los de S1,
              debe asignarsele 2 prospecto a dos vendedores de S1, 2 a dos vendedores de S2 y
              2 a dos vendedores de S3
        """

        prospectos = self._prospectos_nuevos(cantidad=6)
        supervisor_uno = self._crear_supervisor(cantidad_de_vendedores=2, con_factor=[2, 1])
        supervisor_dos = self._crear_supervisor(cantidad_de_vendedores=2, con_factor=[5, 1])
        supervisor_tres = self._crear_supervisor(cantidad_de_vendedores=3, con_factor=[1, 0, 0])

        supervisores = Vendedor.objects.filter(id__in=[supervisor_uno.id, supervisor_dos.id, supervisor_tres.id])
        distribuidor = DistribucionDeProspectos.asignar_vendedor_via_supervisores(
            supervisores=supervisores,
            tipo_de_seleccion_de_vendedor=SeleccionDeTodos,
            metodo_de_seleccion=MetodoDeSeleccionPorFactorManual())
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_vendedores_tienen(supervisor_uno, cantidad_de_vendedores=2, cantidad_de_prospectos=1)
        self._assert_vendedores_tienen(supervisor_dos, cantidad_de_vendedores=1, cantidad_de_prospectos=2)
        self._assert_vendedores_tienen(supervisor_tres, cantidad_de_vendedores=1, cantidad_de_prospectos=2)
        self._assert_resultado_de_entrega_a(cantidad=2, supervisor=supervisor_uno, resultado=resultado)
        self._assert_resultado_de_entrega_a(cantidad=2, supervisor=supervisor_dos, resultado=resultado)
        self._assert_resultado_de_entrega_a(cantidad=2, supervisor=supervisor_tres, resultado=resultado)

    def test_asignar_vendedor_via_supervisores_con_seleccion_uniforme_de_vendedor_con_restricciones(self):
        """
            Dado 5 prospectos y 3 supervisores S1, S2 y S3 con 1, 5 y 3 vendedores a cargo.
              Debe asignarsele 1 prospecto al vendedor de S1 (por la restriccion), 2 a dos vendedores de S2 y
              2 a dos vendedores de S3
        """
        prospectos = self._prospectos_nuevos(cantidad=5)
        supervisor_uno = self._crear_supervisor()
        self._agregar_vendedor_a_(supervisor_uno, limite_de_datos_nuevos=1)
        supervisor_dos = self._crear_supervisor(cantidad_de_vendedores=5)
        supervisor_tres = self._crear_supervisor(cantidad_de_vendedores=3)

        restricciones = ListaDeRestriccionesDeVendedores.con_filtros([RestriccionLimiteDatosNuevos()])
        supervisores = Vendedor.objects.filter(id__in=[supervisor_uno.id, supervisor_dos.id, supervisor_tres.id])
        distribuidor = DistribucionDeProspectos.asignar_vendedor_via_supervisores(
            supervisores=supervisores,
            tipo_de_seleccion_de_vendedor=SeleccionDeTodos,
            metodo_de_seleccion=MetodoDeSeleccionUniforme(),
            restricciones=restricciones)
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_vendedores_tienen(supervisor_uno, cantidad_de_vendedores=1, cantidad_de_prospectos=1)
        self._assert_vendedores_tienen(supervisor_dos, cantidad_de_vendedores=2, cantidad_de_prospectos=1)
        self._assert_vendedores_tienen(supervisor_dos, cantidad_de_vendedores=3, cantidad_de_prospectos=0)
        self._assert_vendedores_tienen(supervisor_tres, cantidad_de_vendedores=2, cantidad_de_prospectos=1)
        self._assert_vendedores_tienen(supervisor_tres, cantidad_de_vendedores=1, cantidad_de_prospectos=0)
        self._assert_resultado_de_entrega_a(cantidad=1, supervisor=supervisor_uno, resultado=resultado)
        self._assert_resultado_de_entrega_a(cantidad=2, supervisor=supervisor_dos, resultado=resultado)
        self._assert_resultado_de_entrega_a(cantidad=2, supervisor=supervisor_tres, resultado=resultado)

    def test_asignar_vendedor_via_supervisores_con_supervisor_sin_vendedores(self):
        prospectos = self._prospectos_nuevos(cantidad=6)
        supervisor_uno = self._crear_supervisor(cantidad_de_vendedores=2)
        supervisor_dos = self._crear_supervisor(cantidad_de_vendedores=0)
        supervisor_tres = self._crear_supervisor(cantidad_de_vendedores=3)

        supervisores = Vendedor.objects.filter(id__in=[supervisor_uno.id, supervisor_dos.id, supervisor_tres.id])
        distribuidor = DistribucionDeProspectos.asignar_vendedor_via_supervisores(
            supervisores=supervisores,
            tipo_de_seleccion_de_vendedor=SeleccionDeTodos,
            metodo_de_seleccion=MetodoDeSeleccionUniforme())
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_vendedores_tienen(supervisor_uno, cantidad_de_vendedores=1, cantidad_de_prospectos=2)
        self._assert_vendedores_tienen(supervisor_uno, cantidad_de_vendedores=1, cantidad_de_prospectos=1)
        self._assert_vendedores_tienen(supervisor_tres, cantidad_de_vendedores=3, cantidad_de_prospectos=1)
        self._assert_resultado_de_entrega_a(cantidad=3, supervisor=supervisor_uno, resultado=resultado)
        self._assert_resultado_de_entrega_a(cantidad=0, supervisor=supervisor_dos, resultado=resultado)
        self._assert_resultado_de_entrega_a(cantidad=3, supervisor=supervisor_tres, resultado=resultado)

    def test_asignar_vendedor_via_supervisores_con_seleccion_por_porductivdad_con_supervisor_sin_vendedores(self):
        prospectos = self._prospectos_nuevos(cantidad=6)
        supervisor_uno = self._crear_supervisor(cantidad_de_vendedores=2)
        supervisor_dos = self._crear_supervisor(cantidad_de_vendedores=0)
        supervisor_tres = self._crear_supervisor(cantidad_de_vendedores=3)

        supervisores = Vendedor.objects.filter(id__in=[supervisor_uno.id, supervisor_dos.id, supervisor_tres.id])
        distribuidor = DistribucionDeProspectos.asignar_vendedor_via_supervisores(
            supervisores=supervisores,
            tipo_de_seleccion_de_vendedor=SeleccionDeTodos,
            metodo_de_seleccion=MetodoDeSeleccionUniforme() + MetodoDeSeleccionPorFactorAsistido.default())
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_vendedores_tienen(supervisor_uno, cantidad_de_vendedores=1, cantidad_de_prospectos=2)
        self._assert_vendedores_tienen(supervisor_uno, cantidad_de_vendedores=1, cantidad_de_prospectos=1)
        self._assert_vendedores_tienen(supervisor_tres, cantidad_de_vendedores=3, cantidad_de_prospectos=1)
        self._assert_resultado_de_entrega_a(cantidad=3, supervisor=supervisor_uno, resultado=resultado)
        self._assert_resultado_de_entrega_a(cantidad=0, supervisor=supervisor_dos, resultado=resultado)
        self._assert_resultado_de_entrega_a(cantidad=3, supervisor=supervisor_tres, resultado=resultado)

    def test_asignar_vendedor_via_pedidos_sin_restricciones(self):
        prospectos = self._prospectos_nuevos(cantidad=6)
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor_uno, campania=self.campania_uno, consumido=0)

        pedidos = PedidoDeProspecto.objects.filter(pk=pedido.pk)
        distribuidor = DistribucionDeProspectos.asignar_vendedor(
            seleccion=SeleccionViaPedidos.para(pedidos=pedidos,
                                               debe_registrar_consumo=False)
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_vendedor_tiene(self.vendedor_uno_de_supervisor_uno, cantidad_de_prospectos=2)
        self._assert_vendedor_tiene(self.vendedor_dos_de_supervisor_uno, cantidad_de_prospectos=2)
        self._assert_vendedor_tiene(self.vendedor_tres_de_supervisor_uno, cantidad_de_prospectos=2)
        self._assert_no_fue_registrado_consumo_para(pedido, prospectos)
        self._assert_resultado_de_entrega_a(cantidad=6, supervisor=self.supervisor_uno, resultado=resultado)

    def test_asignar_vendedor_via_pedidos_sin_restricciones_con_descuento_debe_asignar_y_descontar(self):
        prospectos = self._prospectos_nuevos(cantidad=6)
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor_uno, campania=self.campania_uno, consumido=0)

        pedidos = PedidoDeProspecto.objects.filter(pk=pedido.pk)
        distribuidor = DistribucionDeProspectos.asignar_vendedor(
            seleccion=SeleccionViaPedidos.para(pedidos=pedidos, debe_registrar_consumo=True)
        )
        resultado = distribuidor.evaluar_para(prospectos)
        self._assert_vendedor_tiene(self.vendedor_uno_de_supervisor_uno, cantidad_de_prospectos=2)
        self._assert_vendedor_tiene(self.vendedor_dos_de_supervisor_uno, cantidad_de_prospectos=2)
        self._assert_vendedor_tiene(self.vendedor_tres_de_supervisor_uno, cantidad_de_prospectos=2)
        self._assert_resultado_de_entrega_a(cantidad=6, supervisor=self.supervisor_uno, resultado=resultado)
        self._assert_fue_registrado_consumo_y_resultado_para(pedido, prospectos, resultado)

    def test_asignar_vendedor_via_pedidos_con_restriccion_de_datos_nuevos(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor_uno,
            metodo_de_asignacion=MetodosDeAsignacionChoices.UNIFORME,
            restringir_por_datos_nuevos=False,
            consumido=1)
        self.creador_de_contexto.asignar_prospectos_nuevos_para(vendedores=[
            self.vendedor_uno_de_supervisor_uno, self.vendedor_dos_de_supervisor_uno,
            self.vendedor_tres_de_supervisor_uno],
            cantidad=2, supervisor=self.supervisor_uno)
        self._configurar_limite_de_datos_nuevos_en_sistema(limite=2)
        self._configurtar_limite_de_datos_nuevos_para(self.vendedor_tres_de_supervisor_uno, limite=4)
        prospectos = self._prospectos_nuevos(cantidad=1)
        pedidos = PedidoDeProspecto.objects.filter(pk=pedido.pk)
        restricciones = ListaDeRestriccionesDeVendedores.con_filtros([RestriccionLimiteDatosNuevos()])
        acceso_a_restricciones = AccesoRestriccionesDeVendedores.preconfigurado_con(restricciones)
        distribuidor = DistribucionDeProspectos.asignar_vendedor(
            seleccion=SeleccionViaPedidos.para(pedidos=pedidos,
                                               debe_registrar_consumo=False,
                                               acceso_a_restricciones=acceso_a_restricciones)
        )
        resultado = distribuidor.evaluar_para(prospectos)
        prospecto = prospectos[0]
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(prospecto_id=prospecto.id,
                                                                 en_pedido_id=pedido.id,
                                                                 a_vendedor=self.vendedor_tres_de_supervisor_uno,
                                                                 a_responsable=self.supervisor_uno,
                                                                 credito_consumido=1,
                                                                 validar_pedido=False)
        self.assertIsNone(prospecto.pedido)
        self._assert_resultado_de_entrega_a(cantidad=1, supervisor=self.supervisor_uno, resultado=resultado)

    def test_asignar_vendedor_via_pedidos_con_restriccion_tomadas_desde_el_pedido(self):
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor_uno,
            metodo_de_asignacion=MetodosDeAsignacionChoices.UNIFORME,
            restringir_por_datos_nuevos=True,
            consumido=1)
        self.creador_de_contexto.asignar_prospectos_nuevos_para(vendedores=[
            self.vendedor_uno_de_supervisor_uno, self.vendedor_dos_de_supervisor_uno,
            self.vendedor_tres_de_supervisor_uno],
            cantidad=2, supervisor=self.supervisor_uno)

        self._configurar_limite_de_datos_nuevos_en_sistema(2)
        self._configurtar_limite_de_datos_nuevos_para(self.vendedor_tres_de_supervisor_uno, 4)
        prospectos = self._prospectos_nuevos(cantidad=1)
        pedidos = PedidoDeProspecto.objects.filter(pk=pedido.pk)
        acceso_a_restricciones = AccesoRestriccionesDeVendedores.desde_pedido()
        distribuidor = DistribucionDeProspectos.asignar_vendedor(
            seleccion=SeleccionViaPedidos.para(pedidos=pedidos,
                                               debe_registrar_consumo=False,
                                               acceso_a_restricciones=acceso_a_restricciones),
        )
        resultado = distribuidor.evaluar_para(prospectos)
        prospecto = prospectos[0]
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(prospecto_id=prospecto.id,
                                                                 en_pedido_id=pedido.id,
                                                                 a_vendedor=self.vendedor_tres_de_supervisor_uno,
                                                                 a_responsable=self.supervisor_uno,
                                                                 credito_consumido=1,
                                                                 validar_pedido=False)
        self.assertIsNone(prospecto.pedido)
        self._assert_resultado_de_entrega_a(cantidad=1, supervisor=self.supervisor_uno, resultado=resultado)

    def test_si_los_prospectos_a_asignar_estan_lockeados_responde_un_resultado_erroneo(self):
        #Dado
        lockeo = Locker.new_for_group('asignacion_de_prospectos')
        prospectos_a_asignar = self._prospectos_nuevos(cantidad=1)
        recursos_lockeados = [prospecto.id for prospecto in prospectos_a_asignar]
        distribuidor = DistribucionDeProspectos.asignar_directa_vendedor(vendedor=self.vendedor_uno_de_supervisor_uno)
        try:
            lockeo.lock_each_resource_of(recursos_lockeados)

            #Cuando
            resultado = distribuidor.evaluar_para(prospectos_a_asignar)

            #Entonces
            self._assert_cantidad_no_entregada(resultado, 1)
            self.assertEqual(resultado.detalle(), "Ya se esta ejecutando una asignacion. Intente de"
                                                  " nuevo en unos minutos")
        finally:
            lockeo.unlock_each_resource_of(recursos_lockeados)

    def test_con_un_solo_de_los_prospectos_a_asignar_lockeado_responde_un_resultado_erroneo(self):
        #Dado
        lockeo = Locker.new_for_group('asignacion_de_prospectos')
        prospectos_a_asignar = self._prospectos_nuevos(cantidad=2)
        recursos_lockeados = [prospectos_a_asignar.first().id]
        distribuidor = DistribucionDeProspectos.asignar_directa_vendedor(vendedor=self.vendedor_uno_de_supervisor_uno)
        try:
            lockeo.lock_each_resource_of(recursos_lockeados)

            #Cuando
            resultado = distribuidor.evaluar_para(prospectos_a_asignar)

            #Entonces
            self._assert_cantidad_no_entregada(resultado, 2)
            self.assertEqual(resultado.detalle(), "Ya se esta ejecutando una asignacion. Intente de"
                                                  " nuevo en unos minutos")
        finally:
            lockeo.unlock_each_resource_of(recursos_lockeados)

    def _assert_no_fue_registrado_consumo_para(self, pedido, prospectos):
        pedido_recargado = reload_model(pedido)
        ids = prospectos.values_list('id', flat=True)
        self.assertFalse(pedido_recargado.prospectos_asignados.filter(pk__in=ids).exists())
        self.assertEqual(pedido_recargado.consumido, 0)

    def _assert_tiene_prospectos_a_cargo_y_resultado(self, supervisor_uno, cantidad, resultado):
        self._assert_tiene_prospectos_a_cargo(supervisor_uno, cantidad)
        self._assert_resultado_de_entrega_a(cantidad, supervisor_uno, resultado)

    def _assert_resultado_de_entrega_a(self, cantidad, supervisor, resultado):
        cantidad_notificada = resultado.cantidad_entregrada_a(supervisor)
        self.assertEqual(
            cantidad_notificada, cantidad,
            "Se espera que la cantidad notificada sea %s, en su lugar se obtuvo %s" % (cantidad, cantidad_notificada))

    def _assert_prospectos_no_asignados_y_resultado(self, prospectos, cantidad, resultado):
        self._assert_prospectos_no_asignados(prospectos, cantidad)
        self._assert_cantidad_no_entregada(resultado, cantidad)

    def _assert_cantidad_no_entregada(self, resultado, cantidad):
        self.assertEqual(resultado.cantidad_no_entregada(), cantidad)

    def _assert_fue_registrado_consumo_y_resultado_para(self, pedido, prospectos, resultado):
        self._assert_fue_registrado_consumo_para(pedido, prospectos)
        consumo = self._consumo_de(prospectos)
        self.assertEqual(resultado.cantidad_consumida_para(pedido), consumo)
