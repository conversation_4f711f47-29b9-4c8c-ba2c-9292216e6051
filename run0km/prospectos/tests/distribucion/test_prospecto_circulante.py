import datetime
from datetime import timedelta

from dateutil.relativedelta import relativedelta
from django.utils import timezone
from freezegun import freeze_time

from prospectos.models import Prospecto
from prospectos.models.gestor.circulacion_de_prospectos.circulador_de_prospectos import CirculadorDeProspectos
from prospectos.models.gestor.circulacion_de_prospectos.estrategia_predefinida_de_priorizacion_de_prospectos import \
    EstrategiaPredefinidaDePriorizacionDeProspectos
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from testing.base import BaseFixturedTest
from testing.test_utils import reload_model


class ProspectoCirculanteTest(BaseFixturedTest):

    def setUp(self):
        super().setUp()
        Prospecto.objects.all().delete()
        self.vendedor_uno = self.fixture['vend_1']
        self.vendedor_dos = self.fixture['vend_2']
        self.vendedor_cinco = self.fixture['vend_5']
        self.supervisor_uno = self.fixture['sup_1']
        self.campania_uno = self.fixture['camp_1']
        self._configurar_en_0_los_minutos_de_inactividad_maximos_para_circular_prospectos_supervisor_uno()
        self._configurar_a_todos_los_vendedores_ultimo_acceso_al_listado_de_prospectos()
        self._configurar_tiempo_de_inactividad_1_minuto_para_todos_los_vendedores()

    def test_un_prospecto_se_le_reasigna_a_otro_vendedor_del_pedido(self):
        # Dado
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
        self._configurar_ultimo_acceso_al_listado_de_prospectos_previo_a_la_creacion_de(prospecto)

        # Cuando
        circulaciones = circulador_de_prospectos.circular()


        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_cinco)
        self.assertEqual(len(circulaciones), 1)
        self._assert_la_circulacion_es_desde_el_vendedor_uno_al_cinco(circulacion=circulaciones[0], prospecto_circulado=prospecto)

    def test_todos_los_vendedores_tienen_datos_nuevos_el_prospecto_no_se_reasigna(self):
        # Dado
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto_uno = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
        prospecto_dos = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_dos)
        prospecto_tres = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_cinco)
        circulador_de_prospectos = self._circulador_con_estrategia_de_priorizacion_predefinida(
            [prospecto_uno, prospecto_dos, prospecto_tres])

        # Cuando
        circulaciones = circulador_de_prospectos.circular()

        # Entonces
        self.assertEqual(len(circulaciones), 0)
        self._assert_tiene_como_vendedor_a(prospecto_uno, self.vendedor_uno)
        self._assert_tiene_como_vendedor_a(prospecto_dos, self.vendedor_dos)
        self._assert_tiene_como_vendedor_a(prospecto_tres, self.vendedor_cinco)

    def test_todos_los_vendedores_candidatos_tienen_limite_datos_nuevos_en_0_el_prospecto_no_se_reasigna(self):
        # Dado
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        self._configurar_limites_datos_nuevos_donde_solo_el_vendedor_uno_es_mayor_a_0()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto_uno = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto_uno, self.vendedor_uno)

    def test_todos_los_vendedores_candidatos_tienen_restriccion_limite_datos_diarios_el_prospecto_no_se_reasigna(self):
        # Dado
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto_uno = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
        self._restringir_los_vendedores_por_limite_diario_para(pedido)

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto_uno, self.vendedor_uno)

    def test_el_pedido_esta_finalizado_el_prospecto_no_se_reasigna(self):
        # Dado
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10, finalizado=True)
        prospecto_uno = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto_uno, self.vendedor_uno)

    def test_todos_los_vendedores_candidatos_tienen_un_tiempo_de_inactividad_de_20_minutos_o_mas_el_prospecto_no_se_reasigna(self):
        with freeze_time("2025-05-26 12:30:00") as tiempo_freezado:
            # Dado
            circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
            pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                       credito=10, consumido=0)
            self.vendedor_uno.actualizar_ultimo_acceso_al_listado_de_prospectos()
            tiempo_freezado.tick(timedelta(seconds=1))
            prospecto_uno = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
            tiempo_freezado.tick(timedelta(minutes=5))
            self._configurar_tiempo_de_inactividad_donde_solo_el_vendedor_uno_es_menor_a_20()

            # Cuando
            circulador_de_prospectos.circular()

            # Entonces
            self._assert_tiene_como_vendedor_a(prospecto_uno, self.vendedor_uno)

    def test_todos_los_vendedores_candidatos_estan_desahabilidados_el_prospecto_no_se_reasigna(self):
        # Dado
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=0)
        prospecto_uno = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
        self.vendedor_dos.deshabilitar()
        self.vendedor_cinco.deshabilitar()

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto_uno, self.vendedor_uno)

    def test_los_prospectos_circulantes_son_asignados_segun_estrategia_de_priorizacion(self):
        # Dado
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto_uno = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
        prospecto_dos = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_dos)
        circulador_de_prospectos = self._circulador_con_estrategia_de_priorizacion_predefinida(
            [prospecto_dos, prospecto_uno])

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto=prospecto_dos, vendedor=self.vendedor_cinco)

    def test_si_un_prospecto_circula_el_vendedor_inicial_no_debe_debe_ser_vendedor_candidato_para_otro_prospecto(self):
        # Dado
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto_uno = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
        prospecto_dos = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_dos)
        circulador_de_prospectos = self._circulador_con_estrategia_de_priorizacion_predefinida(
            [prospecto_uno, prospecto_dos])

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto_uno, self.vendedor_cinco)
        self._assert_tiene_como_vendedor_a(prospecto_dos, self.vendedor_dos)

    def test_si_prospecto_no_esta_asignado_a_ningun_vendedor_no_se_reasigna(self):
        # Dado
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        prospecto = self.creador_de_contexto.crear_prospecto_nuevo()

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        prospecto = reload_model(prospecto)
        self.assertIsNone(prospecto.obtener_vendedor()) #No se reasigno

    def test_el_prospecto_tiene_antiguedad_mayor_a_la_seteada_en_configuracion_supervisor_no_se_reasigna(self):
        with freeze_time("2024-11-30 09:00:00") as tiempo_freezado:
            # Dado
            self._configurar_los_dias_para_atender_prospectos_del_supervisor_en(dias=1)

            circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
            pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                       credito=10, consumido=10)

            prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=self.vendedor_uno)
            prospecto.asignar_pedido(pedido=pedido)
            self._avanzar_tiempo_hasta_dias_para_atender_prospectos_del_supervisor(tiempo_freezado)

            # Cuando
            circulador_de_prospectos.circular()

            # Entonces
            self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)

    def test_el_prospecto_tiene_pedido_con_integracion_con_otro_crm_no_se_reasigna(self):
        # Dado

        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        self.creador_de_contexto.agregar_configuracion_maipu_para(pedido=pedido, descripcion="Lorem Ipsum")
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=self.vendedor_uno)
        prospecto.asignar_pedido(pedido=pedido)

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)

    def test_si_la_campania_del_prospecto_no_permite_circular_prospecto_no_se_reasigna(self):
        # Dado
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=self.vendedor_uno)
        self._configurar_campania_para_no_poder_circular_prospecto(prospecto=prospecto, permite_circular_prospectos=False)
        prospecto.asignar_pedido(pedido=pedido)

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)

    def test_el_prospecto_con_antiguedad_mayor_a_la_maxima_no_se_reasigna(self):
        with freeze_time("2024-01-17 09:00:00") as tiempo_freezado:
            # Dado
            antiguedad_maxima_de_prospecto_en_dias = 365
            self._configurar_los_dias_para_atender_prospectos_del_supervisor_en(dias=antiguedad_maxima_de_prospecto_en_dias + 10)
            circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
            pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                       credito=10, consumido=10)
            prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
            tiempo_freezado.tick(timedelta(days=antiguedad_maxima_de_prospecto_en_dias + 1))

            # Cuando
            circulador_de_prospectos.circular()

            # Entonces
            self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)

    def test_el_vendedor_posee_un_prospecto_no_tiene_permiso_para_circular_prospecto_no_se_reasigna(self):
        # Dado
        self._configurar_circular_prospecto_deshabilitado_a(vendedor=self.vendedor_uno)
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)

    def test_el_vendedor_candidato_no_tiene_permiso_para_circular_prospecto_no_se_reasigna_a_ese_vendedor(self):
        # Dado
        self._configurar_circular_prospecto_deshabilitado_a(vendedor=self.vendedor_dos)
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_cinco)

    def test_el_supervisor_no_tiene_permiso_para_circular_prospecto_no_se_reasigna(self):
        # Dado
        self._configurar_circular_prospecto_deshabilitado_a(vendedor=self.supervisor_uno)
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)

    def test_la_concesionaria_no_tiene_permiso_para_circular_prospecto_no_se_reasigna(self):
        # Dado
        concesionaria = self.vendedor_uno.obtener_concesionaria()
        concesionaria.configuracion_de_servicios().deshabilitar_circular_prospecto()

        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)

    def test_los_prospectos_de_los_vendedores_que_nunca_accedieron_al_listado_de_prospectos_no_se_reasignan(self):
        # Dado
        self.vendedor_uno._ultimo_acceso_al_listado_de_prospectos = None
        self.vendedor_uno.save()
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)

    def test_los_prospectos_recientemente_listados_por_sus_vendedores_no_se_reasignan(self):
        # Dado / Llamamos listados a los prospectos que tienen fecha de asignación menor a la fecha de la última actualización
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
        self._configurar_ultimo_acceso_al_listado_de_prospectos_posterior_a_la_asignacion_de(prospecto)

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)

    def test_los_prospectos_recientemente_listados_por_sus_responsables_no_se_reasignan(self):
        # Dado
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
        self._configurar_ultimo_acceso_al_listado_de_prospectos_previo_a_la_creacion_de(prospecto)
        self._configurar_ultimo_acceso_del_responsable_al_listado_de_prospecto_posterior_a_la_creacion_de(prospecto)

        # Cuando
        circulaciones = circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)
        self.assertEqual(len(circulaciones), 0)

    def test_un_prospecto_se_le_reasigna_a_otro_vendedor_del_pedido_segun_priorizacion_de_ultima_actividad(self):
        # Dado
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
        self._configurar_ultimo_acceso_al_listado_de_prospectos_previo_a_la_creacion_de(prospecto)
        self._forzar_que_el_vendedor_dos_tenga_la_ultima_actividad_mas_reciente()

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_dos)

    def test_un_prospecto_con_atributo_puede_circular_false_no_se_reasigna(self):
        # Dado
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
        prospecto._puede_circular = False
        prospecto.save()
        self._configurar_ultimo_acceso_al_listado_de_prospectos_previo_a_la_creacion_de(prospecto)

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)

    def test_los_minutos_de_inactividad_del_venedor_es_mayor_al_tiempo_maximo_de_inactividad_seteado_en_supervisor_no_se_reasigna(self):
        # Dado
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)

        self._configuracion_minutos_maximos_de_inactividad_para_circular_prospectos_supervisor_uno()

        self._configurar_a_vendedor_uno_ultima_actividad_un_minuto_menor_al_maximo_de_inactividad_para_circular_prospectos()

        self._configurar_a_vendedor_dos_ultima_actividad_un_minuto_mayor_al_maximo_de_inactividad_para_circular_prospectos()

        # Cuando
        circulador_de_prospectos.circular()

        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)

    def test_solo_circula_los_prospectos_en_estado_nuevo(self):
        # Dado
        circulador_de_prospectos = CirculadorDeProspectos.nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos()
        pedido = self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(supervisor=self.supervisor_uno,
                                                                                   credito=10, consumido=10)
        prospecto = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)
        prospecto_dos = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_uno)

        self._pasar_prospectos_a_en_progreso_y_finalizados(prospecto, prospecto_dos)

        self._configurar_ultimo_acceso_al_listado_de_prospectos_previo_a_la_creacion_de(prospecto)

        # Cuando
        circulaciones = circulador_de_prospectos.circular()


        # Entonces
        self._assert_tiene_como_vendedor_a(prospecto, self.vendedor_uno)
        self._assert_tiene_como_vendedor_a(prospecto_dos, self.vendedor_uno)
        self.assertEqual(len(circulaciones), 0)

    def _circulador_con_estrategia_de_priorizacion_predefinida(self, prospectos_priorizados):
        estrategia_de_priorizacion_de_prospectos = EstrategiaPredefinidaDePriorizacionDeProspectos.nuevo(
            prospectos_priorizados)
        circulador_de_prospectos = CirculadorDeProspectos.nuevo(
            estrategia_de_priorizacion_de_prospectos=estrategia_de_priorizacion_de_prospectos)
        return circulador_de_prospectos

    def _assert_tiene_como_vendedor_a(self, prospecto, vendedor):
        prospecto = reload_model(prospecto)
        self.assertEqual(prospecto.obtener_vendedor(), vendedor)

    def _assert_la_circulacion_es_desde_el_vendedor_uno_al_cinco(self, circulacion, prospecto_circulado):
        self.assertTrue(circulacion.tiene_como_cedente_a(cedente=self.vendedor_uno))
        self.assertTrue(circulacion.tiene_como_destinatario_a(destinatario=self.vendedor_cinco))
        self.assertTrue(circulacion.es_una_circulacion_de(prospecto=prospecto_circulado))

    def _configurar_a_todos_los_vendedores_ultimo_acceso_al_listado_de_prospectos(self):
        self.vendedor_cinco.actualizar_ultimo_acceso_al_listado_de_prospectos()
        self.vendedor_uno.actualizar_ultimo_acceso_al_listado_de_prospectos()
        self.vendedor_dos.actualizar_ultimo_acceso_al_listado_de_prospectos()
        self.supervisor_uno.actualizar_ultimo_acceso_al_listado_de_prospectos()

    def _configurar_a_vendedor_dos_ultima_actividad_un_minuto_mayor_al_maximo_de_inactividad_para_circular_prospectos(self):
        delta = timedelta(minutes=self.supervisor_uno._minutos_de_inactividad_maximos_para_circular_sus_prospectos + 1)
        self.vendedor_dos._ultima_actividad = timezone.now() - delta
        self.vendedor_dos.save()

    def _configurar_a_vendedor_uno_ultima_actividad_un_minuto_menor_al_maximo_de_inactividad_para_circular_prospectos(self):
        delta = timedelta(minutes=self.supervisor_uno._minutos_de_inactividad_maximos_para_circular_sus_prospectos - 1)
        self.vendedor_uno._ultima_actividad = timezone.now() - delta
        self.vendedor_uno.save()

    def _configuracion_minutos_maximos_de_inactividad_para_circular_prospectos_supervisor_uno(self):
        self.supervisor_uno._minutos_de_inactividad_maximos_para_circular_sus_prospectos = 10
        self.supervisor_uno.save()

    def _forzar_que_el_vendedor_dos_tenga_la_ultima_actividad_mas_reciente(self):
        self._configurar_ultima_actividad_excedida_a(vendedor=self.vendedor_dos, tiempo_inactividad=1)

    def _configurar_ultimo_acceso_al_listado_de_prospectos_posterior_a_la_asignacion_de(self, prospecto):
        delta = timedelta(seconds=1)
        self.vendedor_uno._ultimo_acceso_al_listado_de_prospectos = prospecto.fecha_de_asignacion_a_vendedor() + delta
        self.vendedor_uno.save()

    def _configurar_ultimo_acceso_al_listado_de_prospectos_previo_a_la_creacion_de(self, prospecto):
        delta = timedelta(seconds=5)
        self.vendedor_uno._ultimo_acceso_al_listado_de_prospectos = prospecto.fecha_de_asignacion_a_vendedor() - delta
        self.vendedor_uno.save()

    def _configurar_ultimo_acceso_del_responsable_al_listado_de_prospecto_posterior_a_la_creacion_de(self, prospecto):
        delta = timedelta(seconds=5)
        self.supervisor_uno._ultimo_acceso_al_listado_de_prospectos = prospecto.fecha_de_asignacion_a_vendedor() + delta
        self.supervisor_uno.save()

    def _configurar_circular_prospecto_deshabilitado_a(self, vendedor):
        vendedor.configuracion_servicios.deshabilitar_circular_prospecto()
        vendedor.save()

    def _configurar_limites_datos_nuevos_donde_solo_el_vendedor_uno_es_mayor_a_0(self):
        self._configurar_limite_de_datos_nuevos_para(vendedor=self.vendedor_uno, limite=1)
        self._configurar_limite_de_datos_nuevos_para(vendedor=self.vendedor_dos, limite=0)
        self._configurar_limite_de_datos_nuevos_para(vendedor=self.vendedor_cinco, limite=0)

    def _configurar_campania_para_no_poder_circular_prospecto(self, prospecto, permite_circular_prospectos=False):
        prospecto.campania.permite_circular_prospectos = permite_circular_prospectos
        prospecto.campania.save()

    def _configurar_limite_de_datos_nuevos_para(self, vendedor, limite):
        vendedor.limite_de_datos_nuevos_en_pedidos = limite
        vendedor.save()

    def _configurar_limite_de_datos_diarios_para(self, vendedor, limite):
        vendedor.limite_de_datos_diarios_en_pedidos = limite
        vendedor.save()

    def _configurar_ultima_actividad_excedida_a(self, vendedor, tiempo_inactividad):
        ahora = timezone.localtime(timezone.now())
        ultima_activdad = ahora - datetime.timedelta(minutes=tiempo_inactividad)
        vendedor._ultima_actividad = ultima_activdad
        vendedor.save()

    def _configurar_tiempo_de_inactividad_donde_solo_el_vendedor_uno_es_menor_a_20(self):
        self._configurar_ultima_actividad_excedida_a(vendedor=self.vendedor_uno, tiempo_inactividad=19)
        self._configurar_ultima_actividad_excedida_a(vendedor=self.vendedor_dos, tiempo_inactividad=21)
        self._configurar_ultima_actividad_excedida_a(vendedor=self.vendedor_cinco, tiempo_inactividad=30)

    def _configurar_tiempo_de_inactividad_1_minuto_para_todos_los_vendedores(self):
        self._configurar_ultima_actividad_excedida_a(vendedor=self.vendedor_uno, tiempo_inactividad=1)
        self._configurar_ultima_actividad_excedida_a(vendedor=self.vendedor_dos, tiempo_inactividad=1)
        self._configurar_ultima_actividad_excedida_a(vendedor=self.vendedor_cinco, tiempo_inactividad=1)

    def _configurar_los_dias_para_atender_prospectos_del_supervisor_en(self, dias):
        self.supervisor_uno.dias_para_atender_prospecto = dias
        self.supervisor_uno.save()

    def _configurar_en_0_los_minutos_de_inactividad_maximos_para_circular_prospectos_supervisor_uno(self):
        self.supervisor_uno._minutos_de_inactividad_maximos_para_circular_sus_prospectos = 0
        self.supervisor_uno.save()
        self.vendedor_uno = reload_model(self.vendedor_uno)
        self.vendedor_dos = reload_model(self.vendedor_dos)
        self.vendedor_cinco = reload_model(self.vendedor_cinco)

    def _crear_prospecto_nuevo_para_pedido(self, pedido, vendedor):
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=vendedor)
        prospecto._puede_circular = True
        prospecto.save()
        prospecto.asignar_pedido(pedido=pedido)
        return prospecto

    def _crear_prospecto_en_proceso_para_pedido(self, pedido):
        prospecto_dos = self._crear_prospecto_nuevo_para_pedido(pedido, self.vendedor_dos)
        gestor_de_prospecto = GestorDeProspecto.nuevo_para(rol=self.vendedor_dos)
        gestor_de_prospecto.comentar_prospecto(prospecto=prospecto_dos, texto="Hola")

    def _restringir_los_vendedores_por_limite_diario_para(self, pedido):
        self._configurar_limite_de_datos_diarios_para(vendedor=self.vendedor_dos, limite=1)
        self._configurar_limite_de_datos_diarios_para(vendedor=self.vendedor_cinco, limite=0)
        self._crear_prospecto_en_proceso_para_pedido(pedido)

    def _avanzar_tiempo_hasta_dias_para_atender_prospectos_del_supervisor(self, tiempo_freezado):
        tiempo_freezado.tick(relativedelta(days=self.supervisor_uno.dias_para_atender_prospecto + 1))

    def _pasar_prospectos_a_en_progreso_y_finalizados(self, prospecto_a_poner_en_progreso, prospecto_a_finalizar):
        gestor = GestorDeProspecto.nuevo_para(self.vendedor_uno)
        gestor.comentar_prospecto(prospecto_a_poner_en_progreso, "Este es un comentario")
        gestor.finalizar_prospecto(prospecto_a_finalizar, "Este prospecto esta finalizado")



