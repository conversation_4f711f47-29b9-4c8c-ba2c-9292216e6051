from django.urls import reverse

from prospectos.models import Prospecto
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from prospectos.views.forms import FinalizacionForm
from testing.base import BaseLoggedTest, BaseFixturedTest
from testing.test_utils import reload_model


class FinalizacionFormTest(BaseFixturedTest):

    def test_ok_form_finalizacion_sin_comentario(self):
        motivo_id = '%s' % self.fixture['motivo_1'].pk
        form = FinalizacionForm(data={"motivo": motivo_id})
        self.assertTrue(form.is_valid())

    def test_error_form_finalizacion_sin_motivo(self):
        form = FinalizacionForm(data={"comentario": "El comentario"})
        self.assertFalse(form.is_valid())
        self.assertTrue('__all__' in form.errors)

    def test_finalizacion_con_motivo(self):
        motivo_id = '%s' % self.fixture['motivo_1'].pk
        form = FinalizacionForm(data={"comentario": "El comentario", "motivo": motivo_id})
        self.assertTrue(form.is_valid())
        form2 = FinalizacionForm(data={"comentario": "El comentario", "otro_motivo": 'Otro Motivo'})
        self.assertTrue(form2.is_valid())


class FinalizacionTest(BaseLoggedTest):
    def setUp(self):
        super(FinalizacionTest, self).setUp()
        self.prospecto = self.fixture['p_1']
        self.vendedor = self.fixture['vend_1']
        self.repartidor = RepartidorDeProspectos.nuevo()
        self.supervisor = self.vendedor.responsable()
        self._asignar_y_responsabilizar_prospecto(vendedor=self.vendedor, prospecto=self.prospecto)

    def _asignar_y_responsabilizar_prospecto(self, vendedor, prospecto):
        self.repartidor.asignar_prospecto_a(prospecto=prospecto, vendedor=vendedor)

    def _finalizar_seguimiento(self, prospecto_id, motivo_id="", comentario="", otro_motivo=""):
        url = reverse('finalizar_seguimiento', kwargs={'pk': prospecto_id})
        response = self.client.post(url, {"motivo": motivo_id, "comentario": comentario,
                                          "otro_motivo": otro_motivo})

        return response

    def _assert_redirect_de_finalizacion_exitosa(self, response, prospecto_id):
        self.assertRedirects(response, '/prospectos/%s/' % prospecto_id, status_code=302, target_status_code=200,
                             msg_prefix='')

    def _assert_redirect_de_finalizacion_fallida(self, response):
        self.assertRedirects(response, reverse('resumen'), status_code=302, target_status_code=200,
                             msg_prefix='')

    def test_finaliza_prospecto_con_motivo_prefijado(self):
        prospecto_id = self.prospecto.pk
        motivo_id = '%s' % self.fixture['motivo_1'].pk
        response = self._finalizar_seguimiento(prospecto_id, motivo_id, comentario="Un comentario")
        self._assert_redirect_de_finalizacion_exitosa(response=response, prospecto_id=prospecto_id)
        prospecto = reload_model(self.prospecto)
        self.assertTrue(prospecto.finalizado)

    def test_finaliza_prospecto_no_asignado_al_vendedor_deberia_redireccionar_a_resumen_y_no_finalizar(self):
        prospecto_id = self.prospecto.pk
        prospectos = Prospecto.objects.filter(pk=prospecto_id)
        self.repartidor.quitar_asignacion_a_prospectos(prospectos)
        motivo_id = '%s' % self.fixture['motivo_1'].pk
        response = self._finalizar_seguimiento(prospecto_id, motivo_id, comentario="Un comentario")
        self._assert_redirect_de_finalizacion_fallida(response=response)
        prospecto = reload_model(self.prospecto)
        self.assertFalse(prospecto.finalizado)

    def test_finaliza_prospecto_con_otro_motivo(self):
        prospecto_id = self.prospecto.pk
        response = self._finalizar_seguimiento(prospecto_id, comentario="Un comentario", otro_motivo="El otro motivo")
        self._assert_redirect_de_finalizacion_exitosa(response=response, prospecto_id=prospecto_id)
        prospecto = reload_model(self.prospecto)
        self.assertTrue(prospecto.finalizado)
        self.assertIsNone(prospecto.finalizacion.motivo, 'El otro motivo')
        self.assertEqual(prospecto.finalizacion.comentario, 'Un comentario')
        self.assertEqual(prospecto.finalizacion.otro_motivo, 'El otro motivo')

    def test_responsable_finaliza_prospecto_sin_vendedor_deberia_ser_finalizado_exitosamente(self):
        self.login_and_assert_correct(user=self.supervisor.user)
        prospecto_id = self.prospecto.pk
        prospectos = Prospecto.objects.filter(pk=prospecto_id)
        self.repartidor.quitar_asignacion_a_prospectos(prospectos)
        self.assertTrue(self.prospecto.obtener_responsable())
        response = self._finalizar_seguimiento(prospecto_id, comentario="Un comentario", otro_motivo="El otro motivo")
        self._assert_redirect_de_finalizacion_exitosa(response=response, prospecto_id=prospecto_id)
        prospecto = reload_model(self.prospecto)
        self.assertTrue(prospecto.finalizado)

