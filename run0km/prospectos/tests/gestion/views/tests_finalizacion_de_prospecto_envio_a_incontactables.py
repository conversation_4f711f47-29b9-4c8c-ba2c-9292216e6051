import mock
from django.urls import reverse

from conversaciones.models import MensajesWhatsapp
from notificaciones import ContenidoCompuesto, FormaDeEnvioWhatsapp, FormaDeEnvioSMS
from notificaciones.tests.soporte import NotificacionesTestHelper
from prospectos.models import Prospecto, MotivoDeFinalizacion
from prospectos.tests.distribucion.pedidos.test_pedidos_core import mock_on_commit
from testing.base import BaseLoggedTest
from testing.test_utils import reload_model


class FinalizacionPorIncontactableTest(BaseLoggedTest):
    """
        Ver tests a nivel servicio: MensajesParaIncontactablesTest y AgregarComentarioPreconfiguradoTest.
    """

    def setUp(self):
        super(FinalizacionPorIncontactableTest, self).setUp()
        self._notificaciones_helper = NotificacionesTestHelper.nuevo_para(self)

    @NotificacionesTestHelper.mock_enviar
    def test_al_finalizacion_por_incontactable_sin_servicio_habilitado_no_deberia_enviar_notificacion(
            self, mock_enviar_notificacion):
        # Dado
        self.vendedor.deshabilitar_mensaje_incontactables()
        prospecto = self._prospecto_en_proceso()
        motivo = MotivoDeFinalizacion.objects.create(descripcion='Incontactable')

        # Cuando
        self._finalizar_seguimiento_y_assert_redirect_de(prospecto.pk, motivo_id=motivo.pk, comentario="Un comentario")

        # Entonces
        self._assert_esta_finalizado(prospecto)
        self._notificaciones_helper.assert_envio_no_realizado(mock_enviar_notificacion)

    @NotificacionesTestHelper.mock_enviar
    def test_al_finalizacion_por_incontactable_sin_sms_ni_whatsapp_no_deberia_enviar_notificacion(
            self, mock_enviar_notificacion):
        # Dado
        self.vendedor.habilitar_mensaje_incontactables()
        self.vendedor.deshabilitar_whatsapp()
        self.vendedor.deshabilitar_sms()
        prospecto = self._prospecto_en_proceso()
        motivo = MotivoDeFinalizacion.objects.create(descripcion='Incontactable')

        # Cuando
        self._finalizar_seguimiento_y_assert_redirect_de(prospecto.pk, motivo_id=motivo.pk, comentario="Un comentario")

        # Entonces
        self._assert_esta_finalizado(prospecto)
        self._notificaciones_helper.assert_envio_no_realizado(mock_enviar_notificacion)

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    def test_al_finalizacion_por_incontactable_con_whatsapp_habilitado_deberia_enviar_notificacion_via_whatsapp(
            self, mock_on_commit_func):
        # Dado
        self.vendedor.habilitar_mensaje_incontactables()
        self._habilitar_whatsapp_para(self.vendedor)
        self.vendedor.deshabilitar_sms()
        prospecto = self._prospecto_en_proceso()
        motivo = MotivoDeFinalizacion.objects.create(descripcion='Incontactable')

        # Cuando
        self._finalizar_seguimiento_y_assert_redirect_de(prospecto.pk, motivo_id=motivo.pk, comentario="Un comentario")

        # Entonces
        self._assert_esta_finalizado(prospecto)
        self._assert_envio_de_whatsapp_a(
            prospecto,
            'Hola %s, soy vend1 de conce_1. Intente llamarte y no pude encontrarte. '
            'Hay algun numero y horario donde pueda contactarte?' % prospecto.nombre)

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @NotificacionesTestHelper.mock_enviar
    def test_al_finalizacion_por_incontactable_con_sms_habilitado_deberia_enviar_notificacion_via_sms(
            self, mock_enviar_notificacion, mock_on_commit_func):
        # Dado
        self.vendedor.habilitar_mensaje_incontactables()
        self.vendedor.deshabilitar_whatsapp()
        self.vendedor.habilitar_sms()
        prospecto = self._prospecto_en_proceso()
        motivo = MotivoDeFinalizacion.objects.create(descripcion='Incontactable')

        # Cuando
        self._finalizar_seguimiento_y_assert_redirect_de(prospecto.pk, motivo_id=motivo.pk, comentario="Un comentario")

        # Entonces
        self._assert_esta_finalizado(prospecto)
        self._notificaciones_helper.assert_envio_con_forma_de_envio(mock_enviar_notificacion, FormaDeEnvioSMS)

    @mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
    @NotificacionesTestHelper.mock_enviar
    def test_al_finalizacion_por_incontactable_con_sms_y_whatsapp_habilitado_deberia_enviar_notificaciones(
            self, mock_enviar_notificacion, mock_on_commit_func):
        # Dado
        self.vendedor.habilitar_mensaje_incontactables()
        self._habilitar_whatsapp_para(self.vendedor)
        self.vendedor.habilitar_sms()
        prospecto = self._prospecto_en_proceso()
        motivo = MotivoDeFinalizacion.objects.create(descripcion='Incontactable')

        # Cuando
        self._finalizar_seguimiento_y_assert_redirect_de(prospecto.pk, motivo_id=motivo.pk, comentario="Un comentario")

        # Entonces
        self._assert_esta_finalizado(prospecto)
        self._notificaciones_helper.assert_envio_con_forma_de_envio(mock_enviar_notificacion, FormaDeEnvioSMS)
        self._notificaciones_helper.assert_envio_con_forma_de_envio(mock_enviar_notificacion, FormaDeEnvioWhatsapp)

    def _prospecto_en_proceso(self):
        prospecto = self.fixture['p_1']
        prospecto.estado = Prospecto.EN_PROCESO
        prospecto.es_telefono_movil = True
        prospecto.save()
        return prospecto

    def _habilitar_whatsapp_para(self, vendedor):
        vendedor.habilitar_whatsapp()
        concesionaria = vendedor.supervisor.concesionaria
        config_de_servicios = concesionaria.configuracion_de_servicios()
        config_de_servicios.habilitar_whatsapp()

    def _finalizar_seguimiento_y_assert_redirect_de(self, prospecto_id, motivo_id="", comentario="", otro_motivo=""):
        url = reverse('finalizar_seguimiento', kwargs={'pk': prospecto_id})
        response = self.client.post(url, {"motivo": str(motivo_id), "comentario": comentario,
                                          "otro_motivo": otro_motivo})
        self.assertRedirects(response, '/prospectos/%s/' % prospecto_id, status_code=302, target_status_code=200,
                             msg_prefix='')
        return response

    def _assert_esta_finalizado(self, prospecto):
        prospecto = reload_model(prospecto)
        self.assertTrue(prospecto.finalizado)

    def _assert_notificacion_con_contenido(self, mock_enviar_notificacion, tipo_de_contenido):
        self.assertTrue(mock_enviar_notificacion.called, msg="La notificacion no fue enviada")
        args, kwargs = mock_enviar_notificacion.call_args
        notificacion = args[0]
        self.assertIsInstance(notificacion.contenido(), tipo_de_contenido)
        return notificacion

    def _assert_envio_de_whatsapp_a(self, prospecto, texto):
        mensajes = MensajesWhatsapp.objects.obtener_conversacion_de_prospecto(prospecto)
        self.assertEqual(mensajes.count(), 1)
        mensaje = mensajes.first()
        self.assertEqual(mensaje.texto(), texto)
        self.assertEqual(mensaje.obtener_estado(), MensajesWhatsapp.PENDIENTE)
        self.assertEqual(mensaje.telefono, prospecto.telefono_para_whatsapp())

    def _assert_notificacion_con_contenido_compuesto(self, mock_enviar_notificacion):
        notificacion = self._assert_notificacion_con_contenido(mock_enviar_notificacion,
                                                               tipo_de_contenido=ContenidoCompuesto)
        self.assertEqual(set(notificacion.formas_de_envio()), {FormaDeEnvioWhatsapp.default(), FormaDeEnvioSMS.default()})
