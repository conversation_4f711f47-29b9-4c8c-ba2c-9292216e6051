from django.core.exceptions import ValidationError

from prospectos.models import InformacionDeRedesSociales
from prospectos.utils.parseo_de_informacion_de_redes_sociales import MostradorDeInformacionDeTwitter, \
    MostradorDeInformacionDeFacebook, MostradorDeInformacionDeCelular, MostradorDeInformacionDeVehiculo, \
    MostradorDeInformacionDeEmail, MostradorDeInformacionDeTelefono, MostradorDeInformacionDeNombre, \
    MostradorDeInformacionDeDireccion, MostradorDeInformacionDeIdentificacion, MostradorDeInformacionDeFoto
from testing.base import BaseFixturedTest


class MostradorDeInformacionContextoTest(BaseFixturedTest):
    def setUp(self):
        super(MostradorDeInformacionContextoTest, self).setUp()

        self.informacion_twitter = InformacionDeRedesSociales.nuevo(prospecto=self.fixture['p_1'],
                                                                    tipo=InformacionDeRedesSociales.TWITTER,
                                                                    valor=self._twitter_json())
        self.informacion_facebook = InformacionDeRedesSociales.nuevo(prospecto=self.fixture['p_1'],
                                                                     tipo=InformacionDeRedesSociales.FACEBOOK,
                                                                     valor=self._facebook_json())
        self.informacion_celular = InformacionDeRedesSociales.nuevo(prospecto=self.fixture['p_1'],
                                                                    tipo=InformacionDeRedesSociales.CELULAR,
                                                                    valor=self._celular_json())
        self.informacion_vehiculo = InformacionDeRedesSociales.nuevo(prospecto=self.fixture['p_1'],
                                                                     tipo=InformacionDeRedesSociales.VEHICULOS,
                                                                     valor='Ford')
        self.informacion_email = InformacionDeRedesSociales.nuevo(prospecto=self.fixture['p_1'],
                                                                  tipo=InformacionDeRedesSociales.EMAIL,
                                                                  valor=self._email_json())
        self.informacion_telefono = InformacionDeRedesSociales.nuevo(prospecto=self.fixture['p_1'],
                                                                     tipo=InformacionDeRedesSociales.TELEFONO,
                                                                     valor=self._telefono_json())
        self.informacion_nombre = InformacionDeRedesSociales.nuevo(prospecto=self.fixture['p_1'],
                                                                   tipo=InformacionDeRedesSociales.NOMBRE_COMPLETO,
                                                                   valor='Gustavo Andres Giolio')
        self.informacion_direccion = InformacionDeRedesSociales.nuevo(prospecto=self.fixture['p_1'],
                                                                      tipo=InformacionDeRedesSociales.DIRECCION,
                                                                      valor=self._direccion_json())
        self.informacion_direccion_incompleta = InformacionDeRedesSociales.nuevo(
            prospecto=self.fixture['p_1'], tipo=InformacionDeRedesSociales.DIRECCION,
            valor=self._direccion_json_incompleta())
        self.informacion_identificacion = InformacionDeRedesSociales.nuevo(
            prospecto=self.fixture['p_1'], tipo=InformacionDeRedesSociales.IDENTIFICACION, valor='DNI: 17420863')
        self.informacion_foto = InformacionDeRedesSociales.nuevo(
            prospecto=self.fixture['p_1'], tipo=InformacionDeRedesSociales.FOTO,
            valor=self._url_foto())

    def _url_foto(self):
        return 'https://d2ojpxxtu63wzl.cloudfront.net/static/c5d08079f6d897d33d473887101b08f6_afc6301b4ad4efef69f11' \
               '24e10f7070a40650802f7a01a05d31c76616825b604'

    def _direccion_json_incompleta(self):
        return '{"Direccion": {"Calle": "YERBAL 2593 5 C", "Pais": {"Nombre": "ARGENTINA"}, ' \
               '"CP": "1406", "Provincia": {"Nombre": "CAPITAL FEDERAL", "Pais": {"Nombre": "ARGENTINA"}}, ' \
               '"Tipo": "Direccion"}, "Prioridad": 0, "Tipo": "Direccion", "Medio": {"Razon": "Claro"}}'

    def _direccion_json(self):
        return '{"Direccion": {"Provincia": {"Nombre": "CAPITAL FEDERAL", "Pais": {"Nombre": "ARGENTINA"}}, ' \
               '"Tipo": "Direccion", "Calle": "Av Juan Bautista Lafuente 1100", ' \
               '"Localidad": {"Nombre": "Ciudad Autonoma Buenos Aires", "Provincia": {"Nombre": "CAPITAL FEDERAL", ' \
               '"Pais": {"Nombre": "ARGENTINA"}}}, "Pais": {"Nombre": "ARGENTINA"}, "CP": "C1406ETX"}, ' \
               '"Prioridad": 0, "Tipo": "Direccion", "Medio": {"Razon": "PERSONAL"}}'

    def _twitter_json(self):
        return '{"Twitter": {"Username": "altit22", "Tipo": "Twitter"}, "Prioridad": 0, "Tipo": "Twitter"}'

    def _facebook_json(self):
        return '{"Facebook": {"Url": "https://www.facebook.com/paul.ticera", "Tipo": "Facebook"}, ' \
               '"Prioridad": 0, "Tipo": "Facebook"}'

    def _email_json(self):
        return '{"Prioridad": 0, "Tipo": "Email", ' \
               '"Email": {"Tipo": "Email", "Direccion": "<EMAIL>"}}'

    def _celular_json(self):
        return '{"Celular": {"NormalizadoFecha": "2015-09-18T10:03:42.71", ' \
               '"Provincia": {"Nombre": "BUENOS AIRES", "Pais": {"Nombre": "ARGENTINA"}}, "Tipo": "Celular", ' \
               '"NormalizadoMovil": true, "NormalizadoSpam": false, "Normalizado": true, ' \
               '"Numero": "1169708918", "CodPais": "54", "NormalizadoOk": true, ' \
               '"Localidad": {"Nombre": "AMBA", "Provincia": {"Nombre": "BUENOS AIRES", ' \
               '"Pais": {"Nombre": "ARGENTINA"}}}, "CodArea": "11"}, "Prioridad": 0, "Tipo": "Celular", ' \
               '"Medio": {"Razon": "TELECOM PERSONAL S.A."}}'

    def _telefono_json(self):
        return '{"Telefono": {"NormalizadoFecha": "2015-09-16T13:22:52.433", ' \
               '"Provincia": {"Nombre": "BUENOS AIRES", "Pais": {"Nombre": "ARGENTINA"}}, ' \
               '"Tipo": "Telefono", "NormalizadoMovil": false, "NormalizadoSpam": false, "Normalizado": true, ' \
               '"Numero": "1143015236", "CodPais": "54", "NormalizadoOk": true, ' \
               '"Localidad": {"Nombre": "AMBA", "Provincia": {"Nombre": "BUENOS AIRES", ' \
               '"Pais": {"Nombre": "ARGENTINA"}}}, "CodArea": "11"}, "Prioridad": 0, ' \
               '"Tipo": "Telefono", "Medio": {"Razon": "TELEFONICA DE ARGENTINA S.A."}}'


class MostradorDeInformacionDeFotoTest(MostradorDeInformacionContextoTest):
    def setUp(self):
        super(MostradorDeInformacionDeFotoTest, self).setUp()
        self.mostrador = MostradorDeInformacionDeFoto.nuevo()

    def test_se_muestra_la_informacion_de_telefono_exitosamente(self):
        self.assertEqual(self.mostrador.mostrar(self.informacion_foto), self._url_foto())

    def mostrador_de_telefono_solo_muestra_informacion_de_telefono(self):
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_facebook)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_twitter)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_celular)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_vehiculo)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_email)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_nombre)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_direccion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_telefono)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_identificacion)


class MostradorDeInformacionDeIdentificacionTest(MostradorDeInformacionContextoTest):
    def setUp(self):
        super(MostradorDeInformacionDeIdentificacionTest, self).setUp()
        self.mostrador = MostradorDeInformacionDeIdentificacion.nuevo()

    def test_se_muestra_la_informacion_de_identificacion_exitosamente(self):
        self.assertEqual(self.mostrador.mostrar(self.informacion_identificacion), 'DNI: 17420863')

    def mostrador_de_identificacion_solo_muestra_informacion_de_identificacion(self):
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_facebook)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_twitter)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_celular)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_vehiculo)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_email)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_nombre)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_direccion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_telefono)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_foto)


class MostradorDeInformacionDeDireccionTest(MostradorDeInformacionContextoTest):
    def setUp(self):
        super(MostradorDeInformacionDeDireccionTest, self).setUp()
        self.mostrador = MostradorDeInformacionDeDireccion.nuevo()

    def test_se_muestra_la_informacion_de_direccion_exitosamente(self):
        self.assertEqual(self.mostrador.mostrar(self.informacion_direccion),
                         'Av Juan Bautista Lafuente 1100, Ciudad Autonoma Buenos Aires, CAPITAL FEDERAL')

    def test_se_muestra_la_informacion_de_direccion_incompleta_exitosamente(self):
        self.assertEqual(self.mostrador.mostrar(self.informacion_direccion_incompleta),
                         "YERBAL 2593 5 C")

    def mostrador_de_direccion_solo_muestra_informacion_de_direccion(self):
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_facebook)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_twitter)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_celular)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_vehiculo)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_email)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_nombre)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_telefono)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_identificacion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_foto)


class MostradorDeInformacionDeTelefonoTest(MostradorDeInformacionContextoTest):
    def setUp(self):
        super(MostradorDeInformacionDeTelefonoTest, self).setUp()
        self.mostrador = MostradorDeInformacionDeTelefono.nuevo()

    def test_se_muestra_la_informacion_de_telefono_exitosamente(self):
        self.assertEqual(self.mostrador.mostrar(self.informacion_telefono), '1143015236')

    def mostrador_de_telefono_solo_muestra_informacion_de_telefono(self):
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_facebook)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_twitter)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_celular)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_vehiculo)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_email)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_nombre)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_direccion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_identificacion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_foto)


class MostradorDeInformacionDeNombreTest(MostradorDeInformacionContextoTest):
    def setUp(self):
        super(MostradorDeInformacionDeNombreTest, self).setUp()
        self.mostrador = MostradorDeInformacionDeNombre.nuevo()

    def test_se_muestra_la_informacion_de_nombre_exitosamente(self):
        self.assertEqual(self.mostrador.mostrar(self.informacion_nombre), 'Gustavo Andres Giolio')

    def test_mostrador_de_nombre_solo_muestra_informacion_de_nombre(self):
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_facebook)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_twitter)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_celular)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_vehiculo)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_telefono)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_email)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_direccion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_identificacion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_foto)


class MostradorDeInformacionDeEmailTest(MostradorDeInformacionContextoTest):
    def setUp(self):
        super(MostradorDeInformacionDeEmailTest, self).setUp()
        self.mostrador = MostradorDeInformacionDeEmail.nuevo()

    def test_se_muestra_la_informacion_de_email_exitosamente(self):
        self.assertEqual(self.mostrador.mostrar(self.informacion_email), '<EMAIL>')

    def test_mostrador_de_email_solo_muestra_informacion_de_email(self):
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_facebook)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_twitter)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_celular)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_vehiculo)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_telefono)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_nombre)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_direccion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_identificacion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_foto)


class MostradorDeInformacionDeVehiculoTest(MostradorDeInformacionContextoTest):
    def setUp(self):
        super(MostradorDeInformacionDeVehiculoTest, self).setUp()
        self.mostrador = MostradorDeInformacionDeVehiculo.nuevo()

    def test_se_muestra_la_informacion_de_vehiculo_exitosamente(self):
        self.assertEqual(self.mostrador.mostrar(self.informacion_vehiculo), 'Ford')

    def test_mostrador_de_vehiculo_solo_muestra_informacion_de_vehiculo(self):
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_facebook)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_twitter)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_celular)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_email)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_telefono)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_nombre)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_direccion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_identificacion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_foto)


class MostradorDeInformacionDeCelularTest(MostradorDeInformacionContextoTest):
    def setUp(self):
        super(MostradorDeInformacionDeCelularTest, self).setUp()
        self.mostrador = MostradorDeInformacionDeCelular.nuevo()

    def test_se_muestra_la_informacion_de_celular_exitosamente(self):
        self.assertEqual(self.mostrador.mostrar(self.informacion_celular), '1169708918')

    def test_mostrador_de_celular_solo_muestra_informacion_de_celular(self):
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_facebook)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_twitter)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_vehiculo)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_email)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_telefono)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_nombre)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_direccion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_identificacion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_foto)


class MostradorDeInformacionDeTwitterTest(MostradorDeInformacionContextoTest):
    def setUp(self):
        super(MostradorDeInformacionDeTwitterTest, self).setUp()
        self.mostrador = MostradorDeInformacionDeTwitter.nuevo()

    def test_tipo_twitter_muestra_exitosamente_los_datos(self):
        self.assertEqual(self.mostrador.mostrar(self.informacion_twitter), 'https://twitter.com/altit22')

    def test_mostrador_de_twitter_solo_muestra_informacion_de_twitter(self):
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_facebook)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_celular)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_vehiculo)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_email)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_telefono)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_nombre)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_direccion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_identificacion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_foto)


class MostradorDeInformacionDeFacebookTest(MostradorDeInformacionContextoTest):
    def setUp(self):
        super(MostradorDeInformacionDeFacebookTest, self).setUp()
        self.mostrador = MostradorDeInformacionDeFacebook.nuevo()

    def test_tipo_facebook_muestra_exitosamente_los_datos(self):
        self.assertEqual(self.mostrador.mostrar(self.informacion_facebook), 'https://www.facebook.com/paul.ticera')

    def test_mostrador_de_facebook_solo_muestra_informacion_de_facebook(self):
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_twitter)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_celular)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_vehiculo)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_email)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_telefono)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_nombre)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_direccion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_identificacion)
        self.assertRaises(ValidationError, self.mostrador.mostrar,
                          informacion_de_redes_sociales=self.informacion_foto)
