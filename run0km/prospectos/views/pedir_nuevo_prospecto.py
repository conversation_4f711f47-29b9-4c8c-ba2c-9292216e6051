from django.views.generic import View
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.http import JsonResponse
from django.core.exceptions import  ValidationError
from rest_framework.views import status
from prospectos.services.pedir_prospecto_service import PedirProspectoService


class PedirNuevoProspectoView(View):

    @method_decorator(login_required)
    def post(self, request):
        try:
            PedirProspectoService.pedir_prospecto_para_vendedor(request.user.role())
            return JsonResponse({
                "title": "Solicitud de nuevo prospecto",
                "message": "Se ha registrado el pedido, en breve será procesado"},
                status = status.HTTP_200_OK
            )
        except ValidationError as e:
            return JsonResponse({"title":"Advertencia", "message":e.message}, status=status.HTTP_400_BAD_REQUEST)
        except ValueError as e:
            return JsonResponse({"title":"Advertencia", "message": str(e)}, status=status.HTTP_400_BAD_REQUEST)
