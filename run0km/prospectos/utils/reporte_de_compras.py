from django.db.models.aggregates import Sum

from prospectos.models import <PERSON><PERSON><PERSON>, Prospect<PERSON>, Proveedor
from prospectos.views.resumen import calcular_ranking


class ReporteDeCompras(object):

    def __init__(self, anio, mes):
        self.anio = anio
        self.mes = mes
        self.compras_por_proveedor = []
        self.calcular_compras_por_proveedor()
        self.datos_totales = {}
        self.calcular_totales()

    def calcular_compras_por_proveedor(self):
        compras = Compra.objects.por_anio_y_mes(self.anio, self.mes)
        for compra in compras:
            self.calcular_datos_de_compra(compra)
        self.agregar_datos_de_proveedores_sin_compras()

    def agregar_datos_de_proveedores_sin_compras(self):
        id_proveedores_con_datos = [x['compra'].proveedor.id for x in self.compras_por_proveedor]
        proveedores_sin_datos = Proveedor.objects.excluir_proveedor_vacio().exclude(id__in=id_proveedores_con_datos)
        for proveedor in proveedores_sin_datos:
            compra_vacia = Compra(proveedor=proveedor, anio=self.anio, mes=self.mes, monto=0)
            self.calcular_datos_de_compra(compra_vacia)

    def calcular_datos_de_compra(self, compra):
        datos = {}
        prospectos = compra.proveedor.prospectos.por_mes_y_anio(self.anio, self.mes).asignado_a_algun_vendedor()

        datos['compra'] = compra
        cantidad_de_prospectos = prospectos.count()
        if cantidad_de_prospectos > 0:
            datos['cantidad_de_prospectos'] = cantidad_de_prospectos
            datos['ventas'] = prospectos.vendidos().count()

            if datos['ventas'] > 0:
                datos['total_precio_ventas'] = prospectos.vendidos().aggregate(Sum('ventas__precio'))['ventas__precio__sum']
            else:
                datos['total_precio_ventas'] = 0

            ranking = calcular_ranking(datos['cantidad_de_prospectos'], datos['ventas'])
            datos['ranking'] = "%s/%s" % ranking
            datos['monto'] = prospectos.valor_total()
            datos['total_comprado'] = compra.monto
            datos['costo_por_dato'] = None
            if not datos['cantidad_de_prospectos'] == 0:
                datos['costo_por_dato'] = datos['total_comprado'] / datos['cantidad_de_prospectos']
            datos['costo_por_venta'] = None
            datos['precio_venta_promedio'] = None
            datos['costo_por_venta_promedio'] = None
            if not datos['ventas'] == 0:
                costo_por_venta = datos['total_comprado'] / datos['ventas']
                datos['costo_por_venta'] = costo_por_venta
                precio_venta_promedio = int(datos['total_precio_ventas'] / datos['ventas'])
                datos['precio_venta_promedio'] = precio_venta_promedio
                datos['costo_por_venta_promedio'] = self._calcular_costo_por_venta_promedio(
                    costo_por_venta, precio_venta_promedio)

            self.compras_por_proveedor.append(datos)

    def calcular_totales(self):
        self.datos_totales['ranking'] = 0 #TODO Averiguar como se calcula
        campos_a_sumar = ['cantidad_de_prospectos', 'ventas', 'monto', 'total_comprado', 'total_precio_ventas']
        for campo in campos_a_sumar:
            self.datos_totales[campo] = 0
        for datos in self.compras_por_proveedor:
            for campo in campos_a_sumar:
                self.datos_totales[campo] += datos[campo]
        self.datos_totales['costo_por_dato'] = None

        if not self.datos_totales['cantidad_de_prospectos'] == 0:
            self.datos_totales['costo_por_dato'] = self.datos_totales['total_comprado']/self.datos_totales['cantidad_de_prospectos']
        self.datos_totales['costo_por_venta'] = None
        self.datos_totales['precio_venta_promedio'] = None
        self.datos_totales['costo_por_venta_promedio'] = None
        if not self.datos_totales['ventas'] == 0:
            costo_por_venta = self.datos_totales['total_comprado'] / self.datos_totales['ventas']
            self.datos_totales['costo_por_venta'] = costo_por_venta
            precio_venta_promedio = int(self.datos_totales['total_precio_ventas'] / self.datos_totales['ventas'])
            self.datos_totales['precio_venta_promedio'] = precio_venta_promedio
            self.datos_totales['costo_por_venta_promedio'] = self._calcular_costo_por_venta_promedio(
                costo_por_venta, precio_venta_promedio)

        ranking = calcular_ranking(self.datos_totales['cantidad_de_prospectos'], self.datos_totales['ventas'])
        self.datos_totales['ranking'] = "%s/%s" % ranking

    def _calcular_costo_por_venta_promedio(self, costo_por_venta, precio_venta_promedio):
        if precio_venta_promedio > 0:
            return round((float(costo_por_venta) / precio_venta_promedio) * 100, 2)
        else:
            return None

    def totales(self):
        return self.datos_totales

    def datos_por_compra(self):
        return self.compras_por_proveedor
