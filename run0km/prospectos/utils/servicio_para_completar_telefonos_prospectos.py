# from prospectos.models import Telefono
from django.conf import settings

from lib.normalizador import (NormalizadorDeTelefonos, PREFIJO, NUMERO, MOVIL, TELCO, SPAM, LOCALIDAD, PROVINCIA,
                              BIEN_CONSTITUIDO, ServicioNormalizarError, NormalizacionTelefonoError,
                              ServicioNormalizarRespuestaInesperadaError)
from lib.whatsapp_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WhatsAppCheckerApiError

from prospectos.models import (CampoExtra, LogDeErrorNormalizador, LogDeErrorChequeadorDeWhatsapp,
                               ProspectoConTelefonoACompletar)

CAMPO_LOCALIDAD = "Localidad Adicional"
CAMPO_PROVINCIA = "Provincia Adicional"


class NormalizadorDeTelefonosDeProspectos(object):
    def __init__(self):
        self.normalizador_de_telefonos = NormalizadorDeTelefonos.nuevo_con_url(settings.NORMALIZACION_SERVICE_URL)

    def _log_error_normalizacion(self, normalizacion_error):
        LogDeErrorNormalizador.guardar_error_de_comunicacion_log(normalizacion_error)

    def _normalizar_telefonos(self, numeros_telefonicos):
        return self.normalizador_de_telefonos.normalizar(numeros_telefonicos)

    def normalizar(self, prospecto):

        if not settings.REALIZAR_NORMALIZACION:
            return

        telefonos = self._extraer_telefonos_prospecto(prospecto)
        try:
            response = self._normalizar_telefonos(telefonos)
        except ServicioNormalizarError as e:
            self._log_error_normalizacion(e)
            return

        self._actualizar_telefono_principal(prospecto, response)
        self._actualizar_telefonos_extra(prospecto, response)
        prospecto.esta_telefono_normalizado = True
        prospecto.full_clean()
        prospecto.save()

    def _actualizar_telefono_principal(self, prospecto, response):
        try:
            normalizado = response.get_telefono(self._get_codigo_para_prospecto(prospecto))
        except ServicioNormalizarRespuestaInesperadaError as e:
            self._log_error_normalizacion(e)
            pass
        except NormalizacionTelefonoError as e:
            pass
        else:
            if normalizado is not None:
                self._asignar_telefono(prospecto, normalizado)
                self._actualizar_localidad_y_provincia(prospecto, normalizado)

    def _extraer_telefonos_prospecto(self, prospecto):
        """
            Responde la lista de telefonos, responde una lista de tuplas (PK, Numero Telefonico)
        :param prospecto: Prospecto
        :return: List(<(String,String)>)
        """

        telefonos = [(self._get_codigo_para_prospecto(prospecto), prospecto.telefono)]

        for telefono_extra in prospecto.telefono_extra.all():
            telefonos.append((self._get_codigo_para_telefono_extra(telefono_extra), telefono_extra.telefono))
        return telefonos

    def _asignar_telefono(self, prospecto_or_telefono_extra, telefono_normalizado):

        prospecto_or_telefono_extra.prefijo = telefono_normalizado[PREFIJO]
        prospecto_or_telefono_extra.telefono = telefono_normalizado[NUMERO]
        prospecto_or_telefono_extra.es_telefono_movil = telefono_normalizado[MOVIL]
        prospecto_or_telefono_extra.telco = telefono_normalizado[TELCO]
        prospecto_or_telefono_extra.telefono_bien_constituido = telefono_normalizado[BIEN_CONSTITUIDO]
        prospecto_or_telefono_extra.esta_spam_list = telefono_normalizado[SPAM]

    def _crear_campo_extra(self, prospecto, nombre, valor):
        count = prospecto.campos_extra.filter(nombre__startswith=nombre).count() + 1
        campo_extra = CampoExtra(prospecto=prospecto, nombre=nombre + ' ' + str(count), valor=valor)
        campo_extra.save()

    def _actualizar_localidad_y_provincia(self, prospecto, telefono_normalizado):
        """
            Asigna la localidad y provincia. Si el prospecto tiene su atributo vacio actualiza este atributo,
            sino agrega el campo extra, si existe un campo con el mismo valor no lo agrega (utiliza la identidad de string)
        :param prospecto: Prospecto
        :param telefono_normalizado: <tupla>
        :return:
        """

        if not prospecto.localidad:
            prospecto.localidad = telefono_normalizado[LOCALIDAD]
        else:
            if prospecto.localidad != telefono_normalizado[LOCALIDAD] and not prospecto.campos_extra.filter(
                    nombre__startswith=CAMPO_LOCALIDAD, valor=telefono_normalizado[LOCALIDAD]):
                self._crear_campo_extra(prospecto, CAMPO_LOCALIDAD, telefono_normalizado[LOCALIDAD])

        if not prospecto.provincia:
            prospecto.provincia = telefono_normalizado[PROVINCIA]
        else:
            if prospecto.provincia != telefono_normalizado[PROVINCIA] and not prospecto.campos_extra.filter(
                    nombre__startswith=CAMPO_PROVINCIA, valor=telefono_normalizado[PROVINCIA]):
                self._crear_campo_extra(prospecto, CAMPO_PROVINCIA, telefono_normalizado[PROVINCIA])

    def _actualizar_telefonos_extra(self, prospecto, response):
        """
            Actualiza los telfonos extra del prospecto con su correspondiente normalizado

        :param prospecto: Prospecto
        :param response: RespuestaNormalizador
        :return: None
        """

        for extra in prospecto.telefono_extra.all():
            try:
                normalizado = response.get_telefono(self._get_codigo_para_telefono_extra(extra), None)
            except ServicioNormalizarRespuestaInesperadaError as e:
                self._log_error_normalizacion(e)
                pass
            except NormalizacionTelefonoError as e:
                pass
            else:
                if normalizado is not None:
                    self._asignar_telefono(extra, normalizado)
                    self._actualizar_localidad_y_provincia(prospecto, normalizado)
            extra.esta_telefono_normalizado = True
            extra.save()

    # necesario porque el telefono no esta reificado, y un prospecto y un telefono extra pueden tener el mismo id
    def _get_codigo_para_prospecto(self, prospecto):
        return 'prospecto' + str(prospecto.pk)

    def _get_codigo_para_telefono_extra(self, telefono_extra):
        return 'telefono_extra' + str(telefono_extra.pk)


class CheckeadorWhatsappDeTelefonosDeProspectos(object):
    def __init__(self):
        self.whatsapp_checker = WhatsappChecker()

    def _log_error(self, error_chequeador):
        LogDeErrorChequeadorDeWhatsapp.guardar_error_de_comunicacion_log(error_chequeador)

    def evaluar(self, prospecto):
        if not settings.REALIZAR_CHECKEO_DE_WHATSAPP:
            return

        self._chequear_telefono_principal(prospecto)
        self._chequear_telefonos_extra(prospecto)

    def _chequear_telefono_principal(self, prospecto):
        self._chequear_telefono_de(prospecto)

    def _chequear_telefonos_extra(self, prospecto):
        for telefono_extra in prospecto.telefono_extra.all():
            self._chequear_telefono_de(telefono_extra)

    def _chequear_telefono_de(self, prospecto_o_telefono_extra):
        try:
            response = self.whatsapp_checker.evaluate(prospecto_o_telefono_extra.telefono)
        except WhatsAppCheckerApiError as exc:
            self._log_error(exc)
            pass
        else:
            if not response.has_error:
                prospecto_o_telefono_extra.tiene_whatsapp = response.has_whatsapp
                try:
                    image_file = response.get_profile_image()
                except WhatsAppCheckerApiError as exc:
                    self._log_error(exc)
                else:
                    if image_file:
                        prospecto_o_telefono_extra.imagen_de_perfil_de_whatsapp.save(image_file.name, image_file,
                                                                                     save=True)
                prospecto_o_telefono_extra.full_clean()
                prospecto_o_telefono_extra.save()


class CompletarInfoDeTelefonosDeProspectos(object):
    def __init__(self):
        self.whatsapp_checker = CheckeadorWhatsappDeTelefonosDeProspectos()
        self.normalizador = NormalizadorDeTelefonosDeProspectos()

    def evaluar(self, prospecto):
        self.normalizador.normalizar(prospecto)
        self.whatsapp_checker.evaluar(prospecto)


class ColaDeProspectosACompletarInfoDeTelefonos(object):
    def procesar(self, cantidad):
        prospectos = self._desencolar_prospectos(cantidad)
        info_completer = CompletarInfoDeTelefonosDeProspectos()
        for prospecto in prospectos:
            info_completer.evaluar(prospecto)

    def encolar(self, prospecto):
        return ProspectoConTelefonoACompletar.encolar(prospecto)

    def encolados(self):
        return ProspectoConTelefonoACompletar.objects.all()

    def _desencolar_prospectos(self, cantidad):
        items_a_desencolar = ProspectoConTelefonoACompletar.objects.all().order_by('prospecto__fecha_creacion')[
                             :cantidad]
        prospectos = []
        for item in items_a_desencolar:
            prospectos.append(item.prospecto)
            item.delete()
        return prospectos
