# coding=utf-8
from datetime import timedelta

from django.core.exceptions import ValidationError
from django.utils import timezone

from concesionarias.rango_laboral import CalendarioLaboral
from lib.timing import timing
import datetime


class SelectorDeHorarioDeProgramacionDeLlamadas(object):
    def __init__(self, llamados_agendados_queryset, llamados_excluidos_queryset, minutos_minimos_entre_llamados,
                 minutos_de_margen_a_la_fecha_actual):
        self._llamados_agendados_queryset = llamados_agendados_queryset
        self._llamados_excluidos_queryset = llamados_excluidos_queryset
        self._minutos_minimos_entre_llamados = minutos_minimos_entre_llamados
        self._minutos_de_margen_a_la_fecha_actual = minutos_de_margen_a_la_fecha_actual
        self._calendario_laboral = self.__class__._calendario_laboral()
        self._llamados = None

    @classmethod
    def nuevo_para(cls, llamados_agendados_queryset, llamados_excluidos_queryset, minutos_entre_llamados,
                   minutos_de_margen_a_la_fecha_actual):
        cls._validar_distancia_entre_llamados(minutos_entre_llamados)
        return cls(llamados_agendados_queryset, llamados_excluidos_queryset, minutos_entre_llamados,
                   minutos_de_margen_a_la_fecha_actual)

    @classmethod
    def nuevo_con_limite_diario(cls, llamados_agendados_queryset, cantidad_de_llamados_diarios, minutos_entre_llamados,
                                minutos_de_margen_a_la_fecha_actual):
        minutos_entre_llamados = cls._calcular_minutos_entre_llamados(minutos_entre_llamados, cantidad_de_llamados_diarios)
        selector = cls.nuevo_para(llamados_agendados_queryset, [], minutos_entre_llamados,
                                  minutos_de_margen_a_la_fecha_actual)
        return selector

    @classmethod
    def _calcular_minutos_entre_llamados(cls, minutos_entre_llamados,cantidad_de_llamados_diarios):
        if minutos_entre_llamados > 0:
            return minutos_entre_llamados
        cls._validar_cantidad_de_llamados_diarios(cantidad_de_llamados_diarios)
        segundos = cls._duracion_jornada_laboral().total_seconds()
        segundos_entre_llamados = segundos / cantidad_de_llamados_diarios
        minutos_entre_llamados = segundos_entre_llamados / 60
        return minutos_entre_llamados

    @classmethod
    def _validar_distancia_entre_llamados(cls, minutos_entre_llamados):
        if timezone.timedelta(minutes=minutos_entre_llamados) > cls._duracion_jornada_laboral():
            raise ValidationError('La distancia entre llamados no puede ser mayor a la jornada laboral')

    @classmethod
    def _validar_cantidad_de_llamados_diarios(cls, cantidad_de_llamados_diarios):
        if cantidad_de_llamados_diarios < 1:
            raise ValidationError('La cantidad de llamados diarios debe ser mayor a cero')

    @classmethod
    def _duracion_jornada_laboral(cls):
        calendario_laboral = cls._calendario_laboral()
        horas = calendario_laboral.duracion_de_jornada_laboral_en_dia_de_semana()
        return horas

    @classmethod
    def _calendario_laboral(cls):
        return CalendarioLaboral.default()

    @timing
    def seleccionar(self, cantidad, posterior_a=None):        
        self._reiniciar_llamados()
        fechas = []
        for _ in range(0, cantidad):
            fecha_y_hora = self._seleccionar(posterior_a=posterior_a, fechas_y_horas_seleccionadas=fechas)
            fechas.append(fecha_y_hora)
        return fechas

    def seleccionar_mejor_fecha_y_hora_desde_ahora(self):
        return self._seleccionar()

    def _seleccionar(self, posterior_a=None, fechas_y_horas_seleccionadas=None):
        posterior_a = posterior_a or timezone.now()
        desde = timezone.localtime(posterior_a) + timedelta(minutes=self._minutos_de_margen_a_la_fecha_actual)
        fecha_y_hora = self._seleccionar_desde(desde, fechas_y_horas_seleccionadas)
        fecha_y_hora = timezone.localtime(fecha_y_hora)
        return fecha_y_hora

    def _seleccionar_desde(self, desde, fechas_y_horas_seleccionadas):
        if not self._calendario_laboral.esta_dentro_del_horario_laboral(desde):
            desde = self._obtener_proxima_fecha_y_hora_de_entrada_laboral(desde)
        return self._seleccionar_mejor_fecha_y_hora_desde_dia_laboral(desde, fechas_y_horas_seleccionadas)

    def _seleccionar_mejor_fecha_y_hora_desde_dia_laboral(self, fecha_y_hora_laboral, fechas_y_horas_seleccionadas):
        hasta = self._convertir_con_horario_de_salida(fecha_y_hora_laboral)
        return self._seleccionar_mejor_hora_para(fecha_y_hora_laboral, hasta, fechas_y_horas_seleccionadas)

    def _convertir_con_horario_de_salida(self, fecha_y_hora):
        horario_de_salida = self._calendario_laboral.horario_de_salida(fecha_y_hora)
        fecha_con_horario_de_salida = timezone.datetime.combine(fecha_y_hora, horario_de_salida).replace(
            tzinfo=fecha_y_hora.tzinfo)
        return fecha_con_horario_de_salida

    def _obtener_proxima_fecha_y_hora_de_entrada_laboral(self, desde):
        if self._esta_fuera_de_horario_laboral_o_no_es_dia_laboral(desde):
            fecha_y_hora = self._calendario_laboral.siguiente_fecha_y_hora_laboral_a(desde)
        elif self._es_dia_laboral_anterior_al_horario_de_ingreso(desde):
            horario_de_entrada = self._calendario_laboral.horario_de_entrada(desde)
            fecha_y_hora = timezone.datetime.combine(desde, horario_de_entrada).replace(tzinfo=desde.tzinfo)
        else:
            fecha_y_hora = desde
        return fecha_y_hora

    def _seleccionar_mejor_hora_para(self, fecha_desde, fecha_hasta, fechas_y_horas_seleccionadas):
        fechas_de_llamados_en_rango = self._obtener_fechas_de_llamados_en_rango(
            fecha_desde, fecha_hasta, fechas_y_horas_seleccionadas)

        mejor_fecha_y_hora = self._buscar_fecha_en_rango(fechas_de_llamados_en_rango)
        if mejor_fecha_y_hora is not None:
            return mejor_fecha_y_hora

        # si no hay opciones para el día actual, se sigue con el próximo día laboral
        siguiente_dia_laboral = self._calendario_laboral.siguiente_fecha_y_hora_laboral_a(fecha_desde)
        return self._seleccionar_mejor_fecha_y_hora_desde_dia_laboral(
            siguiente_dia_laboral, fechas_y_horas_seleccionadas)

    def _buscar_fecha_en_rango(self, fechas_de_llamados_en_rango):
        # primera y ultima fecha son la fecha actual o las fechas de inicio/fin del horario laboral
        for i in range(0, len(fechas_de_llamados_en_rango) - 1):
            fecha_inicial = fechas_de_llamados_en_rango[i]
            fecha_final = fechas_de_llamados_en_rango[i + 1]
            fecha_inicial_no_es_de_un_llamado = i == 0
            if self._alcanza_el_tamanio_del_rango(fecha_inicial, fecha_final, fecha_inicial_no_es_de_un_llamado):
                if fecha_inicial_no_es_de_un_llamado:
                    return fecha_inicial
                else:
                    return fecha_inicial + timedelta(minutes=self._minutos_minimos_entre_llamados)

    def _esta_fuera_de_horario_laboral_o_no_es_dia_laboral(self, fecha_y_hora):
        return not self._calendario_laboral.es_dia_laboral(fecha_y_hora) or \
               fecha_y_hora.time() > self._calendario_laboral.horario_de_salida(fecha_y_hora)

    def _es_dia_laboral_anterior_al_horario_de_ingreso(self, fecha_y_hora):
        return self._calendario_laboral.es_dia_laboral(fecha_y_hora) and \
               fecha_y_hora.time() < self._calendario_laboral.horario_de_entrada(fecha_y_hora)

    def _alcanza_el_tamanio_del_rango(self, fecha_inicial_rango, fecha_final_rango, fecha_inicial_no_es_de_un_llamado):
        segundos_en_el_rango = self._cantidad_de_segundos_entre(fecha_inicial_rango, fecha_final_rango)
        return segundos_en_el_rango >= self._tamanio_minimo_de_rango_en_segundos(fecha_inicial_no_es_de_un_llamado)

    def _tamanio_minimo_de_rango_en_segundos(self, fecha_inicial_no_es_de_un_llamado):
        """
            El tamaño minimo es dos veces la separacion en segundos, ya que el rango va a ser partido en dos,
            siempre y cuando el rango esté constituído entre dos llamados distintos y no con alguna fecha inicial que
            no sea un llamado (ej: comienzo del dia laboral u hora actual)
        """
        separacion_en_segundos = self._minutos_minimos_entre_llamados * 60
        if fecha_inicial_no_es_de_un_llamado:
            return separacion_en_segundos
        else:
            return separacion_en_segundos * 2

    def _cantidad_de_segundos_entre(self, fecha_inicial, fecha_final):
        diferencia = fecha_final - fecha_inicial
        segundos = diferencia.total_seconds()
        return segundos

    def _obtener_fechas_de_llamados_en_rango(self, fecha_desde, fecha_hasta, fechas_y_horas_seleccionadas=None):
        # Un potencial problema es que los limites del rango son manejados como llamados

        fecha_desde_sin_margen = fecha_desde - timedelta(minutes=self._minutos_de_margen_a_la_fecha_actual)
        fechas_y_horas_seleccionadas = fechas_y_horas_seleccionadas or []
        fechas_y_horas_seleccionadas = [each for each in fechas_y_horas_seleccionadas if fecha_desde_sin_margen <= each <= fecha_hasta]
        fechas_de_llamados = self._llamados_entre(fecha_desde, fecha_hasta)
        fechas_de_llamados.append(fecha_desde)
        fechas_de_llamados.append(fecha_hasta)
        fechas_de_llamados += fechas_y_horas_seleccionadas
        fechas_de_llamados.sort()
        return fechas_de_llamados

    def _llamados_entre(self, fecha_desde, fecha_hasta):
        llamados = self._obtener_llamados()
        llamados_filtrados = [each for each in llamados if fecha_desde <= each <= fecha_hasta]
        return llamados_filtrados

    def _obtener_llamados(self):
        if self._llamados is None:
            llamados_queryset = self._llamados_agendados_queryset.exclude(id__in=self._llamados_excluidos_queryset)
            fechas_y_horas_de_llamados = list(llamados_queryset.fechas_y_horas())
            self._llamados = [timezone.localtime(fecha_y_hora) for fecha_y_hora in fechas_y_horas_de_llamados]
        return self._llamados

    def _reiniciar_llamados(self):
        self._llamados = None
