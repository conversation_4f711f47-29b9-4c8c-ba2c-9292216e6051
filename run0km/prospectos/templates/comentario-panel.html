{% load static from staticfiles %}


<div style="margin: 15px 0px;">
    <p>Nuevo comentario</p>
    <input type="hidden" id="url_comentar_{{ prospecto.id }}" value="{% url 'comentar' prospecto.id %}">
    <div class="comentario-contenido">
        <textarea id="comentario_{{ prospecto.id }}"
                class="comentario-texto"
                cols="30" rows="10" placeholder="Agregar comentario"></textarea>
    </div>
    <div id="spin_comentario_{{ prospecto.id }}" class='spin-holder'></div>
    <div class="comentario-bar">
        <button class="comentario-button btn btn-outline-primary"
                onclick="gtag('event', 'lista_agregar_comentario', {}); comentarProspecto({{ prospecto.id }}, comentarProspectoListaResponse);">Enviar comentario</button>        

        <button class="comentario-button btn btn-outline-primary"
                onclick="gtag('event', 'agregar_comentario_automatico', {}); agregarComentarioNoContestaAProspecto({{ prospecto.id }}, comentarProspectoListaResponse);">No contesta</button>
        <button class="comentario-button btn btn-outline-primary"
                onclick="gtag('event', 'agregar_comentario_automatico', {}); agregarComentarioOcupadoAProspecto({{ prospecto.id }}, comentarProspectoListaResponse);">Ocupado</button>
        <button class="comentario-button btn btn-outline-primary btn-comentario"
         onclick="recordButtonClicked('comentario_{{ prospecto.id }}', 'recordButton_{{prospecto.id}}')" id="recordButton_{{prospecto.id }}">
                <img src="{{ STATIC_URL }}img/mic.png" width="auto" height="100%" title="Apretar para grabar, volver a apretar para dejar de grabar"/>
        </button>
        <button class="comentario-button btn btn-outline-primary"
                onclick="gtag('event', 'agregar_comentario_automatico', {}); agregarComentarioContestadorAProspecto({{ prospecto.id }}, comentarProspectoListaResponse);">Contestador</button>
        <textarea id="comentario_{{ prospecto.id }}_transcript"
                class="comentario-texto"
                cols="30" rows="10" placeholder="Agregar comentario" hidden></textarea>
        
    </div>
      
</div>

<script>
// Detectar si el navegador es Firefox o Safari
var esFirefox = typeof InstallTrigger !== 'undefined';
var esSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
// Si es Firefox o Safari, ocultar el botón
if (esFirefox || esSafari) {
    const botones = document.querySelectorAll('.btn-comentario');
    botones.forEach(boton => {
        boton.style.display = 'none';
    });

}
</script>
<style>
        @keyframes blink {
    50% {
        background-color:  #1e62af;
    }
}

.blinking {
    animation: blink 1s infinite;
}

#micIcon {
    filter: hue-rotate(180deg);
}
</style>