{% extends "admin/base_site.html" %}
{% block extrahead %}
    <script type="text/javascript" src="{{STATIC_URL}}js/jquery.min.js"></script>

    <script type="text/javascript">
      var campanias = { {%for campania in campanias%}'{{campania.id}}':'{{campania.categoria.tipo_de_origen_id}}', {%endfor%}};
      var vendedores = { {%for vendedor in vendedores%}'{{vendedor.id}}':'{{vendedor.supervisor_id}}', {%endfor%}};
      var pedidos = { {%for pedido in pedidos%}'{{pedido.id}}':'{{pedido.supervisor_id}}', {%endfor%}};
      $(InicializarForm);

      function InicializarForm(){
        ActualizarCampanias();
        $('#id_origen').change(ActualizarCampanias);
        ActualizarVendedoresYPedidos();
        $('#id_responsable').change(ActualizarVendedoresYPedidos);
      }

      function ActualizarCampanias(){
        ActualizarPorId('id_origen','id_campania', campanias);
      }

      function ActualizarVendedoresYPedidos(){
          ActualizarVendedores();
          ActualizarPedidos();
      }

      function ActualizarVendedores(){
        ActualizarPorId('id_responsable','id_vendedor', vendedores);
      }

      function ActualizarPedidos(){
        ActualizarPorId('id_responsable','id_pedido', pedidos);
      }

      function ActualizarPorId(campo_maestro, campo_dependiente, dependencia){
        var maestro= $('#'+campo_maestro).val()
        $('#'+campo_dependiente).children().each(function(){
            var id = $(this).val();
            if (maestro == dependencia[id] || id==''){
                $(this).show();
                $(this).removeAttr('disabled');
            }
            else{
                $(this).hide();
                $(this).attr('disabled', 'disabled');
            }
        });
        var ninguno =$('#'+campo_dependiente).children()[0];
        $(ninguno).attr('selected','selected');
      }

      function EnviarFormulario(){
          $('#submit_button').attr('disabled', 'disabled');
          $('#bin_form').submit();
      }

    </script>
{% endblock %}

{% block title %}Cargar prospectos vía CSV{% endblock %}

{% block content %}
<div id="content-main">
  <h1>Cargar prospectos vía CSV</h1>
    <h3>Seleccione el archivo a importar</h3>
    <a href="{% url 'admin:add-prospectos-csv-doc' %}">Documentación</a>
    <form enctype="multipart/form-data" action="" method="post" id="bin_form">
        {% csrf_token %}
        <fieldset class="module aligned ">
        {% if msg %}
            <span style="color:green;">{{msg}}</span>
        {% endif %}
        {% if err_filas %}
            </br><span style="color:red;">{{err_filas}}</span>
        {% endif %}
        {% if err_codigos %}
            </br><span style="color:red;">{{err_codigos}}</span>
        {% endif %}
        {% if err_vendedores %}
            </br><span style="color:red;">{{err_vendedores}}</span>
        {% endif %}
        {% if log_url %}
            </br></br><span style="color:red;"><a href="{{log_url}}">Descargar archivo con filas erróneas</a></span>
        {% endif %}
        {{ form.as_p }}
        </fieldset>
        <input id='submit_button' type="button" value="Subir" onclick="EnviarFormulario();"/>
    </form>
</div>
{% endblock %}

