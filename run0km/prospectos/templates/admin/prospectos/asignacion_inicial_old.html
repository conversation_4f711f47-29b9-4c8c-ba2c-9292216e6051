{% extends "admin/base_site.html" %}
{% load prospectos_utils %}

{% block extrahead %}
    <link rel="stylesheet" type="text/css" href="{{STATIC_URL}}admin/css/forms.css" />
    <link rel="stylesheet" type="text/css" href="{{STATIC_URL}}css/runkm-admin.css" />
    <script type="text/javascript" src="{{STATIC_URL}}js/jquery.min.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/system_unavailable.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/csrf_token.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/asignacion_inicial_old.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/admin_pedidos.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/spin.min.js"></script>
    <script type="text/javascript" src="{{STATIC_URL}}js/spinners.js"></script>

    <script type="text/javascript">
      var cantidad_filtrada_url = "{% url 'admin:cantidad_filtrada_admin' %}";
      var cantidad_filtrada_distribucion_url = "{% url 'admin:cantidad_filtrada_distribucion_admin' %}";
      var orden_de_filtros = ['origen', 'categorias', 'marcas', 'campanias', 'provincias','prefijos', 'cantidad'];
      var spinners_asignacion = {};
      var categorias= {% categorias_de_campania_json %};
      var campanias= {% campanias_json %};
      var vendedores = {% vendedores_json %};
      var pedidos = { {%for pedido in pedidos%}'{{pedido.id}}':'{{pedido.supervisor.id}}', {%endfor%} };
      var permitir_multiples_origenes = true;
      var para_asignacion = {% if para_asignacion %}true{% else %}false{% endif %};

      var usar_seleccion_previa = {{ usar_seleccion_previa }};
      var seleccion_previa = {{ seleccion_previa_json }};

      $(InicializarFormDeAsignacion);
      $(ActualizarAsignarA);
    </script>

    <style type="text/css">
    #id_pedido {min-width: 165px;}
    .spin-holder {
        position: relative;
        display: inline;
        margin-left: 150px;
        padding-top: 5px;"
    }
    </style>
{% endblock %}

{% block title %} {% if para_asignacion %} Asignación Inicial {%else%} Distribución Inicial {%endif%}{% endblock %}

{% block content %}
<h1>{% if para_asignacion %} Asignación Inicial {%else%} Distribución Inicial {%endif%}</h1>
<div id="content-main">
    <form enctype="multipart/form-data" action="" method="post">
        {% csrf_token %}
        {% if form.errors %}
         <p class="errornote">Por favor corrija los errores detallados abajo.</p>
        {% endif %}
        {{ form.non_field_errors }}
        <div>
        {% if para_asignacion %}
          <fieldset class="module aligned ">
              <h2>Condiciones iniciales:</h2>
              <div class="form-row"><div>
                {{ form.tipo_rechazo.errors }}
                {{ form.tipo_rechazo.label_tag }} {{ form.tipo_rechazo }}
              </div></div>
              <div class="form-row"><div>
                {{ form.rechazado_por.errors }}
                {{ form.rechazado_por.label_tag }} {{ form.rechazado_por}}
              </div></div>
          </fieldset>
        {% endif %}
        <fieldset class="module aligned ">
            <h2>Filtros</h2>

            {% if not para_asignacion %}
              <div class="form-row"><div>
                {{ form.responsable.errors }}
                {{ form.responsable.label_tag }} {{ form.responsable}}
              </div></div>
                <input type="hidden" name="accion" id="id_accion" value="asignar">
            {% endif %}

            <div class="form-row"><div>
              {{ form.origen.errors }}
              {{ form.origen.label_tag }}
              <div id="origen_spinner" class="spin-holder"></div>
              <div id="origen_container">
                {{ form.origen}}
              </div>
            </div></div>
            <div class="form-row"><div>
              {{ form.categorias.errors }}
              {{ form.categorias.label_tag }}
              <div id="categorias_spinner" class="spin-holder"></div>
              <div id="categorias_container">
                {{ form.categorias}}
              </div>
            </div></div>
            <div class="form-row"><div>
              {{ form.marcas.errors }}
              {{ form.marcas.label_tag }}
              <div id="marcas_spinner" class="spin-holder"></div>
              <div id="marcas_container" class="checkbox-list-container">
                <div><label for="all_marcas">
                    <input checked="" id="all_marcas" name="all_marcas" type="checkbox" value="1"> Todas</label><br/>
                </div>
                {{form.marcas }}
              </div>
            </div></div>
            <div class="form-row"><div>
              {{ form.campanias.errors }}
              {{ form.campanias.label_tag }}
              <div id="campanias_spinner" class="spin-holder"></div>
              <div id="campanias_container" class="checkbox-list-container">
                <div><label for="all_campanias">
                    <input id="all_campanias" name="all_campanias" type="checkbox" value="1"> Todas</label><br/>
                </div>
                {{ form.campanias }}
              </div>
            </div></div>

            <div class="form-row"><div>
              {{ form.provincias.errors }}
              {{ form.provincias.label_tag }}
              <div id="provincias_spinner" class="spin-holder"></div>
              <div id="provincias_container" class="checkbox-list-container">
                <div><label for="all_provincias">
                    <input checked="" id="all_provincias" name="all_provincias" type="checkbox" value="1"> Todas</label><br/>
                </div>
                {{ form.provincias }}
              </div>
            </div></div>

            <div class="form-row"><div>
              {{ form.prefijos.errors }}
              {{ form.prefijos.label_tag }}
              <div id="prefijos_spinner" class="spin-holder"></div>
              <div id="prefijos_container" class="checkbox-list-container">
                <div><label for="all_prefijos">
                    <input checked="" id="all_prefijos" name="all_prefijos" type="checkbox" value="1"> Todos</label><br/>
                </div>
                {{ form.prefijos }}
              </div>
            </div></div>
        </fieldset>

        <fieldset class="module aligned ">
            <h2>Cantidad a Asignar</h2>
            <div class="form-row">
              <div>
                {{ form.cantidad.errors }}
                {{ form.cantidad.label_tag }}
                <div id="cantidad_spinner" class="spin-holder"></div>
                <div id="cantidad_container">
                  {{ form.cantidad}} de <span id='total_filtrado' style="font-weight: bold;"></span>
                </div>
              </div>
            </div>
        </fieldset>

        <fieldset class="module aligned ">
            <h2>Asignacion</h2>

            {% if para_asignacion %}
              <div class="form-row"><div>
                {{ form.accion.errors }}
                {{ form.accion.label_tag }} {{ form.accion}}
              </div></div>
              <div class="form-row"><div>
                {{ form.responsable.errors }}
                {{ form.responsable.label_tag }} {{ form.responsable}}
              </div></div>
            {% endif %}

            <div class="form-row"><div>
              {{ form.asignar_a.errors }}
              {{ form.asignar_a.label_tag }} {{ form.asignar_a}}
            </div></div>
            <div class="form-row"><div>
              {{ form.vendedor.errors }}
              {{ form.vendedor.label_tag }} {{ form.vendedor}}
            </div></div>
            <div class="form-row"><div>
              {{ form.equipo.errors }}
              {{ form.equipo.label_tag }} {{ form.equipo}}
            </div></div>
            <div class="form-row"><div>
              {{ form.metodo.errors }}
              {{ form.metodo.label_tag }} {{ form.metodo}}
            </div></div>
            <div class="form-row"><div>
              {{ form.pedido.errors }}
              {{ form.pedido.label_tag }} {{ form.pedido}}
            </div></div>
        </fieldset>

        <input type="submit" value="Enviar" />
        </div>
    </form>
</div>
{% endblock %}

