# coding=utf-8
import calendar
import datetime
import json
import re

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.core.exceptions import ValidationError, ObjectDoesNotExist
from django.core.validators import MinValueValidator
from django.db import models
from django.db import transaction
from django.db.models import Q
from django.db.models.functions import Length
from django.utils import timezone
from django.utils.dates import MONTHS
from django.utils.timezone import localtime, now

from campanias.models import Campania
from concesionarias.rango_laboral import CalendarioLaboral
from core.validators import alphanumeric
from core.validators import car_price
from core.validators import car_price_length
from log_de_errores.models import LogDeError
from notificaciones import FormaDeEnvioEmail, FormaDeEnvioSMS, FormaDeEnvioWhatsapp
from prospectos.configuracion import CAMPOS_DE_PROSPECTO_PUBLICOS
from prospectos.models.adicionales import InformacionAdicionalDeProspecto
from prospectos.models.entrega_de_datos.metodos_de_seleccion import NingunVendedorSatisfaceError, \
    ConfiguracionDeEntrega, MetodoDeSeleccionPorFactorAsistido, SeleccionVaciaError
from prospectos.models.entrega_de_datos.opciones import ModoSeleccionDeVendedorChoices, MetodosDeAsignacionChoices
from prospectos.models.entrega_de_datos.restricciones import ListaDeRestriccionesDeVendedores, \
    RestriccionLimiteDatosDiario, RestriccionLimiteDatosNuevos, RestriccionPorUltimoAccesso
from prospectos.models.exceptions import ProspectoTieneVentaNoCanceladaException
from prospectos.models.managers import (ProspectoManager, ComentarioManager, CompraManager,
                                        RechazoManager, LogDeErrorChequeadorDeWhatsappManager,
                                        LogDeErrorNormalizadorManager, LogDeErrorDeInformacionDeRedesSocialesManager,
                                        LogDeErrorDeCRMManager,
                                        AsignacionDeProspectoQuerySet, ProveedorQuerySet, TagQuerySet)
from prospectos.models.querysets import PedidoDeProspectoQuerySet, FiltroDePedidoQuerySet, VentaQuerySet, \
    LlamadoQuerySet, LlamadoProgramadoCaducoQuerySet, LlamadaRealizadaQuerySet, MarcaDeTarjetaDeCreditoQueryset, \
    TarjetaDeProspectoQueryset, CampoExtraQuerySet
from prospectos.models.soporte import obtener_marca_blaca, obtener_proveedor_vacio
from prospectos.utils.opciones import ModoDeIngresoChoices
from prospectos.utils.parseo_de_informacion_de_redes_sociales import MostradorDeInformacionDeRedesSociales

ESTADOS = dict(N='Nuevo', P='En proceso', F='Finalizado', V='Vendido')
ESTADOS_DE_PROSPECTO = (
    ('N', ESTADOS['N']),
    ('P', ESTADOS['P']),
    ('F', ESTADOS['F']),
    ('V', ESTADOS['V']),
)
BOOL_CHOICES = ((True, 'Si'), (False, 'No'))
MAPPING = {
    'submission_date': 'fecha',
}


class Dummy(object):
    pass


# class Telefono(models.Model):
#    numero = models.CharField(max_length=64, blank=True, default='')
#    esta_activo = models.BooleanField(default=True)
#    telco = models.TextField(blank=True, default='')
#    es_movil = models.BooleanField(default=False)
#    esta_normalizado = models.BooleanField(default=False)
# Spam (true,false) indoca si pertenece a una base de spam, la cual no puede ser enviado ni ser llamado.
#    esta_spam_list = models.BooleanField(default=False)


class AsignacionDeProspecto(models.Model):
    prospecto = models.OneToOneField('Prospecto', related_name='asignacion')
    fecha_de_asignacion_a_vendedor = models.DateTimeField(null=True, blank=True, db_index=True)
    fecha_de_asignacion_a_supervisor = models.DateTimeField(null=True, blank=True, db_index=True)
    vendedor = models.ForeignKey('vendedores.Vendedor', related_name='asignaciones', null=True, blank=True,
                                 on_delete=models.SET_NULL, db_index=True)

    objects = AsignacionDeProspectoQuerySet.as_manager()

    def clean(self):
        super(AsignacionDeProspecto, self).clean()
        if self.prospecto.responsable and self.fecha_de_asignacion_a_supervisor is None:
            raise ValidationError(
                {'fecha_de_asignacion_a_vendedor':
                     'El prospecto tiene responsable pero no se ha actualizado la fecha de asignacion'})
        if self.prospecto.vendedor and self.fecha_de_asignacion_a_vendedor is None:
            raise ValidationError({
                'fecha_de_asignacion_a_vendedor':
                    'El prospecto tiene vendedor pero no se ha actualizado la fecha de asignacion'})
        if self.prospecto.vendedor != self.vendedor:
            raise ValidationError({
                'vendedor': 'La asignacion debe tener el mismo vendedor que el prospecto'})

    def obtener_vendedor(self):
        return self.vendedor

    def cambiar_vendedor_desde_prospecto(self, fecha):
        if not self.prospecto.tiene_vendedor():
            raise ValidationError(message='El prospecto no tiene vendedor asignado.')
        self.fecha_de_asignacion_a_vendedor = fecha
        self.vendedor = self.prospecto.obtener_vendedor()

    def cambiar_fecha_para_supervisor(self, fecha):
        if not self.prospecto.tiene_responsable():
            raise ValidationError(message='El prospecto no tiene responsable asignado.')
        self.fecha_de_asignacion_a_supervisor = fecha

    @classmethod
    def nuevo(cls, prospecto, vendedor=None,
              fecha_de_asignacion_a_vendedor=None, fecha_de_asignacion_a_supervisor=None):
        asignacion = cls(prospecto=prospecto, vendedor=vendedor,
                         fecha_de_asignacion_a_vendedor=fecha_de_asignacion_a_vendedor,
                         fecha_de_asignacion_a_supervisor=fecha_de_asignacion_a_supervisor)
        asignacion.save()
        return asignacion

    @classmethod
    def con_responsable(cls, prospecto, fecha_de_asignacion_a_supervisor):
        asignacion = cls.nuevo(prospecto,
                               fecha_de_asignacion_a_vendedor=None,
                               fecha_de_asignacion_a_supervisor=fecha_de_asignacion_a_supervisor)
        return asignacion


class DatosDeArchivadoParaProspecto(models.Model):
    fecha = models.DateField(blank=True, null=True, auto_now_add=True)
    prospecto = models.OneToOneField('Prospecto', on_delete=models.CASCADE, null=True, blank=True,
                                     related_name='archivado')

    @classmethod
    def nuevo(cls):
        prospecto_archivado = cls(fecha=now().date())
        prospecto_archivado.save()
        return prospecto_archivado


class Prospecto(models.Model):
    """
    Esta clase modela una multitud de entidades del dominio.
    Modela una persona que está interesada en comprar algo (Interes)
    y un vendedor que gestiona este interés para convertirlo en una venta. (Gestion de vendedor)
    Junto con cómo esta "proto-venta" se asignó dentro del sistema, qué gestion interna tuvo (Gestion interna)
    Por lo tanto este objeto incluye:
        Interes
            Interesado y Objecto (de interés)
        Gestión del Vendedor
        Gestion Interna
    Donde se pueda, se intentará aclarar a qué "subobjeto" una variable o método pertenece.
    Esto es un primer paso para eventualmente refactorizar este objeto y desacoplarlo.

    NOTA IMPORTANTE: Al modificar el prospecto y sus relaciones hay que mantener "sincronizado"
        MergeadorDeProspectos._configuraciones_por_defecto

    """
    VENDIDO = 'V'
    EN_PROCESO = 'P'
    FINALIZADO = 'F'
    NUEVO = 'N'
    _NOMBRE_DESCONOCIDO = 'nn'

    # Gestion del vendedor
    vendedor = models.ForeignKey('vendedores.Vendedor', related_name='prospectos', null=True, blank=True,
                                 on_delete=models.SET_NULL, verbose_name='Vendedor Asignado', db_index=True)
    responsable = models.ForeignKey('vendedores.Vendedor', related_name='prospectos_a_cargo', null=True, blank=True,
                                    on_delete=models.SET_NULL, db_index=True)
    fecha = models.DateTimeField(blank=True, null=True)

    # Interes - Interesado
    nombre = models.CharField(max_length=64, blank=True, default='')
    nombre_alternativo = models.CharField(max_length=64, blank=True, default='')
    # Todo: reificarlo a un objeto telefono
    prefijo = models.CharField(max_length=5, blank=True, default='')
    telefono = models.CharField(max_length=64, blank=True, default='')
    telefono_sin_normalizar = models.CharField(max_length=128, blank=True, default='')
    telefono_activo = models.BooleanField(default=True)
    telco = models.CharField(max_length=255, blank=True, default='')
    es_telefono_movil = models.NullBooleanField()
    esta_telefono_normalizado = models.BooleanField(default=False)
    telefono_bien_constituido = models.BooleanField(default=False)
    tiene_whatsapp = models.NullBooleanField()
    imagen_de_perfil_de_whatsapp = models.ImageField(upload_to='whatsapp', null=True, blank=True)
    esta_spam_list = models.NullBooleanField()
    # fin_telefono
    email = models.CharField(max_length=64, blank=True, default='')
    email_activo = models.BooleanField(default=True)
    mensaje = models.CharField(max_length=255, blank=True, default='')


    # Gestión del vendedor
    estado = models.CharField(max_length=1, choices=ESTADOS_DE_PROSPECTO, default='N')

    # Interes - Interesado
    provincia = models.CharField(max_length=64, blank=True)
    localidad = models.CharField(max_length=64, blank=True)

    # Interes - Objeto
    _marca = models.ForeignKey('prospectos.Marca', related_name='_prospectos',
                               default=obtener_marca_blaca, on_delete=models.SET_DEFAULT)
    _modelos = models.ManyToManyField('prospectos.Modelo', related_name='_prospectos', blank=True)

    # Gestion interna
    campania = models.ForeignKey('campanias.Campania', related_name='prospectos', verbose_name="campaña")
    fecha_creacion = models.DateTimeField(auto_now_add=True, db_index=True)
    exportado = models.BooleanField(default=False)
    proveedor = models.ForeignKey('Proveedor', related_name='prospectos',
                                  default=obtener_proveedor_vacio, on_delete=models.SET_DEFAULT)
    informacion_de_redes_sociales_pedida = models.BooleanField(default=False)
    pedido = models.ForeignKey('PedidoDeProspecto', related_name='prospectos_asignados', null=True, blank=True,
                               on_delete=models.SET_NULL)
    consumido_en_pedido = models.PositiveIntegerField(default=0)
    _tiempo_de_respuesta = models.IntegerField(null=True, blank=True)
    _grupo_de_repetidos = models.ForeignKey('prospectos.GrupoDeRepetidos', related_name='_prospectos',
                                            null=True, blank=True)
    _modo_de_ingreso = models.CharField(max_length=1, choices=ModoDeIngresoChoices.choices(), null=True, blank=True)
    _puede_circular = models.BooleanField(default=True)

    objects = ProspectoManager()

    # Gestion del vendedor
    def obtener_comentarios(self):
        return self.comentarios.all()

    def tarjetas_de_credito(self):
        if hasattr(self, '_tarjetas_de_credito'):
            return self._tarjetas_de_credito.all()
        return None

    def obtener_estado(self):
        return self.estado

    def obtener_mensaje(self):
        return self.mensaje

    def obtener_geolocalizacion(self):
        geolocalizacion = self.geolocalizacion
        return geolocalizacion

    def obtener_pais(self):
        return "Argentina"

    def obtener_provincia(self):
        geolocalizacion = self.obtener_geolocalizacion()
        if geolocalizacion:
            return self.provincia or geolocalizacion.obtener_provincia()
        return self.provincia

    def obtener_localidad(self):
        geolocalizacion = self.obtener_geolocalizacion()
        if geolocalizacion:
            return self.localidad or geolocalizacion.obtener_localidad()
        return self.localidad

    def obtener_latitud(self):
        geolocalizacion = self.obtener_geolocalizacion()
        if geolocalizacion:
            return geolocalizacion.obtener_latitud()

    def obtener_longitud(self):
        geolocalizacion = self.obtener_geolocalizacion()
        if geolocalizacion:
            return geolocalizacion.obtener_longitud()

    def obtener_ip(self):
        geolocalizacion = self.obtener_geolocalizacion()
        if geolocalizacion:
            return geolocalizacion.obtener_ip()

    def obtener_vendedor(self):
        return self.vendedor

    def obtener_responsable(self):
        return self.responsable

    def obtener_gestor(self):
        vendedor = self.obtener_vendedor()
        if vendedor is not None:
            return vendedor
        return self.obtener_responsable()

    def obtener_venta_activa(self):
        return self.ventas.exclude(estado=Venta.CANCELADA).first()

    def obtener_informacion_adicional(self):
        return InformacionAdicionalDeProspecto.para(self)

    def obtener_telefono_sin_normalizar(self):
        return self.telefono_sin_normalizar

    def obtener_conversaciones(self):
        return self.conversacion.all().excluir_conversaciones_de_chat_mal_formadas()

    def marca_de_tarjeta_de_credito(self):
        tarjeta = self.tarjetas_de_credito().first()
        if tarjeta is None:
            return None
        else:
            return tarjeta.marca()

    # Interes - Interesado
    def obtener_email(self):
        return self.email

    def obtener_prefijo(self):
        return self.prefijo

    def obtener_telefono(self):
        return self.telefono

    def obtener_telefonos_extras(self):
        return self.telefono_extra.all()

    def obtener_emails_extra(self):
        return self.email_extra.all()

    def obtener_campos_extra(self):
        return self.campos_extra.all()

    def obtener_valor_campo_extra(self, nombre, valor_por_defecto):
        try:
            return self.campos_extra.obtener_valor(nombre, self)
        except ObjectDoesNotExist:
            return valor_por_defecto

    def tiene_telefono_celular(self):
        return self.es_telefono_movil

    def perfil_facebook(self):
        if hasattr(self, 'perfil_de_facebook'):
            return self.perfil_de_facebook
        else:
            return None

    def facebook_url(self):
        if self.perfil_facebook():
            return self.perfil_facebook().obtener_url()
        redes_sociales_facebook = self.informacion_de_redes_sociales.filter(
            prospecto=self, tipo=InformacionDeRedesSociales.FACEBOOK)
        if redes_sociales_facebook.exists():
            return redes_sociales_facebook[0].valor_a_mostrar()
        return ''

    def tiene_nombre(self):
        return self.nombre and (self.nombre.lower().replace('.', '') != 'nn')

    def nombre_completo(self):
        if self.nombre and self.nombre_alternativo:
            return '%s (Ex %s)' % (self.nombre_alternativo, self.nombre)
        else:
            return self.obtener_nombre()

    def avatar(self):
        imagen_de_perfil = self.imagen_de_perfil()
        if imagen_de_perfil:
            imagen_url = imagen_de_perfil.url
        else:
            imagen_url = self.obtener_foto() or settings.STATIC_URL + 'img/generic-user.png'
        return imagen_url

    def nombre_y_apellido(self):
        nombre = self.obtener_nombre()
        particion = nombre.split()
        if len(particion) > 2:
            return particion[0], particion[1]
        else:
            return particion[0], 'Desconocido'

    def obtener_foto(self):
        if self.perfil_facebook():
            return self.perfil_facebook().obtener_foto()
        fotos = self.informacion_de_redes_sociales.filter(tipo=InformacionDeRedesSociales.FOTO)
        foto = fotos.first()
        if foto:
            return foto.valor_a_mostrar()
        else:
            return None

    def obtener_informacion_de_redes_sociales(self):
        return self.informacion_de_redes_sociales.all()

    def obtener_nombre(self):
        if self.tiene_nombre():
            return self.nombre
        elif self.nombre_alternativo:
            return self.nombre_alternativo
        else:
            return self._NOMBRE_DESCONOCIDO

    def tiene_telefono_para_whatsapp(self):
        return self.telefono_para_whatsapp() is not None

    def tiene_informacion_de_redes_sociales(self):
        return self.informacion_de_redes_sociales.exists()

    # Devuelve el primer telefono activo

    def telefono_para_whatsapp(self):
        if self.telefono_activo and len(self.telefono) > 0 and (
                self.tiene_whatsapp is not False):  # en caso de None permite enviar
            return self.telefono
        else:
            activos = self.telefono_extra.filter(Q(activo=True) & ~Q(tiene_whatsapp=False))
            if activos.count() > 0:
                return activos[0].telefono
        return None

    def telefonos_activos(self):
        telefonos = []
        if self.telefono_activo and len(self.telefono) > 0:
            telefonos.append(self.telefono)
        # Asume que es llamado mediante un prefetch_related('telefono_extra')
        for telefono in self.telefono_extra.all():
            if telefono.activo and len(telefono.telefono) > 0:
                telefonos.append(telefono.telefono)
        return telefonos

    def celular(self):
        # en caso de es_telefono_movil None permite enviar
        if self.telefono_activo and len(self.telefono) > 0 and self.es_telefono_movil is not False:
            return self.telefono
        else:
            celulares = self.telefono_extra.annotate(telefono_len=Length('telefono')).filter(
                activo=True, telefono_len__gte=7).filter(~Q(es_telefono_movil=False))
            if celulares.exists():
                return celulares.first().telefono
        return None

    def imagen_de_perfil(self):
        if self.imagen_de_perfil_de_whatsapp:
            return self.imagen_de_perfil_de_whatsapp
        else:
            imagenes = self.telefono_extra.filter(imagen_de_perfil_de_whatsapp__isnull=False)
            imagen = imagenes.first()
            if imagen:
                return imagen.imagen_de_perfil_de_whatsapp
        return None

    def telefonos_extra_llamables(self):
        return self.telefono_extra.filter(esta_telefono_normalizado=True, telefono_bien_constituido=True,
                                          esta_spam_list=False)

    # Interes - Objeto
    def obtener_marca(self):
        return self._marca

    def obtener_modelos(self):
        return self._modelos.all()

    def modelos_como_string(self, char_separador=' | '):
        return char_separador.join([model.nombre() for model in self.obtener_modelos()])

    def tiene_marca(self):
        return not self.obtener_marca().es_marca_blanca()

    def tiene_modelo(self):
        return self._modelos.exists()

    # Gestion interna
    def obtener_rechazos(self):
        return self.rechazos.all()

    def obtener_proveedor(self):
        if hasattr(self, 'proveedor'):
            return self.proveedor

    def obtener_campania(self):
        return self.campania

    def puede_circular(self):
        return self._puede_circular

    def fecha_de_asignacion_a_vendedor(self):
        if hasattr(self, 'asignacion'):
            asignacion = self.asignacion
            if not asignacion.fecha_de_asignacion_a_vendedor and self.vendedor:
                return self.fecha_de_asignacion_a_supervisor()
            else:
                return asignacion.fecha_de_asignacion_a_vendedor
        else:
            return None

    def fecha_de_asignacion_a_supervisor(self):
        if hasattr(self, 'asignacion'):
            return self.asignacion.fecha_de_asignacion_a_supervisor
        else:
            return None

    # Gestion interna - Repetidos

    def grupo_de_repetidos(self):
        return self._grupo_de_repetidos

    def tiene_repetidos(self):
        return self.grupo_de_repetidos() is not None

    def siguiente_repetido_para_vendedor(self):
        grupo = self.grupo_de_repetidos()
        if grupo:
            return grupo.siguiente_para_vendedor_a(prospecto=self)
        return None

    def siguiente_repetido_para_supervisor(self):
        grupo = self.grupo_de_repetidos()
        if grupo:
            return grupo.siguiente_para_supervisor_a(prospecto=self)
        return None

    # No clasificados
    def obtener_fecha(self):
        return self.fecha or self.fecha_creacion

    def obtener_fecha_de_creacion(self):
        return self.fecha_creacion

    def obtener_planes(self):
        if hasattr(self, '_planes'):
            return self._planes.all()

    # ---
    def asociar_archivado(self, archivado):
        """
        A usar UNICAMENTE por GestorDeProspecto.
        """
        self.vendedor = None
        self.archivado = archivado
        self.save()

    def cambiar_marca_por(self, marca):
        """
        A usar UNICAMENTE por GestorDeProspecto.
        """
        self._marca = marca
        self.save()

    def enlazar_con_prospecto_original(self, relacion_original_y_repetidos):
        """
        A usar UNICAMENTE por GestorDeProspecto para Prospectos Repetidos.
        """
        self._relacion_con_prospecto_original = relacion_original_y_repetidos
        self.full_clean()
        self.save()

    def reemplazar_modelos(self, modelos):
        """
        A usar UNICAMENTE por GestorDeProspecto.
        """
        self._modelos = modelos
        self.full_clean()
        self.save()

    def nombre_coincide_parcialmente_con_redes_sociales(self):
        info_redes_sociales = self.informacion_de_redes_sociales.filter(prospecto=self, tipo='Nombre completo')
        if (self.nombre == '' and self.nombre_alternativo == '') or not info_redes_sociales.exists():
            return True
        else:
            for info in info_redes_sociales:
                splice_nombre_completo = info.valor_a_mostrar().split()
                for nombre in splice_nombre_completo:
                    if nombre in self.nombre or nombre in self.nombre_alternativo:
                        return True
            return False

    def asignar_pedido(self, pedido):
        self.pedido = pedido
        self.save()

    def calidad(self):
        return self.campania.nombre_origen()

    def obtener_email_activo(self):
        if self.email and self.email_activo:
            return self.email
        else:
            activos = self.email_extra.filter(activo=True)
            if activos.count() > 0:
                return activos.first().email
        return None

    def emails_activos(self):
        emails = []
        if self.email_activo and len(self.email) > 0:
            emails.append(self.email)
        # Asume que es llamado mediante un prefetch_related('emails_extra')
        for email in self.email_extra.all():
            if email.activo and len(email.email) > 0:
                emails.append(email.email)
        return emails

    def esta_archivado(self):
        return hasattr(self, 'archivado')

    def tiene_venta_no_cancelada(self):
        return self.ventas.exclude(estado=Venta.CANCELADA).exists()

    def llamados_realizados(self):
        return self.llamadas_realizadas.all()

    def tiene_responsable(self):
        return self.responsable is not None

    def tiene_vendedor(self):
        return self.vendedor_id is not None

    def tiene_vendedor_asignado_a(self, vendedor):
        return self.vendedor == vendedor

    def tiempo_de_respuesta(self):
        # TODO: trabajar correctamente con la excepcion

        if self._tiempo_de_respuesta is not None:
            return self._tiempo_de_respuesta
        else:
            try:
                return CalendarioLaboral.default().horas_laborales_entre(
                    fecha_y_hora_final=timezone.now(),
                    fecha_y_hora_inicial=self.fecha_de_asignacion_a_vendedor()).total_seconds()
            except ValueError:
                return 0

    def actualizar_tiempo_de_respuesta(self):
        horario_laboral = CalendarioLaboral.default()
        self._tiempo_de_respuesta = horario_laboral.horas_laborales_entre(
            fecha_y_hora_final=timezone.now(),
            fecha_y_hora_inicial=self.fecha_de_asignacion_a_vendedor()).total_seconds()
        self.save()

    def save(self, *args, **kwargs):
        self.prefijo = Prospecto.dar_formato_a_prefijo(self.prefijo)
        if self.pedido:
            self.consumido_en_pedido = self.campania.categoria.valor
        else:
            self.consumido_en_pedido = 0
        super(Prospecto, self).save(*args, **kwargs)

    def __str__(self):
        vendedor = self.vendedor.user.username if self.vendedor else 'Sin asignar'
        return "%s | %s | %s" % (self.id, self.campania, vendedor)

    def tiene_conversacion_de_chat(self):
        from conversaciones.models import Conversacion
        return Conversacion.objects.filter(prospecto=self, tipo=Conversacion.TIPO_CHAT) != []

    def cambiar_a_estado(self, estado):
        self._validar_estado(estado)
        self.estado = estado
        self.full_clean()
        self.save()

    def _validar_estado(self, estado):
        if estado not in [Prospecto.EN_PROCESO, Prospecto.VENDIDO, Prospecto.FINALIZADO]:
            raise ValidationError('El estado es inválido.')

    def es_nuevo(self):
        return self.estado == Prospecto.NUEVO

    @property
    def en_proceso(self):
        return not self.finalizado and not self.vendido

    @property
    def finalizado(self):
        # return hasattr(self, 'finalizacion')
        return self.estado == self.FINALIZADO

    @property
    def vendido(self):
        # TODO: Oscuro, se actualiza el estado en la signal `al_vender`
        return self.estado == self.VENDIDO

    def esta_finalizado(self):
        return self.finalizado

    def reactivar_seguimiento(self):
        if self.vendido or self.en_proceso:
            raise ValidationError('Para reactivar el seguimiento, el prospecto no debe estar en proceso ni vendido.')
        # ToDo: parche de fin de semana largo
        if self.finalizacion and self.finalizacion.id is not None:
            self.finalizacion.delete()  # post delete actualiza el estado

    def tiene_llamado(self):
        return hasattr(self, 'llamado')

    def tiene_llamado_vencido(self):
        try:
            return self.llamado.esta_vencido()
        except Llamado.DoesNotExist:
            return False

    def borrar_llamado(self):
        try:
            if self.tiene_llamado():
                llamado = self.llamado
                llamado.delete()
        except Llamado.DoesNotExist:
            pass

    def campos_publicos(self):
        """
        Campos para mostrar el prospecto en el listado o en el detalle de prospectos.
        :return:
        """
        datos = Dummy()
        for campo in CAMPOS_DE_PROSPECTO_PUBLICOS:
            try:
                val = getattr(self, campo)
            except:
                val = None
            setattr(datos, campo, val)
        setattr(datos, 'tiene_informacion_de_redes_sociales', self.tiene_informacion_de_redes_sociales())
        setattr(datos, 'tiene_telefono_para_whatsapp', self.tiene_telefono_para_whatsapp())
        setattr(datos, 'telefono_para_whatsapp', self.telefono_para_whatsapp())
        return datos

    def ultimos_comentarios_manuales(self):
        return self.comentarios.ultimos_comentarios_manuales()

    def esta_sin_atender_hace_demasiado(self):
        if self.estado == Prospecto.NUEVO and self.vendedor:
            limite = self.vendedor.fecha_limite_para_atender_prospectos_en_tiempo()
            fecha_de_asignacion_a_vendedor = self.fecha_de_asignacion_a_vendedor()
            return fecha_de_asignacion_a_vendedor is not None and fecha_de_asignacion_a_vendedor <= limite
        return False

    def poseedor_de_la_venta(self):
        return self.obtener_vendedor() or self.obtener_responsable()

    @classmethod
    def nuevo(cls, omitir_validar_email=False, modo_de_ingreso=None, puede_circular=True, **datos_del_prospecto):
        if not cls._tiene_datos_de_contacto(datos_del_prospecto):
            raise ValidationError('Datos de contactos invalidos.')
        if modo_de_ingreso is not None:
            modo_de_ingreso = ModoDeIngresoChoices.key_for(modo_de_ingreso.__class__)
        prospecto = cls(_modo_de_ingreso=modo_de_ingreso, _puede_circular=puede_circular, **datos_del_prospecto)
        return cls.validar_y_guardar_prospecto(prospecto, omitir_validar_email, **datos_del_prospecto)

    @classmethod
    def nuevo_sin_validar_email(cls, **datos_del_prospecto):
        prospecto = cls(**datos_del_prospecto)
        return cls.validar_y_guardar_prospecto(prospecto, True, **datos_del_prospecto)

    @classmethod
    def validar_y_guardar_prospecto(cls, prospecto, omitir_validar_email=False, **datos_del_prospecto):
        cls.detectar_error_de_validacion_de_campos(prospecto, omitir_validar_email)
        telefono = datos_del_prospecto.get('telefono')
        telefono_normalizado = cls.normalizar_telefono(telefono)
        if len(prospecto.prefijo) > 5:
            prospecto.prefijo = ''
        prospecto.telefono = telefono_normalizado
        prospecto.telefono_sin_normalizar = cls.limpiar_telefono_no_normalizado(telefono)
        prospecto.full_clean()
        prospecto.save()
        return prospecto

    @classmethod
    def detectar_error_de_validacion_de_email(cls, email):
        if email and '@' not in email:
            raise ValidationError('Introduzca una dirección de email válida.')

    @classmethod
    def dar_formato_a_prefijo(cls, prefijo):
        if prefijo:
            prefijo = re.sub("\D", "", prefijo)
            if len(prefijo) > 0:
                prefijo = prefijo[0:5].zfill(5)
        return prefijo

    @classmethod
    def normalizar_telefono(cls, telefono):
        telefono_normalizado = telefono
        if not telefono_normalizado:
            return ''
        # telefono_normalizado = re.sub("\D", "", telefono_normalizado)
        telefono_normalizado = re.sub("^0+", "", telefono_normalizado)
        return telefono_normalizado

    @classmethod
    def detectar_error_de_validacion_de_campos(cls, prospecto, omitir_validar_email):
        if not omitir_validar_email:
            cls.detectar_error_de_validacion_de_email(prospecto.email)

    @classmethod
    def _tiene_datos_de_contacto(cls, datos_del_prospecto):
        return datos_del_prospecto.get('telefono') or datos_del_prospecto.get('email')

    class Meta:
        verbose_name = 'prospecto'
        verbose_name_plural = 'prospectos'

    @classmethod
    def limpiar_telefono_no_normalizado(cls, telefono):
        if not telefono:
            return ''
        telefono_sin_espacios = telefono.replace(' ', '')  # Remueve whitespaces del final y del inicio.
        telefono_normalizado = re.sub("[^0-9]", "", telefono_sin_espacios)  # Remueve caracteres no numericos.
        return telefono_normalizado

    def obtener_distribuidor(self):
        return self.campania.categoria.distribuidor


class Comentario(models.Model):
    SEMANTICA_DESCONOCIDA = 'DESCONOCIDA'
    NO_CONTESTA = 'NO_CONTESTA'
    OCUPADO = 'OCUPADO'
    CONTESTADOR = 'CONTESTADOR'
    NO_PUEDE_AHORA = 'NO_PUEDE_AHORA'
    NO_ESTA = 'NO_ESTA'
    LLAMAR_MAS_TARDE = 'LLAMAR_MAS_TARDE'

    SEMANTICA_CHOICES = (
        (SEMANTICA_DESCONOCIDA, 'Semantica desconocida'),
        (NO_CONTESTA, 'No contesta'),
        (OCUPADO, 'Ocupado'),
        (CONTESTADOR, 'Contestador'),
        (NO_PUEDE_AHORA, 'No puede ahora'),
        (NO_ESTA, 'No está'),
        (LLAMAR_MAS_TARDE, 'Llamar más tarde'),
    )

    datetime = models.DateTimeField()
    prospecto = models.ForeignKey('Prospecto', related_name='comentarios')
    vendedor = models.ForeignKey('vendedores.Vendedor', related_name='comentarios')
    comentario = models.TextField()
    etiqueta_semantica = models.CharField(max_length=20, choices=SEMANTICA_CHOICES, default=SEMANTICA_DESCONOCIDA)
    automatico = models.BooleanField(default=False)
    # audio = models.FileField(upload_to='audios-de-comentarios/', null=True, blank=True)
    
    objects = ComentarioManager()

    def _es_el_primer_comentario_del_prospecto(self):
        return self.prospecto.comentarios.count() == 1

    @classmethod
    def nuevo(cls, prospecto, vendedor, comentario, audio=None, automatico=False,
              etiqueta_semantica=SEMANTICA_DESCONOCIDA, fecha=None):
        fecha_or_ahora = fecha or timezone.now()
        comentario = cls(prospecto=prospecto,
                         vendedor=vendedor,
                         comentario=comentario,
                         #audio=audio,
                         automatico=automatico,
                         etiqueta_semantica=etiqueta_semantica,
                         datetime=fecha_or_ahora)

        comentario.full_clean()
        comentario.save()

        if comentario._es_el_primer_comentario_del_prospecto() and prospecto.fecha_de_asignacion_a_vendedor():
            prospecto.actualizar_tiempo_de_respuesta()

        return comentario

    @classmethod
    def nuevo_con_semantica(cls, prospecto, vendedor, etiqueta_semantica, fecha):
        cls._validar_etiqueta_con_semantica(etiqueta_semantica)
        return cls.nuevo(prospecto, vendedor,
                         comentario=cls._contenido_para(etiqueta_semantica),
                         etiqueta_semantica=etiqueta_semantica, automatico=False, fecha=fecha)

    @classmethod
    def _validar_etiqueta_con_semantica(cls, etiqueta_semantica):
        if etiqueta_semantica not in cls._etiquetas_con_semantica():
            raise ValidationError('%s no es una etiqueta valida' % etiqueta_semantica)

    @classmethod
    def _etiquetas_con_semantica(cls):
        return [each[0] for each in list(cls.SEMANTICA_CHOICES) if each[0] != cls.SEMANTICA_DESCONOCIDA]

    @classmethod
    def _contenido_para(cls, etiqueta_semantica):
        for etiqueta_con_descripcion in list(cls.SEMANTICA_CHOICES):
            if etiqueta_con_descripcion[0] == etiqueta_semantica:
                return etiqueta_con_descripcion[1]
        return ''

    def texto(self):
        return self.comentario

    def fecha(self):
        return self.datetime

    def es_automatico(self):
        return self.automatico

    def obtener_prospecto(self):
        return self.prospecto

    def obtener_vendedor(self):
        return self.vendedor

    def obtener_etiqueta_semantica(self):
        return self.etiqueta_semantica

    def __str__(self):
        return "%s | %s" % (self.prospecto.id, self.comentario)

    class Meta:
        verbose_name = 'comentario'
        verbose_name_plural = 'comentarios'


class MarcaYaLLamadoOtraConcesionaria(models.Model):
    prospecto = models.ForeignKey('Prospecto', related_name='marcas_ya_llamado')
    vendedor = models.ForeignKey('vendedores.Vendedor', related_name='marcas_ya_llamado')

    def __str__(self):
        return "%s | %s" % (self.prospecto.id, self.vendedor.id)

    class Meta:
        verbose_name = 'marca_ya_llamado'
        verbose_name_plural = 'marcas_ya_llamado'

    @classmethod
    def nuevo(cls, prospecto, vendedor):
        marca = MarcaYaLLamadoOtraConcesionaria(prospecto=prospecto, vendedor=vendedor)
        marca.full_clean()
        marca.save()
        return marca


class RespuestaDeCuestionarioDeLlamadaRealizada(models.Model):
    llamada = models.ForeignKey('LlamadaRealizada', related_name='respuestas_de_cuestionario')
    pregunta = models.CharField(max_length=255, blank=True, default='')
    contenido = models.TextField()

    @classmethod
    def nueva(cls, llamada, pregunta, contenido):
        respuesta = cls.objects.create(llamada=llamada, pregunta=pregunta, contenido=contenido)
        return respuesta


class LlamadaRealizada(models.Model):
    prospecto = models.ForeignKey('Prospecto', related_name='llamadas_realizadas')
    fecha_comienzo = models.DateTimeField()
    duracion = models.PositiveIntegerField(default=0, blank=True, null=True)

    objects = LlamadaRealizadaQuerySet.as_manager()

    class Meta:
        unique_together = ('prospecto', 'fecha_comienzo')

    def agregar_respuestas(self, respuestas):
        respuestas_de_cuestionario = []
        for pregunta, contenido in respuestas:
            respuesta = RespuestaDeCuestionarioDeLlamadaRealizada(llamada=self,
                                                                  pregunta=pregunta,
                                                                  contenido=contenido)
            respuestas_de_cuestionario.append(respuesta)
        RespuestaDeCuestionarioDeLlamadaRealizada.objects.bulk_create(respuestas_de_cuestionario)

    def agregar_respuesta(self, pregunta, contenido):
        RespuestaDeCuestionarioDeLlamadaRealizada.nueva(self, pregunta, contenido)

    @classmethod
    def nueva(cls, prospecto, fecha_comienzo, duracion):
        llamada = cls.objects.create(prospecto=prospecto, fecha_comienzo=fecha_comienzo, duracion=duracion)
        return llamada

    @classmethod
    def para(cls, prospecto, fecha_comienzo, duracion):
        llamada = cls.objects.get(prospecto=prospecto, fecha_comienzo=fecha_comienzo, duracion=duracion)
        return llamada

    @classmethod
    def obtener_o_crear(cls, prospecto, fecha_comienzo, duracion):
        llamada, fue_creado = cls.objects.get_or_create(
            prospecto=prospecto, fecha_comienzo=fecha_comienzo, duracion=duracion)
        return llamada

    def obtener_duracion(self):
        return self.duracion


class Llamado(models.Model):
    """
        Representa una llamada programada! Cambiarle el nombre

        datetime representa la fecha de creación del llamado.
        fecha representa la fecha de la llamada programada
    """
    datetime = models.DateTimeField(auto_now_add=True)
    prospecto = models.OneToOneField('Prospecto', related_name='llamado')
    # Habria que borrar este campo, lo dejo por compatibilidad hacia atras. Seria como el vendedor que programo
    # la llamada
    vendedor = models.ForeignKey('vendedores.Vendedor', related_name='llamados', null=True, blank=True)
    fecha = models.DateTimeField(db_index=True)

    objects = LlamadoQuerySet.as_manager()

    class Meta:
        verbose_name = 'llamado programado'
        verbose_name_plural = 'llamados programados'

    def obtener_fecha_de_creacion(self):
        return self.datetime

    def obtener_prospecto(self):
        return self.prospecto

    def obtener_vendedor(self):
        return self.prospecto.vendedor

    def vendedor_programador(self):
        return self.vendedor

    def obtener_fecha(self):
        return self.fecha

    def esta_vencido(self):
        return self.fecha < timezone.now()

    @classmethod
    def nuevo(cls, prospecto, fecha, vendedor_programador=None):
        llamado = Llamado(prospecto=prospecto, fecha=fecha, vendedor=vendedor_programador)
        llamado.save()
        return llamado


class LlamadoProgramadoCaduco(models.Model):
    """
        Representa una llamada programada que ya no está vigente
    """
    _fecha_de_creacion = models.DateTimeField()
    _prospecto = models.ForeignKey('Prospecto', related_name='_llamados_programados_caducos')
    _vendedor = models.ForeignKey('vendedores.Vendedor', related_name='_llamados_programados_caducos')
    _fecha_programada = models.DateTimeField(db_index=True)

    objects = LlamadoProgramadoCaducoQuerySet.as_manager()

    def obtener_fecha_de_creacion(self):
        return self._fecha_de_creacion

    def obtener_prospecto(self):
        return self._prospecto

    def obtener_vendedor(self):
        return self._vendedor

    def obtener_fecha_programada(self):
        return self._fecha_programada

    @classmethod
    def nuevo_desde(cls, llamado_programado):
        llamado_programado_caduco = cls(
            _fecha_de_creacion=llamado_programado.obtener_fecha_de_creacion(),
            _vendedor=llamado_programado.obtener_vendedor(),
            _fecha_programada=llamado_programado.obtener_fecha(),
            _prospecto=llamado_programado.obtener_prospecto()
        )
        llamado_programado_caduco.save()
        return llamado_programado_caduco


class Venta(models.Model):
    APROBADA = 'A'
    CANCELADA = 'C'
    PENDIENTE = 'P'
    ESTADO_CHOICES = (
        (APROBADA, 'Aprobada'),
        (CANCELADA, 'Cancelada'),
        (PENDIENTE, 'Pendiente'),
    )
    datetime = models.DateTimeField(auto_now_add=True)
    prospecto = models.ForeignKey('Prospecto', related_name='ventas', on_delete=models.CASCADE)
    vendedor = models.ForeignKey('vendedores.Vendedor', related_name='ventas')
    marca = models.CharField(max_length=64, default='')
    modelo = models.CharField(max_length=64, default='')
    fecha_de_realizacion = models.DateField()
    fecha_de_aprobacion = models.DateField(null=True, auto_now_add=True)
    fecha_de_cancelacion = models.DateField(null=True, blank=True)
    motivo_de_cancelacion = models.CharField(max_length=220, null=True, blank=True, default='')
    numero_de_contrato = models.CharField(max_length=20, null=True, blank=True, validators=[alphanumeric])
    precio = models.CharField(max_length=20, validators=[car_price, car_price_length])
    estado = models.CharField(
        max_length=1,
        choices=ESTADO_CHOICES,
        default=APROBADA,
    )

    objects = VentaQuerySet.as_manager()

    class Meta:
        verbose_name = 'venta'
        verbose_name_plural = 'ventas'

    @classmethod
    def nueva(cls, prospecto, vendedor, marca, modelo, fecha_de_realizacion, numero_de_contrato, precio):
        if prospecto.tiene_venta_no_cancelada():
            raise ProspectoTieneVentaNoCanceladaException.nuevo()
        else:
            venta = cls(prospecto=prospecto, vendedor=vendedor, marca=marca, modelo=modelo, precio=precio,
                        numero_de_contrato=numero_de_contrato, fecha_de_realizacion=fecha_de_realizacion)
            venta.full_clean()
            venta.save()
            return venta

    def calidad(self):
        return self.prospecto.calidad()

    def esta_bloqueada(self):
        try:
            menor_mes_de_realizacion = self.fecha_de_realizacion.month < self.fecha_de_aprobacion.month
            mismo_anio = self.fecha_de_aprobacion.year == self.fecha_de_realizacion.year
            menor_anio = self.fecha_de_realizacion.year < self.fecha_de_aprobacion.year
            return self.aprobada() and ((menor_mes_de_realizacion and mismo_anio) or menor_anio)
        except AttributeError:
            # Ventas sin fecha de aprobacion no estan bloqueadas
            return False

    def __str__(self):
        return "Venta del prospecto %s realizada por %s" % (self.prospecto.id, self.vendedor)

    def fecha(self):
        if self.aprobada():
            return self.fecha_de_aprobacion
        else:
            return self.fecha_de_realizacion

    def mes_de_realizacion(self):
        return self.fecha_de_realizacion.month

    def obtener_precio(self):
        return self.precio

    def obtener_modelo(self):
        return self.modelo

    def obtener_vendedor(self):
        return self.vendedor

    def anio_de_realizacion(self):
        return self.fecha_de_realizacion.year

    def aprobada(self):
        return self.estado == self.APROBADA

    def cancelada(self):
        return self.estado == self.CANCELADA

    def pendiente(self):
        return self.estado == self.PENDIENTE

    def aprobar(self):
        if self.aprobada():
            raise ValidationError('No se puede aprobar una Venta ya aprobada.')
        hoy = localtime(now()).date()
        self.fecha_de_aprobacion = hoy
        self.estado = self.APROBADA
        self.save()

    def desaprobar(self):
        if not self.aprobada():
            raise ValidationError('No se puede desaprobar una Venta cancelada o pendiente.')
        self.estado = self.PENDIENTE
        self.save()

    def cancelar(self, motivo=''):
        if self.aprobada():
            raise ValidationError('No se puede cancelar una Venta aprobada.')
        self.estado = self.CANCELADA
        self.fecha_de_cancelacion = localtime(now()).today()
        self.motivo_de_cancelacion = motivo
        self.save()

    def nombre_de_comprador(self):
        return self.prospecto.obtener_nombre()


class MotivoDeFinalizacion(models.Model):
    descripcion = models.CharField(max_length=64, default='')

    class Meta:
        verbose_name_plural = 'Motivos de finalizacion'

    @classmethod
    def para(cls, descripcion):
        motivo = cls.objects.get(descripcion=descripcion)
        return motivo

    def obtener_descripcion(self):
        return self.descripcion

    def es_incontactable(self):
        return self.descripcion == 'Datos Err\xf3neos' or self.descripcion == 'Incontactable'

    def __str__(self):
        return self.descripcion


class Finalizacion(models.Model):
    datetime = models.DateTimeField(auto_now_add=True)
    prospecto = models.OneToOneField('Prospecto', related_name='finalizacion')
    vendedor = models.ForeignKey('vendedores.Vendedor', related_name='finalizaciones')
    motivo = models.ForeignKey('MotivoDeFinalizacion', blank=True, null=True, on_delete=models.SET_NULL)
    otro_motivo = models.CharField(max_length=64, default='', blank=True, null=True)
    comentario = models.TextField(blank=True)

    class Meta:
        verbose_name_plural = 'finalizaciones'

    @classmethod
    def nueva_para(cls, prospecto, vendedor, comentario, motivo=None, otro_motivo=None, fecha=None):
        finalizacion = cls(prospecto=prospecto, vendedor=vendedor, motivo=motivo,
                           comentario=comentario, otro_motivo=otro_motivo)
        if fecha:
            finalizacion.datetime = fecha
        finalizacion.full_clean()
        finalizacion.save()
        return finalizacion

    @classmethod
    def de(cls, prospecto):
        return cls.objects.get(prospecto=prospecto)

    def obtener_motivo(self):
        return self.motivo

    def descripcion_de_motivo(self):
        if self.motivo is None:
            # Tecnicamente el motivo esta en el campo otro_motivo,
            # pero para este nivel de abstraccion elegimos solo responder "otro"
            return 'Otro'
        else:
            return self.motivo.descripcion

    def __str__(self):
        return "%s Finalizado por %s" % (self.prospecto.__str__(), self.vendedor)


class TelefonoExtra(models.Model):
    datetime = models.DateTimeField(auto_now_add=True)
    prospecto = models.ForeignKey('Prospecto', related_name='telefono_extra')
    vendedor = models.ForeignKey('vendedores.Vendedor', related_name='telefonos_extra')
    # Todo: reificarlo a un objeto telefono
    prefijo = models.CharField(max_length=5, blank=True, default='')
    telefono = models.CharField(max_length=64, blank=True, default='')
    telco = models.CharField(max_length=255, blank=True, default='')
    es_telefono_movil = models.NullBooleanField()
    esta_telefono_normalizado = models.BooleanField(default=False)
    telefono_bien_constituido = models.BooleanField(default=False)
    tiene_whatsapp = models.NullBooleanField()
    imagen_de_perfil_de_whatsapp = models.ImageField(upload_to='whatsapp', null=True, blank=True)
    esta_spam_list = models.NullBooleanField()
    # fin_telefono
    activo = models.BooleanField(default=True)

    def __str__(self):
        return "%s | %s" % (self.prospecto.id, self.telefono)

    @classmethod
    def nuevo(cls, prospecto, vendedor, prefijo, telefono):
        nuevo_telefono = TelefonoExtra(prospecto=prospecto, vendedor=vendedor, prefijo=prefijo, telefono=telefono)

        nuevo_telefono.full_clean()
        nuevo_telefono.save()

        return nuevo_telefono

    def obtener_telefono(self):
        return self.telefono

    def es_telefono_celular(self):
        return self.es_telefono_movil

    def save(self, *args, **kwargs):
        self.prefijo = Prospecto.dar_formato_a_prefijo(self.prefijo)
        super(TelefonoExtra, self).save(*args, **kwargs)

    class Meta:
        verbose_name = 'teléfono adicional'
        verbose_name_plural = 'teléfonos adicionales'


class EmailExtra(models.Model):
    datetime = models.DateTimeField(auto_now_add=True)
    prospecto = models.ForeignKey('Prospecto', related_name='email_extra')
    vendedor = models.ForeignKey('vendedores.Vendedor', related_name='emails_extra')
    email = models.EmailField()
    activo = models.BooleanField(default=True)

    def __str__(self):
        return "%s | %s" % (self.prospecto.id, self.email)

    @classmethod
    def nuevo(cls, prospecto, vendedor, email, activo):
        email_extra = EmailExtra(prospecto=prospecto, vendedor=vendedor, email=email, activo=activo)
        email_extra.full_clean()
        email_extra.save()
        return email_extra

    class Meta:
        verbose_name = 'email adicional'
        verbose_name_plural = 'emails adicionales'

    def obtener_email(self):
        return self.email


class CampoExtra(models.Model):
    datetime = models.DateTimeField(auto_now_add=True)
    prospecto = models.ForeignKey('Prospecto', related_name='campos_extra')
    nombre = models.CharField(max_length=64, default='')
    valor = models.TextField()

    objects = CampoExtraQuerySet.as_manager()

    def nombre_formateado(self):
        return self.nombre.replace("_", " ")

    @classmethod
    def nuevo(cls, prospecto, nombre, valor):
        nuevo_campo = cls(prospecto=prospecto, nombre=nombre, valor=valor)
        nuevo_campo.full_clean()
        nuevo_campo.save()

        return nuevo_campo

    def __str__(self):
        return "%s | %s" % (self.prospecto.id, self.nombre)

    def obtener_valor(self):
        return self.valor

    class Meta:
        verbose_name = 'campo extra'
        verbose_name_plural = 'campos extras'


class SubidaErronea(models.Model):
    archivo = models.FileField(upload_to='erroneas')
    fecha = models.DateTimeField(auto_now_add=True)
    ejecutor = models.ForeignKey('users.User', related_name='subidas_erroneas', null=True)
    fallidas = models.IntegerField()
    exitosas = models.IntegerField()
    origen = models.CharField(max_length=16, blank=True, null=True)
    campania = models.CharField(max_length=110, blank=True, null=True)
    responsable = models.CharField(max_length=64, blank=True, null=True)
    vendedor = models.CharField(max_length=64, blank=True, null=True)

    @classmethod
    def guardar_log(cls, log_erroneas, ejecutor, fallidas, exitosas, origen, campania, responsable, vendedor):
        subida = cls(archivo=log_erroneas, ejecutor=ejecutor, fallidas=fallidas, exitosas=exitosas,
                     origen=origen, campania=campania, responsable=responsable, vendedor=vendedor)
        subida.save()
        return subida

    class Meta:
        verbose_name = 'Subidas erronea'

class Rechazo(models.Model):
    prospecto = models.ForeignKey('Prospecto', related_name='rechazos')
    responsable = models.ForeignKey('vendedores.Vendedor', related_name='rechazos')
    datetime = models.DateTimeField(auto_now_add=True)

    objects = RechazoManager()

    def __str__(self):
        return "%s | Rechazado el %s por %s" % (
            self.prospecto.__str__(), self.datetime.strftime('%Y-%m-%d'), self.responsable)


class Tag(models.Model):
    vendedor = models.ForeignKey('vendedores.Vendedor', related_name='tags')
    nombre = models.CharField(max_length=32)
    prospectos = models.ManyToManyField(Prospecto, related_name='tags')

    objects = TagQuerySet.as_manager()

    class Meta:
        unique_together = ('vendedor', 'nombre')

    def __str__(self):
        return "%s | %s" % (self.vendedor, self.nombre)

    def as_json(self):
        return {
            'id': self.pk,
            'nombre': self.nombre,
            'vendedor': self.vendedor.as_json()
        }


class PedidoDeProspecto(models.Model):
    VIEJA_FORMA_DE_ENTREGA = 0
    NUEVA_FORMA_DE_ENTREGA = 1

    FORMAS_DE_ENTREGA = (
        (VIEJA_FORMA_DE_ENTREGA, 'Vieja'),
        (NUEVA_FORMA_DE_ENTREGA, 'Nueva'),
    )

    nombre = models.CharField(max_length=128, null=True, blank=True)
    supervisor = models.ForeignKey('vendedores.Vendedor', related_name='pedidos')
    credito = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    yapa = models.PositiveIntegerField(default=0)
    consumido = models.PositiveIntegerField(default=0)
    fecha = models.DateField('Fecha de comienzo', db_index=True)
    _calidades = models.ManyToManyField('campanias.TipoDeOrigen')
    categorias = models.ManyToManyField('campanias.CategoriaDeCampania')
    campanias = models.ManyToManyField('campanias.Campania', blank=True)
    asignar_a = models.CharField(max_length=1, choices=ModoSeleccionDeVendedorChoices.choices(), blank=True)
    vendedor = models.ForeignKey('vendedores.Vendedor', null=True, blank=True)
    equipo = models.ForeignKey('equipos.Equipo', null=True, blank=True)
    metodo_de_asignacion = models.PositiveIntegerField(choices=MetodosDeAsignacionChoices.choices(),
                                                       default=MetodosDeAsignacionChoices.UNIFORME,
                                                       null=True, blank=True)
    metodo_por_productividad = models.BooleanField(default=False)
    es_renovable = models.BooleanField(default=False)
    finalizado = models.BooleanField(default=False)
    factor_de_distribucion = models.PositiveIntegerField(default=10)
    restringir_por_datos_diarios = models.BooleanField(default=False)
    restringir_por_datos_nuevos = models.BooleanField(default=False)
    restringir_por_acceso = models.BooleanField(default=False)
    _excluye_campanias = models.BooleanField(default=True, choices=BOOL_CHOICES)
    forma_de_entrega = models.PositiveIntegerField(choices=FORMAS_DE_ENTREGA,
                                                   default=NUEVA_FORMA_DE_ENTREGA)
    _actualizacion_limite_diario_de_supervisor_habilitada = models.BooleanField(
        default=True, verbose_name="actualización límite diario de supervisor habilitada")

    objects = PedidoDeProspectoQuerySet.as_manager()

    def integraciones_con_crms(self):
        return self.clientes_crms.all()

    def no_tiene_integraciones_con_crms(self):
        return len(self.clientes_crms.all()) == 0

    def cambiar_a_inclusion_de_campanias(self):
        self._excluye_campanias = False
        self.save()

    def cambiar_calidades_por(self, calidades):
        self._calidades = calidades
        self.save()

    def registrar_consumo(self, consumo):
        self.consumido += consumo
        self.save()

    def asignar_prospecto(self, prospecto):
        self.asignar_prospecto_con_restricciones(
            prospecto=prospecto, lista_de_restricciones=self.filtro_de_vendedores())

    def asignar_prospecto_con_restricciones(self, prospecto, lista_de_restricciones):
        from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
        self._validar_por_datos_diarios(lista_de_restricciones)
        repartidor = RepartidorDeProspectos.nuevo()
        try:
            vendedor = self.seleccionar_vendedor_con_restricciones(lista_de_restricciones)
        except SeleccionVaciaError:
            self._asignar_responsable_con_restricciones(prospecto, lista_de_restricciones, repartidor)
        except NingunVendedorSatisfaceError:
            self._raise_pedido_no_aplica()
        else:
            repartidor.asignar_prospecto_a(vendedor=vendedor, prospecto=prospecto)

    def satisface_campania(self, campania):
        categoria = campania.categoria
        return self._tiene_credito_para_categoria(categoria) and self._incluye_origen(categoria) and \
               self._incluye_categoria(categoria) and self._satisface_campania(campania)

    def duracion(self):
        primero_de_mes = self.fecha.replace(day=1)
        fecha_de_cierre = primero_de_mes + relativedelta(months=1)
        return fecha_de_cierre - self.fecha

    def esta_finalizado(self):
        return self.finalizado

    def excluye_campanias(self):
        return self._excluye_campanias

    def calidades(self):
        return self._calidades.all()

    def concesionaria(self):
        return self.supervisor.obtener_concesionaria()

    def obtener_supervisor(self):
        return self.supervisor

    def obtener_credito(self):
        return self.credito

    def vendedores(self):
        return self.modo_de_seleccion_de_vendedor().vendedores(self)

    def incluye_vendedor(self, vendedor):
        return vendedor in self.vendedores()

    def porcentaje_entregado(self):
        if self.finalizado:
            return (float(self.consumido) / self.credito) * 100 if self.credito > 0 else 100

        hoy = datetime.datetime.now()
        cantidad_dias_en_mes = calendar.monthrange(hoy.year, hoy.month)[1]
        credito_por_dia = float(self.credito) / cantidad_dias_en_mes

        return (float(self.consumido) / (credito_por_dia * hoy.day)) * 100 if credito_por_dia > 0 else 100

    def __str__(self):
        if self.nombre:
            return self.nombre
        return "%s | %s" % (self.supervisor.concesionaria if self.supervisor.concesionaria else 'Sin Concesionaria',
                             self.supervisor)

    def saldo_de_yapa(self):
        return self.yapa + min(0, self.credito - self.consumido)

    def fraccion_de_credito_consumida(self):
        """
        :return: Fraccion de credito consumida teniendo en cuenta que lo consumido puede ser mayor que el credito.
                 Si el credito es 0, esta completamente consumido.
        """
        if self.credito > 0:
            return 1 - float(self.credito - min(self.credito, self.consumido)) / float(self.credito)
        return 1

    def primer_orden_de_asignacion(self):
        if self.credito > self.consumido:
            return self.fraccion_de_credito_consumida() / self.factor_de_distribucion
        return 1

    def es_para_unico_vendedor(self):
        return self.modo_de_seleccion_de_vendedor().es_a_vendedor()

    def es_asignacion_de_responsable(self):
        return self.asignar_a == ModoSeleccionDeVendedorChoices.VACIA

    def clean(self):
        if self.factor_de_distribucion < 1:
            raise ValidationError({'factor_de_distribucion': 'El factor de distribución debe ser mayor que 0'})
        return super(PedidoDeProspecto, self).clean()

    def modo_de_seleccion_de_vendedor(self, forzar_por_productividad=False):
        seleccion_class = ModoSeleccionDeVendedorChoices.asociado_a(self.asignar_a)
        return seleccion_class.nuevo_para_pedido(self, forzar_por_productividad)

    def metodo_de_priorizacion_de_vendedores(self, forzar_por_productividad=False):
        metodo = MetodosDeAsignacionChoices.asociado_a(self.metodo_de_asignacion)
        if forzar_por_productividad or self.metodo_por_productividad:
            metodo = metodo + MetodoDeSeleccionPorFactorAsistido.default()
        return metodo

    def seleccionar_vendedor(self):
        return self.seleccionar_vendedor_con_restricciones(self.filtro_de_vendedores())

    def seleccionar_vendedor_con_restricciones(self, lista_de_restricciones, forzar_por_productividad=False):
        modo_de_seleccion_de_vendedor = self.modo_de_seleccion_de_vendedor(forzar_por_productividad)
        vendedor = modo_de_seleccion_de_vendedor.seleccionar_para(lista_de_restricciones=lista_de_restricciones)
        return vendedor

    def detalles_de_asignacion(self):
        try:
            vendedor = self.seleccionar_vendedor_con_restricciones(self.filtro_de_vendedores())
        except SeleccionVaciaError:
            return self.detalles_de_supervisor()
        except NingunVendedorSatisfaceError:
            mensaje = 'El pedido momentaneamente no acepta prospectos'
        else:
            mensaje = 'Proximo vendedor %s' % vendedor.full_name()
        detalle = self.detalles_de_vendedores()
        informacion = {'ID':self.id,'nombre': self.nombre,'estado': mensaje, 'detalle': detalle}
        return informacion

    def detalles_de_vendedores(self):
        modo_de_seleccion_de_vendedor = self.modo_de_seleccion_de_vendedor(forzar_por_productividad=False)
        detalles = modo_de_seleccion_de_vendedor.detalles(lista_de_restricciones=self.filtro_de_vendedores())
        return detalles

    def detalles_de_supervisor(self):
        restricciones_de_responsable = self.filtro_de_vendedores().convertir_para_supervisor()
        detalles = restricciones_de_responsable.detalle_para(self.supervisor)
        return detalles

    def campanias_pedidas(self):
        if self.excluye_campanias():
            ids_campanias = self.categorias.values_list('campanias', flat=True)
            #TODO hay muchisimas campañas, hay que modificar y manejar por categorias y calidades
            return Campania.objects.filter(id__in=ids_campanias).exclude(pk__in=self.campanias.all())
        else:
            return self.campanias.all()

    def filtro_de_vendedores(self):
        # Todo: resolver la seleccion multiple desde un model a un objeto no persistente
        # Ver: django-select-multiple-field
        lista = ListaDeRestriccionesDeVendedores.vacia()
        if self.restringir_por_datos_diarios:
            lista.agregar(RestriccionLimiteDatosDiario())
        if self.restringir_por_datos_nuevos:
            lista.agregar(RestriccionLimiteDatosNuevos())
        if self.restringir_por_acceso:
            lista.agregar(RestriccionPorUltimoAccesso())
        return lista

    def usa_nueva_forma_de_entrega(self):
        return self.forma_de_entrega == self.NUEVA_FORMA_DE_ENTREGA

    @property
    def uniforme(self):
        return self.metodo_de_asignacion == MetodosDeAsignacionChoices.UNIFORME

    def debe_enviar_notificaciones(self):
        return len(self.medios_de_notificaciones()) > 0

    def medios_de_notificaciones(self):
        if hasattr(self, 'configuracion_de_notificacion'):
            return self.configuracion_de_notificacion.formas_de_envio()
        else:
            return []

    def marcas(self):
        # return [filtro.valor for filtro in self.filtros.filter(campo='marca', accion=FiltroDePedido.INCLUYE)]
        """
            Asume que se precargaron los filtros por cuestiones de performance
        """
        return [filtro.valor for filtro in self.filtros.all()
                if filtro.incluye() and filtro.refiere_a(campo='marca')]

    def actualizacion_limite_diario_de_supervisor_habilitada(self):
        return self._actualizacion_limite_diario_de_supervisor_habilitada

    def tiene_credito_disponible(self):
        return (self.credito - self.consumido + self.yapa) > 0

    def _tiene_credito_para_categoria(self, categoria):
        return categoria.valor + self.consumido < self.credito + self.yapa

    def _incluye_origen(self, categoria):
        return categoria.tipo_de_origen in self.calidades()

    def _incluye_categoria(self, categoria):
        lista_de_categorias = list(self.categorias.all())
        return len(lista_de_categorias) == 0 or categoria in lista_de_categorias

    def _satisface_campania(self, campania):
        pedido_sin_campanias = self.campanias.count() == 0
        existe_campania = self.campanias.filter(id=campania.id).exists()
        if self.excluye_campanias():
            return pedido_sin_campanias or not existe_campania
        else:
            return pedido_sin_campanias or existe_campania

    def _validar_por_datos_diarios(self, lista_de_restricciones):
        """
            Solo valida si el pedido tiene restriccion de datos diarios
        """
        restricciones_de_responsable = lista_de_restricciones.convertir_para_supervisor().convertir_seleccionado(
            lambda restriccion: restriccion.es_limite_diario())
        if restricciones_de_responsable.esta_restringido(self.supervisor):
            self._raise_pedido_no_aplica()

    def esta_restringido(self, vendedor):
        return self.filtro_de_vendedores().esta_restringido(vendedor)

    def _asignar_responsable_con_restricciones(self, prospecto, lista_de_restricciones, repartidor):
        restricciones_de_responsable = lista_de_restricciones.convertir_para_supervisor()
        if not restricciones_de_responsable.esta_restringido(self.supervisor):
            repartidor.asignar_responsable_a(prospecto=prospecto, supervisor=self.supervisor)
        else:
            self._raise_pedido_no_aplica()

    def _raise_pedido_no_aplica(self):
        from prospectos.models.entrega_de_datos.pedidos import PedidoNoAplicaError
        raise PedidoNoAplicaError()

    def _validar_restricciones_supervisor(self):
        if not (
                self._supervisor_cumple_restriccion_por_datos_diarios() and self._supervisor_cumple_restriccion_por_acceso()):
            self._raise_pedido_no_aplica()

    def _supervisor_cumple_restriccion_por_acceso(self):
        por_acceso = self.restringir_por_acceso
        config = ConfiguracionDeEntrega(vendedor=self.supervisor)
        limite = config.limite_de_acceso_en_horas()
        return self.supervisor.horas_desde_su_ultima_actividad() < limite or (not por_acceso)

    def _supervisor_cumple_restriccion_por_datos_diarios(self):
        hoy = now()
        hay_que_verificar = self.restringir_por_datos_diarios
        limite_sup = self.supervisor.limite_de_datos_diarios_al_supervisor_en_pedidos
        prospectos_sup_del_dia = self.supervisor.prospectos_a_cargo.filter(
            asignacion__fecha_de_asignacion_a_vendedor__day=hoy.day,
            asignacion__fecha_de_asignacion_a_vendedor__month=hoy.month,
            asignacion__fecha_de_asignacion_a_vendedor__year=hoy.year).count()
        return (not hay_que_verificar) or (limite_sup is None) or (hay_que_verificar and limite_sup >
                                                                   prospectos_sup_del_dia)



class ConfiguracionDeNotificacionDePedido(models.Model):
    pedido = models.OneToOneField(PedidoDeProspecto, related_name='configuracion_de_notificacion')
    por_mail = models.BooleanField(default=False)
    por_sms = models.BooleanField(default=False)
    por_whatsapp = models.BooleanField(default=False)

    def __str__(self):
        return self.pedido.nombre

    def formas_de_envio(self):
        formas_de_envio = self._formas_de_envio_para(self.por_mail, FormaDeEnvioEmail)
        formas_de_envio += self._formas_de_envio_para(self.por_sms, FormaDeEnvioSMS)
        formas_de_envio += self._formas_de_envio_para(self.por_whatsapp, FormaDeEnvioWhatsapp)
        return formas_de_envio

    def _formas_de_envio_para(self, condicion, forma_de_envio):
        if condicion:
            return [forma_de_envio]
        else:
            return []


class FiltroDePedido(models.Model):
    EXCLUYE = 'E'
    INCLUYE = 'I'
    PREFIJO = 'pre'
    CONTIENE = 'in'
    SUFIJO = 'fin'

    TIPOS_DE_SELECTORES = ((PREFIJO, 'Comienza con'), (CONTIENE, 'Contiene'), (SUFIJO, 'Termina con'))
    SELECTORES = {PREFIJO: 'istartswith', CONTIENE: 'icontains', SUFIJO: 'iendswith'}

    pedido = models.ForeignKey(PedidoDeProspecto, related_name='filtros')
    accion = models.CharField(max_length=1, choices=((INCLUYE, 'Incluir'), (EXCLUYE, 'Excluir')))
    campo = models.CharField(max_length=64)
    selector = models.CharField(max_length=3, choices=TIPOS_DE_SELECTORES)
    valor = models.CharField(max_length=64)

    objects = FiltroDePedidoQuerySet.as_manager()

    def incluye(self):
        return self.accion == self.INCLUYE

    def excluye(self):
        return self.accion == self.EXCLUYE

    def refiere_a(self, campo):
        return self.campo == campo

    def satisface(self, valor):
        aplica = self.aplica(valor)
        return (aplica and self.incluye()) or (not aplica and self.excluye())

    def satisface_alguno_de(self, valores):
        return any([self.satisface(valor) for valor in valores])


    def aplica(self, valor):
        if self.selector == self.PREFIJO:
            return valor.lower().startswith(self._valor_esperado())
        if self.selector == self.CONTIENE:
            return self._valor_esperado() in valor.lower()
        if self.selector == self.SUFIJO:
            return valor.lower().endswith(self._valor_esperado())

    def _valor_esperado(self):
        valor = self.valor
        if self.campo == 'prefijo':
            valor = re.sub('^0+', '', self.valor)
        return valor.lower()

    def __str__(self):
        return '%s %s %s...' % (self.__class__.__name__, self.campo, self.verbose_selector().lower())

    def verbose_selector(self):
        for tipo_de_selector in self.TIPOS_DE_SELECTORES:
            if tipo_de_selector[0] == self.selector:
                return tipo_de_selector[1]
        return ''

    @classmethod
    def _nuevo(cls, pedido, accion, campo, selector, valor):
        filtro = cls(pedido=pedido, accion=accion, campo=campo, selector=selector, valor=valor)
        filtro.full_clean()
        filtro.save()
        return filtro

    @classmethod
    def nuevo_para_exclusion(cls, pedido, campo, selector, valor):
        filtro = cls._nuevo(pedido=pedido, accion=cls.EXCLUYE, campo=campo, selector=selector, valor=valor)
        return filtro

    @classmethod
    def nuevo_para_inclusion(cls, pedido, campo, selector, valor):
        filtro = cls._nuevo(pedido=pedido, accion=cls.INCLUYE, campo=campo, selector=selector, valor=valor)
        return filtro


class Proveedor(models.Model):
    source_id = models.CharField(max_length=100, unique=True, verbose_name='ID')
    nombre = models.CharField(max_length=100, blank=True)

    objects = ProveedorQuerySet.as_manager()

    @classmethod
    def nuevo(cls, nombre, source_id):
        proveedor = cls(nombre=nombre, source_id=source_id)
        proveedor.full_clean()
        proveedor.save()
        return proveedor

    @classmethod
    def obtener_o_crear(cls, nombre, source_id):
        try:
            return cls.objects.get(source_id=source_id)
        except cls.DoesNotExist:
            return cls.nuevo(nombre=nombre, source_id=source_id)

    @classmethod
    def vacio(cls):
        proveedor, created = cls.objects.get_or_create(
            source_id=cls.source_id_de_vacio(), defaults={'nombre': Proveedor._nombre_de_proveedor_vacio()})
        return proveedor

    @classmethod
    def source_id_de_vacio(cls):
        return ''

    @classmethod
    def _nombre_de_proveedor_vacio(cls):
        return 'Proveedor Vacio'

    def obtener_nombre(self):
        return self.nombre

    def es_vacio(self):
        return self.source_id == self.__class__.source_id_de_vacio()

    class Meta:
        verbose_name_plural = 'proveedores'

    def __str__(self):
        return "%s | %s" % (self.source_id, self.nombre)


def anio_actual():
    return timezone.now().year


def mes_actual():
    return timezone.now().month


class Compra(models.Model):
    proveedor = models.ForeignKey(Proveedor, related_name='compras')
    mes = models.PositiveSmallIntegerField(default=mes_actual, choices=list(MONTHS.items()))
    anio = models.PositiveIntegerField(default=anio_actual, validators=[MinValueValidator(1900)], verbose_name='año')
    monto = models.PositiveIntegerField(validators=[MinValueValidator(1)])

    objects = CompraManager()

    class Meta:
        unique_together = (('proveedor', 'mes', 'anio'),)


class CargaFallidaDeJotform(models.Model):
    datos = models.TextField()
    fecha = models.DateTimeField(auto_now_add=True)
    error = models.CharField(max_length=256)
    exportado = models.BooleanField(default=False)

    @classmethod
    def registrar(cls, datos_de_carga, error):
        datos = json.dumps(datos_de_carga)
        carga = cls(datos=datos, error=error)
        carga.save()
        return carga

    def obtener_datos_de_carga(self):
        try:
            return json.loads(self.datos)
        except ValueError:
            return {}

    def __repr__(self):
        return self.error


from django.db.models.signals import post_delete, post_save
from django.dispatch import receiver


@receiver(post_delete, sender=SubidaErronea)
def my_handler(sender, **kwargs):
    kwargs['instance'].archivo.delete(save=False)


@receiver(post_save, sender=Finalizacion)
def al_finalizar(sender, **kwargs):
    finalizacion = kwargs['instance']
    finalizacion.prospecto.estado = Prospecto.FINALIZADO
    finalizacion.prospecto.save()
    from prospectos import tasks
    transaction.on_commit(lambda: tasks.mensaje_a_incontactable_por_finalizacion.delay(finalizacion_pk=finalizacion.pk))


@receiver(post_delete, sender=Finalizacion)
def al_borrar_finalizacion(sender, **kwargs):
    finalizacion = kwargs['instance']
    finalizacion.prospecto.estado = Prospecto.EN_PROCESO
    finalizacion.prospecto.save()


# Cualquier accion sobre el prospecto que genere un comentario
# marca al prospecto como en proceso (llamada, tel o mail extra, etc...)
@receiver(post_save, sender=Comentario)
def al_comentar(sender, **kwargs):
    comentario = kwargs['instance']
    # Solo cambio el estado a "En Proceso" si estaba nuevo.
    # Si esta Vendido, Finalizado o En proceso, no hace falta cambiarlo.
    if comentario.prospecto.estado == Prospecto.NUEVO:
        comentario.prospecto.estado = Prospecto.EN_PROCESO
        comentario.prospecto.save()


class LogDeErrorNormalizador(LogDeError):
    objects = LogDeErrorNormalizadorManager()

    class Meta:
        proxy = True
        verbose_name = 'Error del normalizador'
        verbose_name_plural = 'Errores del normalizador'

    @classmethod
    def codigo_de_log(cls):
        return cls.CODIGO_LOG_DE_ERROR_NORMALIZADOR


class LogDeErrorChequeadorDeWhatsapp(LogDeError):
    objects = LogDeErrorChequeadorDeWhatsappManager()

    class Meta:
        proxy = True
        verbose_name = 'Error del verificador de Whatsapp'
        verbose_name_plural = 'Errores del verificador de Whatsapp'

    @classmethod
    def codigo_de_log(cls):
        return cls.CODIGO_LOG_DE_ERROR_CHEQUEADOR_DE_WHATSAPP


class LogDeErrorDeInformacionDeRedesSociales(LogDeError):
    objects = LogDeErrorDeInformacionDeRedesSocialesManager()

    class Meta:
        proxy = True
        verbose_name = 'Error del servicio de Redes Sociales'
        verbose_name_plural = 'Errores del servicio de Redes Sociales'

    @classmethod
    def codigo_de_log(cls):
        return cls.CODIGO_LOG_DE_ERROR_INFORMACION_DE_REDES_SOCIALES


class LogDeErrorDeCRM(LogDeError):
    objects = LogDeErrorDeCRMManager()

    class Meta:
        proxy = True
        verbose_name = 'Error de servicio de CRM'
        verbose_name_plural = 'Errores de servicios de CRM'

    @classmethod
    def codigo_de_log(cls):
        return cls.CODIGO_LOG_DE_ERROR_CRM


ESTADOS_DE_REDES = dict(E='Erróneo', V='Válido', S='Sin Validar')
OPCIONES_DE_REDES = (
    ('E', ESTADOS_DE_REDES['E']),
    ('V', ESTADOS_DE_REDES['V']),
    ('S', ESTADOS_DE_REDES['S']),)


class LogDeExportacionDeProspecto(models.Model):
    cantidad = models.PositiveIntegerField(default=0)
    fecha = models.DateField(auto_now_add=True)
    user = models.ForeignKey('users.User', related_name='logs_de_exportacion')

    class Meta:
        verbose_name = 'Log de exportación de Prospecto'
        verbose_name_plural = 'Logs de exportación de Prospectos'
        unique_together = ('user', 'fecha')

    @classmethod
    def registrar(cls, user, fecha, cantidad):
        log, creado = LogDeExportacionDeProspecto.objects.get_or_create(fecha=fecha, user=user)
        log.actualizar_cantidad_de_prospectos_exportados(prospectos_exportados=cantidad)
        return log

    def actualizar_cantidad_de_prospectos_exportados(self, prospectos_exportados):
        self.cantidad += prospectos_exportados
        self.full_clean()
        self.save()


class InformacionDeRedesSociales(models.Model):
    TWITTER = 'Twitter'
    FACEBOOK = 'Facebook'
    CELULAR = 'Celular'
    TELEFONO = 'Telefono'
    NOMBRE_COMPLETO = 'Nombre completo'
    DIRECCION = 'Direccion'
    EMAIL = 'Email'
    IDENTIFICACION = 'Identificación'
    VEHICULOS = 'Vehiculos'
    FOTO = 'Foto'

    datetime = models.DateTimeField(auto_now_add=True)
    prospecto = models.ForeignKey('Prospecto', related_name='informacion_de_redes_sociales')
    tipo = models.CharField(max_length=64)
    valor = models.TextField(max_length=1024)
    estado = models.CharField(max_length=1, choices=OPCIONES_DE_REDES, default='S')
    comentario = models.CharField(max_length=256, default='', blank=True)

    class Meta:
        verbose_name = 'Información De Redes Sociales'
        verbose_name_plural = 'Información De Redes Sociales'

    @classmethod
    def nuevo(cls, prospecto, tipo, valor):
        informacion = cls(prospecto=prospecto, tipo=tipo, valor=valor)
        informacion.save()
        return informacion

    def valor_a_mostrar(self):
        mostrador = MostradorDeInformacionDeRedesSociales.para_informacion(self)
        return mostrador.mostrar(self)

    def fue_validada(self):
        return self.estado == 'V'

    def es_telefono_o_email(self):
        return self.es_telefono() or self.es_email()

    def es_telefono(self):
        return self.tipo.lower() in ['telefono', 'celular']

    def es_email(self):
        return self.tipo.lower() in ['email', 'mail', 'e-mail']


class ProspectoConTelefonoACompletar(models.Model):
    """
    ### Para ser usado por ColaDeProspectosACompletarInfoDeTelefonos
    """
    prospecto = models.OneToOneField('Prospecto', related_name='completar_informacion', primary_key=True)

    @classmethod
    def encolar(cls, prospecto):
        prospecto_a_completar = cls(prospecto=prospecto)
        prospecto_a_completar.save()
        return prospecto_a_completar


class MarcaDeTarjetaDeCredito(models.Model):
    """
    Esta clase modela la marca de una marca_de_tarjeta_de_credito de credito.

    La idea es que represente conceptos de la marca, como nombre, logo, etc. No deberia representar cosas como
    el numero de la tarjeta, para eso ver TarjetaDeProspecto
    """
    # Codigo, Nombre
    VISA = ('VISA', 'Visa')
    AMEX = ('AMEX', 'American Express')
    MASTER = ('MASTER', 'Mastercard')
    DINERS = ('DINERS', 'Diners Club')
    MAESTRO = ('MAESTRO', 'Maestro')
    DESCONOCIDA = ('DESCONOCIDA', 'Desconocida')
    MERCADOPAGO = ('MERCADOPAGO', 'Mercadopago')

    # Maxima longitud de un codigo
    CODE_MAX_LENGTH = 15

    OPCIONES = (VISA, AMEX, MASTER, DINERS, MAESTRO, MERCADOPAGO, DESCONOCIDA)

    _nombre = models.CharField(choices=OPCIONES, max_length=CODE_MAX_LENGTH, unique=True)
    _logo = models.ImageField("Logo", upload_to='prospectos', null=True, blank=True)

    objects = MarcaDeTarjetaDeCreditoQueryset.as_manager()

    def nombre(self):
        return self._nombre

    def logo(self):
        return self._logo

    def tarjetas(self):
        return self._tarjetas.all()

    @classmethod
    def nombre_de_marca_maestro(cls):
        return cls.MAESTRO[0]

    @classmethod
    def nombre_de_marca_visa(cls):
        return cls.VISA[0]

    @classmethod
    def nombre_de_marca_desconocida(cls):
        return cls.DESCONOCIDA[0]

    @classmethod
    def nueva(cls, nombre, logo=None):
        marca = cls(_nombre=nombre, _logo=logo)
        marca.full_clean()
        marca.save()
        return marca

    def __str__(self):
        return self.nombre()

    class Meta:
        # Al ser un ManyToMany no se puede: unique_together = ('_habilitada', '_cliente', '_supervisores')
        verbose_name = 'Marca de Tarjeta de Credito'
        verbose_name_plural = 'Marcas de Tarjetas de Credito'


class TarjetaDeProspecto(models.Model):
    """
    Esta clase modela la tenencia de una marca_de_tarjeta_de_credito de credito por parte de un prospecto.

    Nota: por ahora solo tiene la marca de la marca_de_tarjeta_de_credito y qué prospecto la tiene.
    En un futuro puede que querramos que tenga mas datos: numero, vencimiento, etc.
    El modelo está armado para que un prospecto pueda tener mas de una marca_de_tarjeta_de_credito.
    """

    _prospecto = models.ForeignKey('Prospecto', related_name='_tarjetas_de_credito')
    _marca = models.ForeignKey('MarcaDeTarjetaDeCredito', related_name='_tarjetas')
    _esta_habilitada = models.BooleanField(default=True)

    objects = TarjetaDeProspectoQueryset.as_manager()

    def marca(self):
        return self._marca

    def esta_habilitada(self):
        return self._esta_habilitada

    def prospecto(self):
        return self._prospecto

    @classmethod
    def nueva(cls, prospecto, marca):
        tarjeta = cls(_prospecto=prospecto, _marca=marca)
        tarjeta.full_clean()
        tarjeta.save()
        return tarjeta
