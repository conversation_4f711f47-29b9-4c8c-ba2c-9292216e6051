from django.db import models

class CirculacionDeProspecto(models.Model):

    _cedente = models.ForeignKey('vendedores.Vendedor', related_name='_cesiones_de_prospectos_circulados',
                                 on_delete=models.CASCADE)
    _destinatario = models.ForeignKey('vendedores.Vendedor', related_name='_recepciones_de_prospectos_circulados',
                                      on_delete=models.CASCADE)
    _prospecto = models.ForeignKey('prospectos.Prospecto', related_name='_circulaciones',
                                   on_delete=models.CASCADE)
    _fecha = models.DateTimeField(auto_now_add=True, db_index=True)

    @classmethod
    def nuevo(cls, cedente, destinatario, prospecto):
        return cls.objects.create(_cedente=cedente, _destinatario=destinatario, _prospecto=prospecto)

    def tiene_como_cedente_a(self, cedente):
        return self._cedente == cedente

    def tiene_como_destinatario_a(self, destinatario):
        return self._destinatario == destinatario

    def es_una_circulacion_de(self, prospecto):
        return self._prospecto == prospecto