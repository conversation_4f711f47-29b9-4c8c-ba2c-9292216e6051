from django.utils import timezone
from datetime import timedelta

from core.models import Sistema
from prospectos.models import Prospecto, CirculacionDeProspecto
from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
from prospectos.models.gestor.circulacion_de_prospectos.estrategia_aleatoria_de_priorizacion_de_prospectos import \
    EstrategiaAleatoriaDePriorizacionDeProspectos
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos


class CirculadorDeProspectos(object):

    """
    Comentarios en _es_candidato_para
    """

    def __init__(self, estrategia_de_priorizacion_de_prospectos, minutos_maximos_de_inactividad,
                 antiguedad_maxima_de_prospectos_en_dias):
        self._estrategia_de_priorizacion_de_prospectos = estrategia_de_priorizacion_de_prospectos
        self._minutos_maximos_de_inactividad = minutos_maximos_de_inactividad
        self._antiguedad_maxima_de_prospectos_en_dias = antiguedad_maxima_de_prospectos_en_dias

    @classmethod
    def nuevo(cls, estrategia_de_priorizacion_de_prospectos,
              antiguedad_maxima_de_prospectos_en_dias=365):
        minutos_maximos_de_inactividad = Sistema.instance().tiempo_maximo_de_inactividad_actividad_hace_minutos
        return cls(estrategia_de_priorizacion_de_prospectos, minutos_maximos_de_inactividad,
                   antiguedad_maxima_de_prospectos_en_dias)

    @classmethod
    def nuevo_con_estrategia_aleatoria_de_priorizacion_de_prospectos(cls, antiguedad_maxima_de_prospectos_en_dias=365):
        estrategia_de_priorizacion_de_prospectos = EstrategiaAleatoriaDePriorizacionDeProspectos.nuevo()
        return cls.nuevo(
            estrategia_de_priorizacion_de_prospectos=estrategia_de_priorizacion_de_prospectos,
            antiguedad_maxima_de_prospectos_en_dias=antiguedad_maxima_de_prospectos_en_dias)

    def circular(self):
        circulaciones = []
        prospectos = self._prospectos_a_circular()
        ids_de_vendedores_excluidos = list(prospectos.vendedores())
        repartidor = RepartidorDeProspectos()

        def circular_a(prospecto_a_circular):
            vendedores_candidatos = self._vendedores_candidatos_para(
                prospecto_a_circular=prospecto_a_circular, ids_de_vendedores_excluidos=ids_de_vendedores_excluidos)
            if len(vendedores_candidatos) > 0:
                cedente = prospecto_a_circular.obtener_vendedor()
                destinatario = vendedores_candidatos[0]
                repartidor.asignar_prospecto_a(vendedor=destinatario, prospecto=prospecto_a_circular)
                circulacion = CirculacionDeProspecto.nuevo(cedente=cedente, destinatario=destinatario, prospecto=prospecto_a_circular)
                circulaciones.append(circulacion)

        self._estrategia_de_priorizacion_de_prospectos.hacer_priorizando(prospectos_a_priorizar=prospectos,
                                                                         bloque=circular_a)

        return circulaciones

    '''Este método es solo para testear cosas'''

    def vendedores_candidatos_por_prospecto(self):
        prospectos = self._prospectos_a_circular()
        ids_de_vendedores_excluidos = list(prospectos.vendedores())
        vendedores_candidatos_por_prospecto = {}
        for prospecto_a_circular in prospectos:
            vendedores_candidatos = self._vendedores_candidatos_para(
                prospecto_a_circular=prospecto_a_circular, ids_de_vendedores_excluidos=ids_de_vendedores_excluidos)
            print(vendedores_candidatos)
            vendedores_candidatos_por_prospecto[prospecto_a_circular] = vendedores_candidatos
        return vendedores_candidatos_por_prospecto

    '''Fin del método para testeo de circulador'''

    def el_vendedor_no_supero_el_limite_diario(self, vendedor):
        administrador_de_pedidos = AdministradorDePedidos.nuevo()
        return administrador_de_pedidos.el_vendedor_no_supero_el_limite_diario(vendedor=vendedor)

    def _prospectos_a_circular(self):
        fecha_desde = self._fecha_de_antiguedad_maxima_de_prospecto()
        prospectos = Prospecto.objects.all().asignado_a_algun_vendedor_activo().con_algun_pedido().con_permiso_para_circular().entre_fechas(
            fecha_desde=fecha_desde).con_utlimo_acceso_al_listado_de_prospectos().con_fecha_ultimo_acceso_al_listado_de_prospectos_menor_a_fecha_de_asignacion().nuevos().puede_circular()

        return prospectos

    def _fecha_de_antiguedad_maxima_de_prospecto(self):
        return timezone.now() - timedelta(days=self._antiguedad_maxima_de_prospectos_en_dias)

    def _vendedores_candidatos_para(self, prospecto_a_circular, ids_de_vendedores_excluidos):
        vendedores_candidatos = []
        pedido = prospecto_a_circular.pedido
        if self._puede_circular_los_prospecto_de(pedido):
            vendedores_candidatos = self._filtrar_vendedores_candidatos(ids_de_vendedores_excluidos,
                                                                        prospecto_a_circular, pedido)
            vendedores_candidatos = sorted(
                vendedores_candidatos,
                key=lambda vendedor: (
                        vendedor.obtener_ultima_actividad()
                ),
                reverse=True
            )
            return vendedores_candidatos
        return vendedores_candidatos

    def _filtrar_vendedores_candidatos(self, ids_de_vendedores_excluidos, prospecto_a_circular, pedido):
        vendedores = pedido.vendedores()
        return [vendedor_candidato for vendedor_candidato in vendedores if
                self._es_candidato_para(prospecto_a_circular, vendedor_candidato, ids_de_vendedores_excluidos)]

    def _puede_circular_los_prospecto_de(self, pedido):
        return self._el_pedido_esta_activo(
            pedido) and pedido.no_tiene_integraciones_con_crms()

    def _el_pedido_esta_activo(self, pedido):
        return not pedido.esta_finalizado()

    def _es_candidato_para(self, prospecto_a_circular, vendedor_candidato, ids_de_vendedores_excluidos):
        """
        No validamos que el supervisor y/o la concesionaria tengan permiso para circular, solo validamos el vendedor.
        No validamos el supervisor/concesionaria ya que si el supervisor/concesionaria no tiene permisos no va a haber
        ningún prospecto a circular entre los vendedores de ese pedido, por lo tanto aunque haya vendedores candidatos
        no va a haber prospectos. El escenario donde esto puede ser necesario es si el prospecto puede tomar vendedores
        de otro pedido.
        """
        return (self._no_es_vendedor_excluido(ids_de_vendedores_excluidos, vendedor_candidato) and
                self._el_vendedor_puede_recibir_al_prospecto(prospecto=prospecto_a_circular, vendedor_candidato=vendedor_candidato) and
                self._el_vendedor_cumple_las_condiciones_generales_para_ser_candidato(vendedor_candidato=vendedor_candidato)
                )

    def _el_vendedor_cumple_las_condiciones_generales_para_ser_candidato(self, vendedor_candidato):
        return (vendedor_candidato.habilitado and
                vendedor_candidato.cantidad_de_prospectos_nuevos() == 0 and
                vendedor_candidato.limite_de_datos_nuevos() > 0 and
                self.el_vendedor_no_supero_el_limite_diario(vendedor=vendedor_candidato) and
                vendedor_candidato.su_ultima_actividad_en_minutos_es_menor_a(
                    minutos_de_inactividad=self._minutos_maximos_de_inactividad) and
                vendedor_candidato.configuracion_de_servicios().habilitado_para_circular_prospectos() and
                not vendedor_candidato.sin_ultima_actividad_ni_acceso_listado()
                )

    def _el_vendedor_puede_recibir_al_prospecto(self, prospecto, vendedor_candidato):
        return (self._el_prospecto_esta_dentro_del_limite_para_ser_atendido(prospecto, vendedor_candidato) and
                self._es_el_prospecto_circulable(prospecto)
                )

    def _es_el_prospecto_circulable(self, prospecto):
        return prospecto.obtener_vendedor().puede_circular_prospectos_si_su_ultima_actividad_es_mayor_a_minutos_de_inactividad_maximos_segun_supervisor()

    def _no_es_vendedor_excluido(self, ids_de_vendedores_excluidos, vendedor_candidato):
        return vendedor_candidato.id not in ids_de_vendedores_excluidos

    def _el_prospecto_esta_dentro_del_limite_para_ser_atendido(self, prospecto_a_circular, vendedor_candidato):
        limite = vendedor_candidato.fecha_limite_para_atender_prospectos_en_tiempo()
        return limite < prospecto_a_circular.obtener_fecha_de_creacion()
