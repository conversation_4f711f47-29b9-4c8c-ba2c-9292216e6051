# coding=utf-8

from dateutil.relativedelta import relativedelta
from django.db.models import Count, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Value
from django.utils.timezone import now, timedelta

from core.models import Sistema
from prospectos.models.entrega_de_datos.soporte import FuncionNormalizadoraDeTiempoDeRespuesta, Regla


class NingunVendedorSatisfaceError(Exception):
    pass


class SeleccionVaciaError(Exception):
    pass


class ConfiguracionDeEntrega(object):
    def __init__(self, vendedor):
        super(ConfiguracionDeEntrega, self).__init__()
        self._vendedor = vendedor
        self._sistema = None

    @classmethod
    def explicacion_calculo_de_limite_de_datos_diarios(cls):
        return "Si el vendedor tiene cero(0) no se le envian prospectos.\n" \
               "Si no tiene nada, se usa el limite definido en el supervisor.\n" \
               "Si el supervisor no tiene definido un limite, se usa el del sistema."

    @classmethod
    def nuevo_para(cls, vendedor):
        return cls(vendedor)

    def vendedor(self):
        return self._vendedor

    def limite_de_acceso_en_horas(self):
        return self._get_sistema().limite_de_horas_sin_actividad_en_pedidos

    def limite_de_datos_nuevos(self):
        if self._vendedor.limite_de_datos_nuevos_en_pedidos is not None:
            return self._vendedor.limite_de_datos_nuevos_en_pedidos
        elif self._vendedor.supervisor and self._vendedor.supervisor.limite_de_datos_nuevos_en_pedidos is not None:
            return self._vendedor.supervisor.limite_de_datos_nuevos_en_pedidos
        else:
            return self._get_sistema().limite_de_datos_nuevos_en_pedidos

    def limite_de_datos_diarios(self):
        return self.calcular_limite_de_datos_diarios()['valor']

    def limite_de_datos_diarios_detallado(self):
        """"
        Se crea este metodo ya que en los templates utilizamos varias partes del diccionario que devuelve calcular limite
        de datos diarios
        """
        return self.calcular_limite_de_datos_diarios()

    def calcular_limite_de_datos_diarios(self):
        """Devuelvo el resultado de calcular el limite diario del vendedor para recibir propspectos de un pedido.
        El resultado consiste de un valor, indicando la cantidad de prospectos maxima que puede recibir en un dia
        y una explicacion que indica el motivo por el cual el valor es ese."""
        if self._vendedor.limite_de_datos_diarios_en_pedidos is not None:
            return {
                'valor': self._vendedor.limite_de_datos_diarios_en_pedidos,
                # Solía decir "Límite definido en el Sistema". No removí la funcionalidad porque puede ser útil.
                'explicacion': ""
            }
        elif self._vendedor.supervisor and self._vendedor.supervisor.limite_de_datos_diarios_en_pedidos is not None:
            return {
                'valor': self._vendedor.supervisor.limite_de_datos_diarios_en_pedidos,
                'explicacion': ""
            }
        else:
            return {
                'valor': self._get_sistema().limite_de_datos_diarios_en_pedidos,
                'explicacion': ""
            }

    def limite_de_datos_diarios_para_supervisor(self):
        if self._vendedor.limite_de_datos_diarios_al_supervisor_en_pedidos is not None:
            return self._vendedor.limite_de_datos_diarios_al_supervisor_en_pedidos
        else:
            return self._get_sistema().limite_de_datos_diarios_en_pedidos

    def _get_sistema(self):
        if not self._sistema:
            self._sistema = Sistema.instance()
        return self._sistema


class ModoDeSeleccionDeVendedor(object):
    def seleccionar_para(self, lista_de_restricciones):
        raise NotImplementedError('Subclass responsibility')

    def evaluar_asignacion(self, seleccion_directa, prospectos, accion):
        raise NotImplementedError('Subclass responsibility')

    def detalles(self, lista_de_restricciones):
        raise NotImplementedError('Subclass responsibility')

    @classmethod
    def descripcion(cls):
        raise NotImplementedError('Subclass responsibility')

    def es_a_vendedor(self):
        return self.__class__().es_a_vendedor()

    def es_a_equipo(self):
        return self.__class__().es_a_equipo()

    def es_a_nadie(self):
        return self.__class__().es_a_nadie()

    def es_a_todos(self):
        return self.__class__().es_a_todos()

    @classmethod
    def es_a_nadie(cls):
        return False

    @classmethod
    def es_a_equipo(cls):
        return False

    @classmethod
    def es_a_todos(cls):
        return False

    @classmethod
    def es_a_vendedor(cls):
        return False

    @classmethod
    def nuevo_para_pedido(cls, pedido, forzar_por_productividad=False):
        raise NotImplementedError('Subclass responsibility')


class SeleccionDeUnicoVendedor(ModoDeSeleccionDeVendedor):
    def __init__(self, vendedor):
        super(SeleccionDeUnicoVendedor, self).__init__()
        self._vendedor = vendedor

    def seleccionar_para(self, lista_de_restricciones):
        if self._puede_ser_seleccionado(self._vendedor, lista_de_restricciones):
            return self._vendedor
        raise NingunVendedorSatisfaceError()

    def evaluar_asignacion(self, seleccion_directa, prospectos, accion):
        return seleccion_directa.evaluar_modo_directo(prospectos, accion)

    def _puede_ser_seleccionado(self, vendedor, lista_de_restricciones):
        return vendedor.habilitado and vendedor.obtener_factor_de_asignacion() > 0 and\
               not lista_de_restricciones.esta_restringido(vendedor)

    def detalles(self, lista_de_restricciones):
        detalles = {'vendedor': self._vendedor.full_name(), 'restricciones': ''}
        if not self._vendedor.habilitado:
            detalles['esta_restringido'] = True
            detalles['detalle'] = 'no habilitado'
        elif not self._vendedor.obtener_factor_de_asignacion() > 0:
            detalles['esta_restringido'] = True
            detalles['detalle'] = 'factor de asignacion cero'
        else:
            detalles['esta_restringido'] = lista_de_restricciones.esta_restringido(self._vendedor)
            detalles['detalle'] = lista_de_restricciones.detalle_para(self._vendedor)
        return detalles

    @classmethod
    def descripcion(cls):
        return 'Vendedor'

    @classmethod
    def es_a_vendedor(cls):
        return True

    @classmethod
    def nuevo_para_pedido(cls, pedido, forzar_por_productividad=False):
        return cls.nuevo(pedido.vendedor)

    @classmethod
    def nuevo(cls, vendedor):
        return cls(vendedor=vendedor)

    def vendedores(self, pedido):
        return [pedido.vendedor]


class SeleccionVacia(ModoDeSeleccionDeVendedor):
    def seleccionar_para(self, lista_de_restricciones):
        raise SeleccionVaciaError()

    def evaluar_asignacion(self, seleccion_directa, prospectos, accion):
        seleccion_directa.evaluar_modo_directo(prospectos, accion)

    @classmethod
    def descripcion(cls):
        return 'Nadie'

    def es_a_nadie(self):
        return True

    def vendedores(self, pedido):
        return []

    @classmethod
    def nuevo_para_pedido(cls, pedido, forzar_por_productividad=False):
        return cls()

    def detalles(self, lista_de_restricciones):
        return {'error': 'solo para pedidos para vendedores'}


class ModoDeSeleccionPriorizada(ModoDeSeleccionDeVendedor):
    def __init__(self, vendedores, metodo_de_priorizacion_de_vendedores):
        super(ModoDeSeleccionPriorizada, self).__init__()
        self._vendedores = vendedores
        self._metodo_de_priorizacion_de_vendedores = metodo_de_priorizacion_de_vendedores

    def seleccionar_para(self, lista_de_restricciones):
        preseleccionados = self._vendedores_filtrados_para(lista_de_restricciones)
        if not preseleccionados.exists():
            raise NingunVendedorSatisfaceError()
        vendedor = self._metodo_de_priorizacion_de_vendedores.seleccionar_para(preseleccionados)
        return vendedor

    def evaluar_asignacion(self, seleccion_directa, prospectos, accion):
        return seleccion_directa.evaluar_modo_seleccion(prospectos, accion)

    def detalles(self, lista_de_restricciones):
        preseleccionados = self._vendedores.filter(factor_de_asignacion__gt=0)
        if not preseleccionados.exists():
            return {'mensaje': 'todos los vendedores tienen factor de asignación cero'}
        else:
            detalles = lista_de_restricciones.detalles_de_vendedores(preseleccionados)
            return detalles

    def _vendedores_filtrados_para(self, lista_de_restricciones):
        preseleccionados = self._vendedores.filter(factor_de_asignacion__gt=0)
        filtrados = lista_de_restricciones.filtrar(preseleccionados)
        return filtrados

    @classmethod
    def nuevo_para_pedido(cls, pedido, forzar_por_productividad=False):
        vendedores = cls._vendedores_desde_pedido(pedido)
        metodo = pedido.metodo_de_priorizacion_de_vendedores(forzar_por_productividad)
        return cls(vendedores=vendedores, metodo_de_priorizacion_de_vendedores=metodo)

    @classmethod
    def _vendedores_desde_pedido(cls, pedido):
        raise NotImplementedError('Subclass responsibility')


class SeleccionDeTodos(ModoDeSeleccionPriorizada):
    @classmethod
    def es_a_todos(cls):
        return True

    def vendedores(self, pedido):
        return list(pedido.supervisor.vendedores.all())

    @classmethod
    def descripcion(cls):
        return 'Todos'

    @classmethod
    def seleccionar_vendedores(cls, supervisor, lista_de_restricciones):
        vendedores = cls._vendedores_de(supervisor)
        return lista_de_restricciones.filtrar(vendedores)

    @classmethod
    def _vendedores_desde_pedido(cls, pedido):
        return cls._vendedores_de(pedido.supervisor)

    @classmethod
    def _vendedores_de(cls, supervisor):
        return supervisor.vendedores.vendedores_activos()

    @classmethod
    def nuevo(cls, supervisor, metodo):
        vendedores = cls._vendedores_de(supervisor)
        return cls(vendedores=vendedores, metodo_de_priorizacion_de_vendedores=metodo)


class SeleccionDeEquipo(ModoDeSeleccionPriorizada):
    def vendedores(self, pedido):
        return list(pedido.equipo.integrantes.all())

    @classmethod
    def descripcion(cls):
        return 'Equipo'

    @classmethod
    def es_a_equipo(cls):
        return True

    @classmethod
    def _vendedores_desde_pedido(cls, pedido):
        return cls._vendedores_desde_equipo(pedido.equipo)

    @classmethod
    def _vendedores_desde_equipo(cls, equipo):
        return equipo.integrantes.vendedores_activos()

    @classmethod
    def nuevo(cls, equipo, metodo):
        vendedores = cls._vendedores_desde_equipo(equipo)
        return cls(vendedores=vendedores, metodo_de_priorizacion_de_vendedores=metodo)


class MetodoDeSeleccionDeVendedor(object):
    def seleccionar_para(self, vendedores_preseleccionados):
        raise NotImplementedError('Subclass responsibility')

    @classmethod
    def descripcion(cls):
        raise NotImplementedError('Subclass responsibility')

    def es_uniforme(self):
        return False

    def __add__(self, other):
        metodo = MetodoDeSeleccionPorFactorMultiple.nuevo_con(self, other)
        return metodo

    # MetodoDeSeleccionPorFactorMultiple compatibility
    def calculos_previos(self, vendedores):
        return {}

    # MetodoDeSeleccionPorFactorMultiple compatibility
    def optimizar_consulta(self, vendedores):
        return vendedores

    # MetodoDeSeleccionPorFactorMultiple compatibility
    def factor_de_distribucion_de_vendedor(self, vendedor, calculos_previos):
        raise NotImplementedError('Subclass responsibility')

    def factor_de_distribucion_de_vendedores(self, vendedores):
        consulta_vendedores = self.optimizar_consulta(vendedores)
        calculos_previos = self.calculos_previos(vendedores)
        distribuciones = {each.id: self.factor_de_distribucion_de_vendedor(each, calculos_previos)
                          for each in consulta_vendedores.all()}
        return distribuciones

    @classmethod
    def sistema(cls):
        return Sistema.instance()


class MetodoDeSeleccionUniforme(MetodoDeSeleccionDeVendedor):
    def seleccionar_entre(self, vendedores):
        vendedores_preseleccionados = vendedores.annotate(
            fecha_de_asignacion_a_vendedor=Max('prospectos__asignacion__fecha_de_asignacion_a_vendedor'))
        vendedores_preseleccionados = vendedores_preseleccionados.order_by('fecha_de_asignacion_a_vendedor')
        return vendedores_preseleccionados.first()

    def seleccionar_para(self, vendedores_preseleccionados):
        vendedores = vendedores_preseleccionados.annotate(
            fecha_de_asignacion_a_vendedor=Max('prospectos__asignacion__fecha_de_asignacion_a_vendedor'))
        vendedores = vendedores.order_by('fecha_de_asignacion_a_vendedor')
        return vendedores.first()

    @classmethod
    def descripcion(cls):
        return 'Uniforme'

    @classmethod
    def default(cls):
        return cls()

    def es_uniforme(self):
        return True

    # MetodoDeSeleccionPorFactorMultiple compatibility
    def factor_de_distribucion_de_vendedor(self, vendedor, calculos_previos):
        return 1.0


class MetodoDeSeleccionDeVendedorPorFactorDeDistribucion(MetodoDeSeleccionDeVendedor):
    def seleccionar_para(self, vendedores_preseleccionados):
        distribuciones = self.distribucion_de_vendedores(vendedores_preseleccionados)
        # Ordenar por relacion (creciente): cantidad de prospectos nuevos / factor_de_asignacion
        vendedor = self._buscar_vendedor(distribuciones)
        return vendedor

    def distribucion_de_vendedores(self, vendedores):
        factores_de_distribucion = self.factor_de_distribucion_de_vendedores(vendedores)
        vendedores_con_cantidad_asignada = self._calcular_cantidad_asignada(vendedores)
        distribuciones = []
        for vendedor in vendedores_con_cantidad_asignada.all():
            factor = factores_de_distribucion.get(vendedor.id)
            # Sumo uno los vendedores sin prospectos nuevos
            if factor > 0:
                ratio = float(vendedor.cantidad_asignada + 1.0) / factor
            else:
                ratio = 0
            distribuciones.append((vendedor, ratio, vendedor.fecha_de_asignacion_a_vendedor))
        return distribuciones

    def _buscar_vendedor(self, distribuciones):
        """
            Selecciona el vendedor con menor factor y menos tiempo de acceso
        """
        distribuciones_ordenadas = sorted(distribuciones, key=self._clave_sort_distribucion)
        for vendedor, valor, fecha_de_asignacion_a_vendedor in distribuciones_ordenadas:
            if valor > 0:
                return vendedor
        raise NingunVendedorSatisfaceError()

    def _clave_sort_distribucion(self, distribucion):
        factor = distribucion[1]
        fecha_de_asignacion_a_vendedor = distribucion[2]
        if not fecha_de_asignacion_a_vendedor:
            fecha_de_asignacion_a_vendedor = now() - relativedelta(years=2)

        return factor, fecha_de_asignacion_a_vendedor

    def _calcular_cantidad_asignada(self, vendedores):
        vendedores_con_cantidad_asignada = vendedores.annotate(
            cantidad_asignada=Count(Case(When(prospectos__estado='N', then=1))),
            fecha_de_asignacion_a_vendedor=Max('prospectos__asignacion__fecha_de_asignacion_a_vendedor'))
        return vendedores_con_cantidad_asignada


class MetodoDeSeleccionPorFactorManual(MetodoDeSeleccionDeVendedorPorFactorDeDistribucion):
    def factor_de_distribucion_de_vendedor(self, vendedor, calculos_previos):
        return self.__class__.sistema().politca_de_entrega().factor_de_asignacion_para(vendedor)

    @classmethod
    def descripcion(cls):
        return 'Manual'

    @classmethod
    def default(cls):
        return cls()


class MetodoDeSeleccionPorFactorAdministrador(MetodoDeSeleccionDeVendedorPorFactorDeDistribucion):
    def factor_de_distribucion_de_vendedor(self, vendedor, calculos_previos):
        return self.__class__.sistema().politca_de_entrega().factor_de_asignacion_administrador_para(vendedor)

    @classmethod
    def descripcion(cls):
        return 'Administrador'

    @classmethod
    def default(cls):
        return cls()


class MetodoDeSeleccionPorFactorAsistido(MetodoDeSeleccionDeVendedorPorFactorDeDistribucion):
    def __init__(self, constante, regla_venta_mensual, regla_venta_semanal,
                 regla_tiempo_de_respuesta_mensual,
                 regla_tiempo_de_respuesta_semanal):
        super(MetodoDeSeleccionPorFactorAsistido, self).__init__()
        self._constante = constante
        self._regla_venta_mensual = regla_venta_mensual
        self._regla_venta_semanal = regla_venta_semanal
        self._regla_tiempo_de_respuesta_mensual = regla_tiempo_de_respuesta_mensual
        self._regla_tiempo_de_respuesta_semanal = regla_tiempo_de_respuesta_semanal

    @classmethod
    def descripcion(cls):
        return 'Asistido'

    @classmethod
    def default(cls):
        sistema = cls.sistema()
        regla_ventas_mensuales = cls._regla_para(sistema.regla_ventas_mensuales_maximo,
                                                 sistema.regla_ventas_mensuales_minimo)
        regla_ventas_semanales = cls._regla_para(sistema.regla_ventas_semanales_maximo,
                                                 sistema.regla_ventas_semanales_minimo)

        regla_tiempo_de_respuesta_mensual = cls._regla_para(sistema.regla_tiempo_de_respuesta_mensual_maximo,
                                                            sistema.regla_tiempo_de_respuesta_mensual_minimo)
        regla_tiempo_de_respuesta_semanal = cls._regla_para(sistema.regla_tiempo_de_respuesta_semanal_maximo,
                                                            sistema.regla_tiempo_de_respuesta_semanal_minimo)
        metodo = cls(constante=sistema.constante_distribucion_asistida_en_pedido,
                     regla_venta_mensual=regla_ventas_mensuales,
                     regla_venta_semanal=regla_ventas_semanales,
                     regla_tiempo_de_respuesta_mensual=regla_tiempo_de_respuesta_mensual,
                     regla_tiempo_de_respuesta_semanal=regla_tiempo_de_respuesta_semanal)
        return metodo

    @classmethod
    def _regla_para(cls, maximo, minimo):
        valor_maximo = float(maximo) / 100
        valor_minimo = float(minimo) / 100
        regla = Regla.nuevo(valor_maximo, valor_minimo)
        return regla

    def calculos_previos(self, vendedores):
        calculos_previos = super(MetodoDeSeleccionPorFactorAsistido, self).calculos_previos(vendedores)
        calculos_previos['funcion_normalizadora'] = FuncionNormalizadoraDeTiempoDeRespuesta.nueva(vendedores.all())
        return calculos_previos

    def optimizar_consulta(self, vendedores):
        un_mes_atras = now() - timedelta(days=30)
        una_semana_atras = now() - timedelta(days=7)
        vendedores_con_ventas = vendedores.annotate(
            cantidad_mensual_de_ventas=self._consulta_cantidad_de_ventas_desde(un_mes_atras),
            cantidad_semanal_de_ventas=self._consulta_cantidad_de_ventas_desde(una_semana_atras))
        extremos = vendedores_con_ventas.aggregate(maximo_mensual=Max('cantidad_mensual_de_ventas'),
                                                   maximo_semanal=Max('cantidad_semanal_de_ventas'),
                                                   minimo_mensual=Min('cantidad_mensual_de_ventas'),
                                                   minimo_semanal=Min('cantidad_semanal_de_ventas')
                                                   )
        vendedores_con_maximos = vendedores_con_ventas.annotate(
            cantidad_maxima_mensual_de_ventas=Value(extremos['maximo_mensual'], IntegerField()),
            cantidad_maxima_semanal_de_ventas=Value(extremos['maximo_semanal'], IntegerField()),
            cantidad_minima_mensual_de_ventas=Value(extremos['minimo_mensual'], IntegerField()),
            cantidad_minima_semanal_de_ventas=Value(extremos['minimo_semanal'], IntegerField()))
        return vendedores_con_maximos

    def _consulta_cantidad_de_ventas_desde(self, fecha):
        suma_ventas = Sum(Case(When(ventas__fecha_de_realizacion__gte=fecha, then=1),
                               default=0,
                               output_field=IntegerField()))
        return suma_ventas

    def factor_de_distribucion_de_vendedor(self, vendedor, calculos_previos):
        funcion_normalizadora = calculos_previos['funcion_normalizadora']
        return self._distribucion_por_venta(vendedor) + self._distribucion_por_tiempo_de_respuesta(
            vendedor, funcion_normalizadora)

    def _distribucion_por_venta(self, vendedor):
        distribucion_mensual = self._funcion_distribucion_por_venta(vendedor.cantidad_mensual_de_ventas,
                                                                    vendedor.cantidad_maxima_mensual_de_ventas,
                                                                    vendedor.cantidad_minima_mensual_de_ventas,
                                                                    self._regla_venta_mensual)
        distribucion_semanal = self._funcion_distribucion_por_venta(vendedor.cantidad_semanal_de_ventas,
                                                                    vendedor.cantidad_maxima_semanal_de_ventas,
                                                                    vendedor.cantidad_minima_semanal_de_ventas,
                                                                    self._regla_venta_semanal)
        return distribucion_mensual + distribucion_semanal

    def _distribucion_por_tiempo_de_respuesta(self, vendedor, funcion_normalizadora):
        factor_mensual = funcion_normalizadora.factor_mensual_para(vendedor)
        distribucion_mensual = self._funcion_distribucion_por_tiempo_de_respuesta(
            factor_mensual,
            self._regla_tiempo_de_respuesta_mensual)
        factor_semanal = funcion_normalizadora.factor_semanal_para(vendedor)
        distribucion_semanal = self._funcion_distribucion_por_tiempo_de_respuesta(
            factor_semanal,
            self._regla_tiempo_de_respuesta_semanal)
        return distribucion_mensual + distribucion_semanal

    def _funcion_distribucion_por_venta(self, ventas_vendedor, ventas_max, ventas_min, regla):
        factor_relativo = float(ventas_vendedor) / ventas_max if ventas_max > 0 else 0
        if ventas_vendedor == ventas_max:
            factor_condicional = ventas_vendedor
        else:
            factor_condicional = ventas_vendedor - ventas_min
        valor = regla.evaluar(constante=self._constante, factor=factor_relativo,
                              factor_condicional=factor_condicional)
        return valor

    def _funcion_distribucion_por_tiempo_de_respuesta(self, factor_vendedor, regla):
        valor = regla.evaluar(constante=self._constante, factor=factor_vendedor,
                              factor_condicional=factor_vendedor)
        return valor


class MetodoDeSeleccionPorFactorMultiple(MetodoDeSeleccionDeVendedorPorFactorDeDistribucion):
    def __init__(self, metodos_de_priorizacion):
        self._metodos_de_priorizacion = metodos_de_priorizacion

    def __add__(self, other):
        if self != other:
            self._metodos_de_priorizacion.append(other)
        return self

    def calculos_previos(self, vendedores):
        calculos_previos = {}
        for metodo in self._metodos_de_priorizacion:
            calculos_previos.update(metodo.calculos_previos(vendedores))
        return calculos_previos

    def optimizar_consulta(self, vendedores):
        optimizar_consulta = vendedores
        for metodo in self._metodos_de_priorizacion:
            optimizar_consulta = metodo.optimizar_consulta(optimizar_consulta)
        return optimizar_consulta

    def factor_de_distribucion_de_vendedor(self, vendedor, calculos_previos):
        return sum([metodo.factor_de_distribucion_de_vendedor(vendedor, calculos_previos)
                    for metodo in self._metodos_de_priorizacion])

    @classmethod
    def descripcion(cls):
        return 'Manual + Administrador'

    @classmethod
    def default(cls):
        metodo = cls.nuevo_con(MetodoDeSeleccionPorFactorAdministrador(),
                               MetodoDeSeleccionPorFactorManual())
        return metodo

    @classmethod
    def nuevo_con(cls, metodo, otro_metodo):
        metodo = cls([metodo, otro_metodo])
        return metodo
