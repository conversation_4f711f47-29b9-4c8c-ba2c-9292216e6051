# coding=utf-8
import logging
import re

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Max
from django.utils import timezone

from core.locker.errors import ResourceLockedError
from core.locker.mem_locker import Locker
from core.models import Sistema
from crms import tasks as crms_tasks
from crms.models import OpcionClienteCRM
from occ.models.eavisos import ConversacionDeEAvisos
from prospectos.models import PedidoDeProspecto, ConfiguracionDeNotificacionDePedido, NingunVendedorSatisfaceError, \
    ListaDeRestriccionesDeVendedores, Prospecto
from prospectos.models.entrega_de_datos.filtros_de_prospectos import EvaluadorDeFiltrosDeProspecto
from prospectos.models.entrega_de_datos.opciones import ModoSeleccionDeVendedorChoices
from prospectos.models.entrega_de_datos.restricciones import RestriccionDeVendedores, RestriccionLimiteDatosDiario
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from reportes.pedidos import EstimadorDePedidos
from vendedores.models import Vendedor

logger = logging.getLogger('model_admin_logger')


class PedidoNoAplicaError(Exception):
    pass


class AdministradorDePedidos(object):

    @classmethod
    def nuevo(cls):
        return cls()

    def actualizar_pedidos_por_fecha(self):
        """
        Finaliza los pedidos activos del mes pasado.
        Duplica los renovables y les pone el contador de consumidos en cero.
        A los no renovables, les crea un nuevo pedido con el saldo restante si es que les queda algo de credito.
        """
        mes_actual = timezone.now().date().replace(day=1)
        # Ver todos los pedidos no finalizados, con fecha anterior al mes actual.
        pedidos = PedidoDeProspecto.objects.activos().fecha_anterior_a(mes_actual)
        for pedido in pedidos:
            self._renovar_pedido(mes_actual, pedido)
        supervisores = [pedido.obtener_supervisor() for pedido in pedidos]
        self.actualizar_limite_de_datos_diarios_desde_pedidos(supervisores)

    def pedido_modificado_o_creado(self, pedido, fue_creado, campos_modificados, supervisores_asignados):
        if self._debe_actualizar_limite_de_datos_diarios(pedido, fue_creado, campos_modificados):
            self.actualizar_limite_de_datos_diarios_desde_pedidos(supervisores_asignados)
            return self._mensaje_de_supervisores_actualizados(supervisores_asignados)
        else:
            return ''

    def actualizar_limite_de_datos_diarios_desde_pedidos(self, supervisores):
        estimador = EstimadorDePedidos()
        for supervisor in set(supervisores):
            self._actualizar_limite_de_datos_diarios_para_pedido(supervisor=supervisor, estimador=estimador)

    def _mensaje_de_supervisores_actualizados(self, supervisores_asignados):
        nombres = [supervisor.full_name() for supervisor in supervisores_asignados]
        nombres_como_string = ', '.join(nombres)
        return 'Se han actualizado el límite diario de %s' % nombres_como_string

    def _debe_actualizar_limite_de_datos_diarios(self, pedido, fue_creado, campos_modificados):
        modificaciones_genera_actualizacion = self._genera_actualizacion_de_limite_diario_las_modificaciones_de(
            campos_modificados)
        habilitado = pedido.actualizacion_limite_diario_de_supervisor_habilitada()
        return habilitado and (fue_creado or modificaciones_genera_actualizacion)

    def _actualizar_limite_de_datos_diarios_para_pedido(self, supervisor, estimador):
        # Se tienen en cuenta todos los pedidos del supervisor, no solo los de poner a responsable
        pedidos = supervisor.pedidos_activos()
        if not pedidos.de_asignacion_de_responsable().exists():
            return
        cantidad = 0
        for pedido in pedidos:
            cantidad_pedida = estimador.cantidad_de_datos_pedidos_para(pedido)
            longitud = pedido.duracion().days
            if longitud > 0:
                cantidad += cantidad_pedida / longitud

        total = round(cantidad * settings.PROPORCION_DE_ACTUALIZACION_DE_LIMITE_DIARIO_A_SUPERVISORES)
        supervisor.cambiar_limite_de_datos_diarios_para_supervisor_por(total)

    def _renovar_pedido(self, mes_actual, pedido):
        pedido_original_pk = pedido.id
        campanias = pedido.campanias.all()
        categorias = pedido.categorias.all()
        calidades = pedido.calidades()
        # Finalizarlos.
        pedido.finalizado = True
        pedido.save()
        filtros = pedido.filtros.all()
        # Duplicarlos
        pedido.id = None
        pedido.finalizado = False
        pedido.fecha = mes_actual
        if pedido.es_renovable:
            # Si era renovable, hacer que el duplicado sea igual
            pedido.consumido = 0
        # Si no es renovable solamente lo duplico si no se consumio su credito (sin contar la yapa).
        elif pedido.credito > pedido.consumido:
            # El duplicado debe tener el saldo restante y la misma yapa que tenia anteriormente.
            pedido.credito -= pedido.consumido
            pedido.consumido = 0
        else:
            return
        pedido.save()
        pedido.campanias = campanias
        pedido.categorias = categorias
        pedido.cambiar_calidades_por(calidades=calidades)
        pedido.save()
        self._replicar_filtros(filtros, pedido)
        self._replicar_configuracion_de_notificacion(pedido_original_pk, pedido)
        self._replicar_configuracion_de_clientes_crms(pedido_original_pk, pedido)

    def _replicar_filtros(self, filtros, pedido):
        for filtro in filtros:
            filtro.id = None
            filtro.pedido = pedido
            filtro.save()

    def _replicar_configuracion_de_notificacion(self, pedido_original_pk, nuevo_pedido):
        try:
            configuracion = ConfiguracionDeNotificacionDePedido.objects.get(pedido__pk=pedido_original_pk)
        except ConfiguracionDeNotificacionDePedido.DoesNotExist:
            pass
        else:
            configuracion.id = None
            configuracion.pedido = nuevo_pedido
            configuracion.save()

    def _replicar_configuracion_de_clientes_crms(self, pedido_original_pk, nuevo_pedido):
        opciones = OpcionClienteCRM.objects.de_pedido(pedido_original_pk)
        for opcion_cliente_crm in opciones:
            opcion_cliente_crm.clonar_para(nuevo_pedido)

    def _obtener_pedidos_que_aplican(self, prospecto):
        pedidos = PedidoDeProspecto.objects.activos_actuales()
        return self._filtrar_por_pedidos_que_aplican(pedidos, prospecto)

    def _filtrar_por_pedidos_que_aplican(self, pedidos, prospecto):
        aplican = []
        for pedido in pedidos:
            if self._prospecto_puede_satisfacer_pedido(pedido, prospecto):
                aplican.append(pedido)
        return aplican

    def _prospecto_puede_satisfacer_pedido(self, pedido, prospecto):
        if not pedido.satisface_campania(campania=prospecto.campania):
            return False
        if pedido.filtros.count() == 0:
            return True
        inclusiones = dict()
        for filtro in pedido.filtros.all():
            aplica_a_prospecto = self._filtro_aplica_a_prospecto(filtro, prospecto)
            if filtro.excluye():
                if aplica_a_prospecto:
                    return False
            # Incluir
            else:
                if filtro.campo in inclusiones:
                    inclusiones[filtro.campo] = inclusiones[filtro.campo] or aplica_a_prospecto
                else:
                    inclusiones[filtro.campo] = aplica_a_prospecto
        return self._todos_los_filtros_de_inclusion_aplican(inclusiones)

    def _todos_los_filtros_de_inclusion_aplican(self, inclusiones):
        # Los filtros por localidad y provincia son un OR. Es decir, con que alguno de los dos matchee, ya esta.
        for campo in inclusiones:
            aplica = inclusiones[campo]
            if campo not in ['localidad', 'provincia'] and not aplica:
                return False
        return self._aplica_algun_filtro_de_localidad_o_provincia(inclusiones)

    def _aplica_algun_filtro_de_localidad_o_provincia(self, inclusiones):
        if 'localidad' not in inclusiones and 'provincia' not in inclusiones:
            return True
        localidad_aplica = inclusiones.get('localidad', False)
        provincia_aplica = inclusiones.get('provincia', False)

        return localidad_aplica or provincia_aplica

    def _filtro_aplica_a_prospecto(self, filtro, prospecto):
        evaluador = EvaluadorDeFiltrosDeProspecto()
        return evaluador.aplica_para(filtro, prospecto)

    def _priorizar_pedidos(self, pedidos):
        # Elige cual de todos los pedidos a los que se puede asignar un prospecto tiene mas prioridad
        # Ultimo Orden por: Saldo Yapa
        ordenados = sorted(pedidos, key=lambda a: -(a.saldo_de_yapa()))
        # Tercer Orden por: Credito inicial
        ordenados = sorted(ordenados, key=lambda a: -a.credito)
        # Segundo orden por menos porcentaje de credito gastado
        ordenados = sorted(ordenados, key=lambda a: (a.fraccion_de_credito_consumida()))
        # Primer orden
        ordenados = sorted(ordenados, key=lambda a: (a.primer_orden_de_asignacion()))
        return ordenados

    def priorizar_pedidos(self, pedidos):
        return self._priorizar_pedidos(pedidos)

    def _elegir_pedido_por_prioridad(self, pedidos):
        ordenados = self._priorizar_pedidos(pedidos)
        pedido = self._elegir_un_pedido(ordenados)
        return pedido

    def _elegir_un_pedido(self, pedidos):
        try:
            pedido = pedidos[0]
        except IndexError:
            raise ValueError('No hay pedidos para este prospecto')
        else:
            return pedido

    def asignar_prospecto(self, a_prospecto, pedido, cantidad_consumida):
        a_prospecto.asignar_pedido(pedido=pedido)
        pedido.registrar_consumo(consumo=cantidad_consumida)
        self._enviar_integraciones_y_notificaciones(a_prospecto, pedido)
        if pedido.pk in settings.DEBUG_PEDIDOS:
            logger.debug(
                'Pedido: %s, prospecto asignado: %s, detalles: %s',
                pedido.pk, a_prospecto.pk, pedido.detalles_de_asignacion())

    def registrar_asignacion_de_prospectos(self, pedido, prospectos, consumido):
        pedido.registrar_consumo(consumo=consumido)
        if pedido.debe_enviar_notificaciones() or pedido.integraciones_con_crms().exists():
            for prospecto in prospectos:
                self._enviar_integraciones_y_notificaciones(prospecto, pedido)

    def pedidos_priorizados_para(self, pedidos, prospecto):
        pedidos = self._filtrar_por_pedidos_que_aplican(pedidos, prospecto)
        pedidos_priorizados = self._priorizar_pedidos(pedidos)
        return pedidos_priorizados

    def _asignar_prospecto_segun_pedido(self, pedido, prospecto):
        pedido.asignar_prospecto(prospecto=prospecto)
        self.asignar_prospecto(a_prospecto=prospecto, pedido=pedido,
                               cantidad_consumida=prospecto.campania.categoria.valor)

    def asignar_prospecto_automaticamente(self, prospecto):
        pedidos = self._obtener_pedidos_que_aplican(prospecto)
        pedidos_priorizados = self._priorizar_pedidos(pedidos)
        asignado = False
        while not asignado and pedidos_priorizados:
            pedido = self._elegir_un_pedido(pedidos_priorizados)
            if pedido.usa_nueva_forma_de_entrega():
                try:
                    self._asignar_prospecto_segun_pedido(prospecto=prospecto, pedido=pedido)
                except PedidoNoAplicaError:
                    asignado = False
                    pedidos_priorizados.remove(pedido)
                    if pedido.pk in settings.DEBUG_PEDIDOS:
                        logger.debug('Pedido %s rechazo prospecto: %s', pedido.pk, prospecto.pk)
                        logger.debug('Detalles: %s', pedido.detalles_de_asignacion())
                else:
                    asignado = True
            else:
                asignado = True
                self._vieja_forma_de_asignar_prospecto_segun_pedido(pedido, prospecto)
        if not asignado:
            logger.debug('Prospecto no asignado: %s' % prospecto.pk)



    def asignar_algun_prospecto_disponible_a_vendedor(self, vendedor):
        def cuando_existen_prospectos_disponibles(pedido, prospecto_disponible):
            return self._asignar_prospecto_lockeandolo(pedido, prospecto_disponible, vendedor)

        def cuando_no_existen_prospectos_disponibles():
            raise ValidationError('No hay prospectos disponibles')

        return self._cuando_tiene_prospectos_disponibles_hace(
            vendedor=vendedor,
            closure_cuando_existen_prospectos_disponibles=cuando_existen_prospectos_disponibles,
            closure_cuando_no_existen_prospectos_disponibles=cuando_no_existen_prospectos_disponibles)

    def tiene_prospecto_disponibles_para_entregar_a(self, vendedor):
        def cuando_existen_prospectos_disponibles(pedido, prospecto_disponible):
            return True

        def cuando_no_existen_prospectos_disponibles():
            return False

        return self._cuando_tiene_prospectos_disponibles_hace(
            vendedor=vendedor,
            closure_cuando_existen_prospectos_disponibles=cuando_existen_prospectos_disponibles,
            closure_cuando_no_existen_prospectos_disponibles=cuando_no_existen_prospectos_disponibles)

    def _cuando_tiene_prospectos_disponibles_hace(self, vendedor, closure_cuando_existen_prospectos_disponibles,
                                                  closure_cuando_no_existen_prospectos_disponibles):
        prospectos_disponibles = self.prospectos_disponibles_para_entregar_a_vendedor()
        pedidos_con_credito = self.pedidos_con_credito_de_vendedor(vendedor)
        tiene_pedidos_con_credito = len(pedidos_con_credito) > 0

        if prospectos_disponibles.exists() and tiene_pedidos_con_credito:
            for prospecto_disponible in prospectos_disponibles:
                for pedido in pedidos_con_credito:
                    if (self._prospecto_puede_satisfacer_pedido(prospecto=prospecto_disponible, pedido=pedido)
                            and not pedido.esta_restringido(vendedor=vendedor)):
                        return closure_cuando_existen_prospectos_disponibles(pedido=pedido,
                                                                             prospecto_disponible=prospecto_disponible)
        return closure_cuando_no_existen_prospectos_disponibles()


    def _asignar_prospecto_lockeandolo(self, pedido, prospecto_a_asignar, vendedor):
        lockeo = Locker.new_for_group('asignacion_de_prospectos')
        try:
            return lockeo.do_locking(prospecto_a_asignar.id, self._asignar_y_registrar_consumo,
                                     [pedido, prospecto_a_asignar, vendedor])
        except ResourceLockedError:
            raise ValueError("Ya se esta ejecutando una asignacion. Intente de nuevo en unos minutos")

    def _asignar_y_registrar_consumo(self, pedido, prospecto_disponible, vendedor):
        repartidor = RepartidorDeProspectos()
        repartidor.asignar_prospecto_a(vendedor=vendedor, prospecto=prospecto_disponible)
        pedido.registrar_consumo(consumo=prospecto_disponible.campania.valor_del_dato())
        return prospecto_disponible

    def prospectos_disponibles_para_entregar_a_vendedor(self):
        minutos_de_antiguedad_minima = Sistema.instance().tiempo_de_antiguedad_minimo_para_prospectos_disponibles
        prospectos_disponibles = Prospecto.objects.sin_vendedor_ni_responsable().que_ingresaron_hace_minutos(
            minutos=minutos_de_antiguedad_minima).ordenar_por_fecha()
        return prospectos_disponibles

    def pedidos_con_credito_de_vendedor(self, vendedor):
        pedidos = self._priorizar_pedidos(PedidoDeProspecto.objects.para_vendedor(vendedor=vendedor))
        pedidos_con_credito = [pedido for pedido in pedidos if pedido.tiene_credito_disponible()]
        return pedidos_con_credito

    def el_vendedor_no_supero_el_limite_diario(self, vendedor) -> bool:
        restricciones = ListaDeRestriccionesDeVendedores.con_filtros([RestriccionLimiteDatosDiario.nuevo()])
        vendedores = Vendedor.objects.filter(id=vendedor.id)
        vendedores_sin_restricciones = restricciones.filtrar(vendedores=vendedores)
        return vendedores_sin_restricciones.exists()

    def contabilizar_carga_de_prospecto_con_responsable(self, prospecto):
        pedidos = PedidoDeProspecto.objects.activos_actuales().filter(supervisor=prospecto.responsable)
        if pedidos:
            pedido = self._elegir_pedido_por_prioridad(pedidos)
            self.asignar_prospecto(a_prospecto=prospecto, pedido=pedido,
                                   cantidad_consumida=prospecto.campania.categoria.valor)

    def _vieja_forma_de_asignar_prospecto_segun_pedido(self, pedido, prospecto):
        repartidor = RepartidorDeProspectos.nuevo()
        vendedor = None
        if pedido.asignar_a in [ModoSeleccionDeVendedorChoices.VENDEDOR,
                                ModoSeleccionDeVendedorChoices.TODOS,
                                ModoSeleccionDeVendedorChoices.EQUIPO]:
            if pedido.asignar_a == ModoSeleccionDeVendedorChoices.VENDEDOR:
                if pedido.vendedor.user.is_active and pedido.vendedor.obtener_factor_de_asignacion() > 0:
                    vendedor = pedido.vendedor
            else:
                if pedido.asignar_a == ModoSeleccionDeVendedorChoices.TODOS:
                    vendedores = pedido.supervisor.vendedores.filter(user__is_active=True, factor_de_asignacion__gt=0)
                # if pedido.asignar_a equipos:
                else:
                    vendedores = pedido.equipo.integrantes.filter(user__is_active=True, factor_de_asignacion__gt=0)
                if vendedores.count() > 0:
                    vendedor = self._vendedor_con_mayor_prioridad(vendedores, distribuir_uniformemente=pedido.uniforme)

        if vendedor is None:
            repartidor.asignar_responsable_a(prospecto=prospecto, supervisor=pedido.supervisor)
        else:
            repartidor.asignar_prospecto_a(vendedor=vendedor, prospecto=prospecto)

        self.asignar_prospecto(a_prospecto=prospecto, pedido=pedido,
                               cantidad_consumida=prospecto.campania.categoria.valor)

    def _vendedor_con_mayor_prioridad(self, vendedores, distribuir_uniformemente):
        # Asume que los vendedores con factor de asignacion 0 fueron descartados previamente
        if distribuir_uniformemente:
            vendedores = vendedores.annotate(
                fecha_de_asignacion_a_vendedor=Max('prospectos__asignacion__fecha_de_asignacion_a_vendedor'))
            vendedores = vendedores.order_by('fecha_de_asignacion_a_vendedor')
            return vendedores[0]
        else:
            datos = []
            # Ordenar por relacion (creciente): cantidad de prospectos nuevos / factor_de_asignacion
            for vendedor in vendedores:
                ratio = float(vendedor.prospectos.filter(estado='N').count()) / vendedor.obtener_factor_de_asignacion()
                datos.append((vendedor, ratio))
            ordenados = sorted(datos, key=lambda a: a[1])
            return ordenados[0][0]

    def seleccionar_vendedor_segun_pedidos_para(self, campania, nombre_de_marca):
        pedidos = self.pedidos_que_aplican_para(campania, nombre_de_marca)
        pedidos_priorizados = self._priorizar_pedidos(pedidos)

        for pedido in pedidos_priorizados:
            vendedor_seleccionado = self._seleccionar_vendedor_desde_pedido(pedido=pedido)
            if vendedor_seleccionado is not None:
                return vendedor_seleccionado

        return None

    def _seleccionar_vendedor_desde_pedido(self, pedido):
        restriccion = RestricionPreguntasSinLeer()
        try:
            restricciones_del_pedido = pedido.filtro_de_vendedores().restricciones()
            restriccion_mensajes_sin_leer = ListaDeRestriccionesDeVendedores.con_filtros(
                restricciones_del_pedido + [restriccion])
            return pedido.seleccionar_vendedor_con_restricciones(restriccion_mensajes_sin_leer)
        except NingunVendedorSatisfaceError:
            return pedido.seleccionar_vendedor()

    def pedidos_que_aplican_para(self, campania, nombre_de_marca=None, variables=None):
        pedidos_que_aplican = []
        for pedido in PedidoDeProspecto.objects.activos_actuales():
            if pedido.satisface_campania(campania=campania) and self._satisface_filtros(
                    pedido, nombre_de_marca, variables):
                pedidos_que_aplican.append(pedido)
        return pedidos_que_aplican

    def _satisface_filtros(self, pedido, nombre_de_marca, variables):
        if not self._campo_satisface_filtros(pedido, campo='marca', valor=nombre_de_marca):
            return False

        campos = self._campos_para_filtros_de_pedidos()
        valores = {campo: variables.valor_de(campo) if variables else None for campo in campos}

        satisface_campos = []
        for campo, valor in valores.items():
            valor = self._verificador_valor_prefijo(campo, valor, valores)

            filtros_del_campo = [f for f in pedido.filtros.all() if f.refiere_a(campo)]

            # Si no hay filtros para este campo, lo ignoramos
            if not filtros_del_campo:
                satisface_campos.append(True)
                continue

            filtros_incluye = [f for f in filtros_del_campo if f.incluye()]
            filtros_excluye = [f for f in filtros_del_campo if f.excluye()]

            # Si hay filtros de inclusión pero no hay valor, no puede satisfacerlos
            if filtros_incluye and not valor:
                return False

            if filtros_excluye and not valor:
                satisface_campos.append(True)
                continue

            # Si hay filtros de inclusión y no matchea con ninguno, fallar
            self._verificar_satisface_campos(filtros_excluye, filtros_incluye, satisface_campos, valor)

        return all(satisface_campos)

    def _verificador_valor_prefijo(self, campo, valor, valores):
        if campo == 'prefijo' and (valor != '' and valor is not None):
            valor = re.sub('^0+', '', valor)
        elif campo == 'prefijo' and valor == '':
            # Si el campo es prefijo y no hay prefijo, hacemos una pasada con los
            # primeros 5 numeros del telefono para ver si contiene el prefijo
            valor = self._prefijo_de_telefono(valores)
        return valor

    def _verificar_satisface_campos(self, filtros_excluye, filtros_incluye, satisface_campos, valor):
        if isinstance(valor, list):
            satisface_incluye = filtros_incluye and any(f.satisface_alguno_de(valor) for f in filtros_incluye)
            satisface_excluye = len(filtros_excluye) == 0 or any(f.satisface_alguno_de(valor) for f in filtros_excluye)
        else:
            satisface_incluye = filtros_incluye and any(f.satisface(valor) for f in filtros_incluye)
            satisface_excluye = len(filtros_excluye) == 0 or any(f.satisface(valor) for f in filtros_excluye)
        # Si hay filtros de exclusión y matchea con alguno, fallar
        satisface_campos.append(satisface_incluye and satisface_excluye)

    def _prefijo_de_telefono(self, valores):
        telefono = valores.get('telefono', '')
        all_pat = self._patrones_para_telefono()
        for pat in all_pat:
            telefono = re.sub(pat, '', telefono)
        return telefono[:5]

    def _patrones_para_telefono(self):
        pat_1 = r'\D'
        pat_2 = r'^0+'
        pat_3 = r'^549?'
        all_pat = [pat_1, pat_2, pat_3, pat_2]
        return all_pat

    def _campos_para_filtros_de_pedidos(self):
        return ['provincia', 'localidad', 'telefono', 'prefijo', 'modelo']

    def _campo_satisface_filtros(self, pedido, campo, valor):
        if not valor:
            return True
        filtros_del_campo = [filtro for filtro in pedido.filtros.all() if filtro.refiere_a(campo)]
        satisface = not filtros_del_campo or any([filtro.satisface(valor) for filtro in filtros_del_campo])
        return satisface

    def _marca_satisface_pedido(self, marca, pedido):
        # NO tiene filtro por marca => True
        # tiene filtro por marca:
        #   a) misma marca y excluye => False
        #   b) misma marca y incluye => True
        #   c) otra marca => False
        incluida_otra_marca = False
        for filtro in pedido.filtros.all():
            if filtro.campo == 'marca':
                aplica_marca = filtro.aplica(marca)
                if aplica_marca:
                    return filtro.incluye()
                else:
                    incluida_otra_marca = True

        return not incluida_otra_marca

    def _enviar_integraciones_y_notificaciones(self, prospecto, pedido):
        self._enviar_a_crms(prospecto, pedido)
        self._enviar_notificaciones(prospecto, pedido)

    def _enviar_a_crms(self, prospecto, pedido):
        transaction.on_commit(lambda: self._enviar_prospecto(prospecto, a_crms=pedido.clientes_crms.all()))

    def _enviar_notificaciones(self, prospecto, pedido):
        if not pedido.debe_enviar_notificaciones():
            return
        from prospectos import tasks as prospectos_tasks
        vendedor = prospecto.vendedor
        if vendedor and vendedor.configuracion_servicios.notificaciones_habilitadas():
            transaction.on_commit(lambda: prospectos_tasks.enviar_notificaciones.delay(prospecto.pk,
                                                                                       vendedor.pk,
                                                                                       pedido.pk))
        responsable = prospecto.responsable
        if not vendedor and responsable and responsable.configuracion_servicios.notificaciones_habilitadas():
            transaction.on_commit(lambda: prospectos_tasks.enviar_notificaciones.delay(prospecto.pk,
                                                                                       responsable.pk,
                                                                                       pedido.pk))

    def _enviar_prospecto(self, prospecto, a_crms):
        for cliente in a_crms:
            crms_tasks.enviar_a_crm.delay(prospecto.pk, cliente.pk)

    def pedido_para_descontar(self, supervisor, accion):
        pedidos = PedidoDeProspecto.objects.de_supervisores([supervisor])
        pedidos = PedidoDeProspecto.objects.filtrar_pedidos_para(pedidos, accion)
        if pedidos.exists():
            return pedidos.first()
        else:
            return None

    def prospectos_que_aplican_a_pedido(self, pedido, fecha=None):
        from prospectos.models import Prospecto
        fecha = fecha or timezone.localtime(timezone.now()).date()
        prospectos = Prospecto.objects.filter(fecha_creacion__date=fecha)
        ids = [p.id for p in prospectos.all() if self._prospecto_puede_satisfacer_pedido(pedido, p)]
        return Prospecto.objects.filter(id__in=ids)

    def _genera_actualizacion_de_limite_diario_las_modificaciones_de(self, campos):
        interseccion = set(campos) & self._campos_que_generan_actualizacion_de_limite_de_supervisor()
        return len(interseccion) > 0

    def _campos_que_generan_actualizacion_de_limite_de_supervisor(self):
        return {'supervisor', 'credito', 'fecha', 'finalizado', 'asignar_a'}


class RestricionPreguntasSinLeer(RestriccionDeVendedores):
    def aplica(self, configuracion_de_entrega):
        seguidor = SeguidorDeActividadesDeVendedor.para(configuracion_de_entrega.vendedor())
        return seguidor.tiene_preguntas_sin_leer()

    def maxima_cantidad_a_entregar_para(self, configuracion_de_entrega):
        return float("inf")

    def detalle_de_aplicacion(self, configuracion_de_entrega):
        return {}


class SeguidorDeActividadesDeVendedor(object):
    def __init__(self, vendedor):
        self._vendedor = vendedor

    @classmethod
    def para(cls, vendedor):
        return cls(vendedor)

    def tiene_preguntas_sin_leer(self):
        return ConversacionDeEAvisos.objects.para_vendedor(self._vendedor).no_leidas().exists()
