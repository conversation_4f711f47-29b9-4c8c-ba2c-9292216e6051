# coding=utf-8

from dal import autocomplete
from django.conf.urls import url
from django.contrib import admin, messages
from django.urls import reverse
from django.db.models import Case, DateField
from django.db.models import When
from django.http import HttpResponseRedirect
from django.shortcuts import render
from django.utils.timezone import now
from django.utils.translation import ngettext

from crms.models import OpcionClienteCRM
from log_de_errores.admin import LogDeErrorAdmin
from prospectos.models import Marca, Modelo, MarcaDeTarjetaDeCredito, Alias, RegistroDeResultadoDeAsignacionInicial, \
    ProspectoAsignadoDesdeAsignacionInicial, PeticionDeProspectoPorParteDelVendedor, CirculacionDeProspecto
from prospectos.models.base import (Prospecto, Comentario, Llamado, Venta, Finalizacion, MotivoDeFinalizacion,
                                    CampoExtra, SubidaErronea, Rechazo, FiltroDePedido, PedidoDeProspecto,
                                    <PERSON><PERSON><PERSON><PERSON>, Compra, CargaFallidaDeJotform, LogDeErrorNormalizador,
                                    LogDeErrorChequeadorDeWhatsapp, LogDeErrorDeInformacionDeRedesSociales,
                                    ConfiguracionDeNotificacionDePedido, LogDeErrorDeCRM, LogDeExportacionDeProspecto,
                                    AsignacionDeProspecto)
from prospectos.models.entrega_de_datos.pedidos import AdministradorDePedidos
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from prospectos.models.permiso_pedir_prospecto_segun_disponibilidad import PermisoPedirProspectoSegunDisponibilidad
from prospectos.utils.exportacion_a_csv import ExportadorDeCargasFallidasACSV
from prospectos.views import admin_views
from prospectos.views.admin_filters import CampaniaListFilter, ResponsableListFilter, VendedorListFilter
from prospectos.views.admin_filters import FiltroEquiposView
from prospectos.views.admin_forms import ReasignarProspectosAdminForm, ProspectoAdminForm, PedidoDeProspectoAdminForm, \
    OpcionClienteCRMForm, CompraAdminForm
from prospectos.views.admin_views import AddProspectosCSVDoc, ReporteDeComprasView, ReporteDeDatosView, \
    ExportarProspectosView, FiltrosExportarProspectosView, InformacionPedidoDeProspectosView, ReporteSimplificadoView
from users.models import User
from vendedores.models import Vendedor


class CampoExtraInlineAdmin(admin.TabularInline):
    model = CampoExtra
    extra = 0


class FinalizacionInlineAdmin(admin.TabularInline):
    model = Finalizacion
    extra = 0


class RechazoInlineAdmin(admin.TabularInline):
    model = Rechazo
    fields = ['prospecto', 'responsable', 'datetime']
    readonly_fields = ['datetime']
    extra = 0


class AsignacionDeProspectoInlineAdmin(admin.TabularInline):
    model = AsignacionDeProspecto
    readonly_fields = ['fecha_de_asignacion_a_vendedor', 'fecha_de_asignacion_a_supervisor']
    extra = 0
    can_delete = False


class ProspectoAdmin(admin.ModelAdmin):
    list_display = ('campania', 'fecha', 'nombre', 'responsable', 'vendedor', 'provincia', '_marca', 'id')
    list_display_links = ('fecha',)
    list_filter = (CampaniaListFilter, ResponsableListFilter, VendedorListFilter, '_modo_de_ingreso')
    search_fields = ('nombre', 'provincia', 'telefono', 'email', '=prefijo', '_marca___nombre', 'id')
    actions = ['reasignar_prospectos']
    readonly_fields = ['pedido', 'estado', 'consumido_en_pedido', '_tiempo_de_respuesta', '_modo_de_ingreso',
                       'exportado', '_puede_circular']
    exclude = ['asignacion', 'archivado', '_grupo_de_repetidos', 'informacion_de_redes_sociales_pedida']
    date_hierarchy = 'fecha_creacion'

    # No mejoro la performance
    # list_select_related = ['_marca', 'campania__categoria', 'responsable__user']
    form = ProspectoAdminForm
    inlines = [CampoExtraInlineAdmin, FinalizacionInlineAdmin, RechazoInlineAdmin, AsignacionDeProspectoInlineAdmin]

    def get_urls(self):
        urls = super(ProspectoAdmin, self).get_urls()
        pull_urls = [
            url(r'^add_prospectos/$', admin_views.ImportarProspectosDesdeCSVView.as_view(), name='add_prospectos'),
            url(r'^asignacion_inicial_old/$', admin_views.asignacion_inicial, name='asignacion_inicial_old'),
            url(r'^asignacion_inicial/$', admin_views.AsignacionInicialView.as_view(), name='asignacion_inicial'),
            url(r'^cantidad_filtrada/$', admin_views.cantidad_filtrada, name='cantidad_filtrada_admin'),
            url(r'^distribucion_inicial/$', admin_views.distribucion_inicial, name='distribucion_inicial'),
            url(r'^cantidad_filtrada_distribucion/$', admin_views.cantidad_filtrada_distribucion,
                name='cantidad_filtrada_distribucion_admin'),
            url(r'^add_prospectos/doc/$', AddProspectosCSVDoc.as_view(), name='add-prospectos-csv-doc'),
            url(r'^reportes/datos/$', ReporteDeDatosView.as_view(), name='reporte-de-datos'),
            url(r'^exportar_prospectos/$', ExportarProspectosView.as_view(), name='exportar-prospectos'),
            url(r'^reporte_simplificado/$', ReporteSimplificadoView.as_view(),
                name='reporte-simplificado-de-prospectos'),
            url(r'^filtros_exportar_prospectos/$', FiltrosExportarProspectosView.as_view(),
                name='filtros_para_exportar_prospectos'),
            url(r'^info_pedido/(?P<pk>\d+)/$', InformacionPedidoDeProspectosView.as_view(), name='info_pedidos'),
        ]
        return pull_urls + urls

    def reasignar_prospectos(self, request, queryset):
        form = None

        if 'apply' in request.POST:
            form = ReasignarProspectosAdminForm(request.POST)

            if form.is_valid():
                vendedor = form.cleaned_data['vendedor']
                responsable = form.cleaned_data['responsable']
                repartidor = RepartidorDeProspectos.nuevo()
                prospectos = queryset
                cantidad = prospectos.count()
                if vendedor:
                    repartidor.asignar_prospectos_a(vendedor=vendedor, prospectos=prospectos)
                    self.message_user(
                        request,
                        "Se han puesto %d prospectos a cargo del Supervisor %s." % (cantidad, vendedor.supervisor))
                    self.message_user(request, "Se reasignaron %d prospectos al Vendedor %s." % (cantidad, vendedor))
                else:
                    if responsable:
                        repartidor.asignar_prospectos_a_responsable(supervisor=responsable, prospectos=prospectos)
                        self.message_user(
                            request,
                            "Se han puesto %d prospectos a cargo del Supervisor %s." % (cantidad, responsable))
                        self.message_user(request,
                                          "Se han puesto %d prospectos sin Vendedor a cargo." % cantidad)
                    else:
                        repartidor.quitar_responsable_a_prospectos(prospectos=prospectos)
                        self.message_user(
                            request,
                            "Se han puesto %d prospectos sin Supervisor Ni Vendedor a cargo." % cantidad)

                return HttpResponseRedirect(request.get_full_path())

        if not form:
            form = ReasignarProspectosAdminForm(
                initial={'_selected_action': request.POST.getlist(admin.ACTION_CHECKBOX_NAME)})

        return render(
            request,
            'admin/prospectos/reasignar.html',
            {
                'prospectos': queryset,
                'user_form': form,
            }
        )

    reasignar_prospectos.short_description = "Reasignar Prospectos"

    def formfield_for_foreignkey(self, db_field, request=None, **kwargs):
        if db_field.name in ["responsable"]:
            kwargs["queryset"] = Vendedor.objects.filter(cargo='Supervisor')
        return super(ProspectoAdmin, self).formfield_for_foreignkey(db_field, request, **kwargs)

    def get_changelist(self, request, **kwargs):
        changelist_class = super(ProspectoAdmin, self).get_changelist(request, **kwargs)

        class CustomChangeList(changelist_class):
            def __init__(self, request, *args, **kwargs):
                self._PARAMETRO_DE_FILTRADO = 'lista_completa'
                super(CustomChangeList, self).__init__(request, *args, **kwargs)

            def get_filters_params(self, params=None):
                lookup_params = super(CustomChangeList, self).get_filters_params(params)
                if self._PARAMETRO_DE_FILTRADO in lookup_params:
                    del lookup_params[self._PARAMETRO_DE_FILTRADO]

                return lookup_params

        return CustomChangeList

    def get_queryset(self, request):
        prospectos = super(ProspectoAdmin, self).get_queryset(request)
        if self._request_va_a_la_lista(request):
            ver_lista_completa = request.GET.get('lista_completa', False)
            if not ver_lista_completa:
                from django.utils import timezone
                from dateutil.relativedelta import relativedelta
                return prospectos.entre_fechas(timezone.now() - relativedelta(months=3), timezone.now())

        return prospectos

    def _request_va_a_la_lista(self, request):
        from django.urls import reverse
        request_va_a_la_lista = request.path == reverse(
            'admin:%s_prospecto_changelist' % self.model._meta.app_label)
        return request_va_a_la_lista


class SubidaErroneaAdmin(admin.ModelAdmin):
    list_display = ('archivo', 'fecha', 'responsable', 'fallidas', 'exitosas', 'campania', 'vendedor')
    date_hierarchy = 'fecha'

    def has_add_permission(self, request):
        return False


class FiltroDePedidoInline(admin.TabularInline):
    model = FiltroDePedido
    extra = 0


class ClientesCRMSInline(admin.TabularInline):
    model = OpcionClienteCRM
    extra = 0
    form = OpcionClienteCRMForm
    template = 'admin/prospectos/pedidodeprospecto/tabular.html'

    def __init__(self, model, admin_site):
        self.form.admin_site = admin_site
        super(ClientesCRMSInline, self).__init__(model, admin_site)


class ConfiguracionDeNotificacionDePedidoInline(admin.StackedInline):
    model = ConfiguracionDeNotificacionDePedido
    can_delete = False
    verbose_name = 'Configuración para'
    verbose_name_plural = 'Configuración de Notificaciones'


class PedidoDeProspectoAdmin(admin.ModelAdmin):
    date_hierarchy = 'fecha'
    DEFAULT_LIST_PER_PAGE = 100
    list_per_page = DEFAULT_LIST_PER_PAGE
    actions_on_bottom = True
    actions_on_top = True
    form = PedidoDeProspectoAdminForm
    inlines = [FiltroDePedidoInline, ClientesCRMSInline, ConfiguracionDeNotificacionDePedidoInline]
    list_display = ('__str__', 'concesionaria', 'supervisor_link', 'fecha', 'credito', 'yapa', 'consumido',
                    'porcentaje_entregados', 'es_renovable', 'finalizado', 'factor_de_distribucion')
    list_editable = ['factor_de_distribucion', ]
    search_fields = ('supervisor__user__first_name', 'supervisor__user__last_name', 'equipo__nombre', 'nombre',
                     'supervisor__concesionaria__nombre')

    fieldsets = (
        (None, {'fields': ['nombre', 'crear_para_concesionaria', 'concesionaria', 'supervisor', 'credito',
                           'yapa', 'fecha', 'es_renovable', '_actualizacion_limite_diario_de_supervisor_habilitada',
                           'finalizado', '_calidades', 'categorias', '_excluye_campanias', 'campanias']}),
        ('Configuración de asignación', {
            'fields': ['forma_de_entrega', 'asignar_a', 'vendedor',
                       'metodo_de_asignacion', 'metodo_por_productividad',
                       'equipo', 'factor_de_distribucion', 'restringir_por_datos_diarios',
                       'restringir_por_datos_nuevos', 'restringir_por_acceso'],
        }),
    )
    ordering = ['supervisor__concesionaria', 'nombre']
    actions = ['duplicar_pedidos']

    # Uso esta funcion como hook para generar los pedidos nuevos, ya que aca ya se salvo el pedido.
    def save_related(self, request, form, formsets, change):
        super(PedidoDeProspectoAdmin, self).save_related(request, form, formsets, change)
        pedido = form.instance
        fue_creado = not change
        supervisores = self._duplicar_pedidos_si_es_necesario_y_obtener_supervisores(form, fue_creado, pedido)
        administrador = AdministradorDePedidos()
        resultado = administrador.pedido_modificado_o_creado(
            pedido, fue_creado, campos_modificados=form.changed_data, supervisores_asignados=supervisores)
        messages.add_message(request, messages.INFO, resultado)

    def _duplicar_pedidos_si_es_necesario_y_obtener_supervisores(self, form, fue_creado, pedido):
        """
            Al crear pedidos si crear_para_concesionaria esta configurado, se duplica el pedido a todos sus
            supervisores. Responde los supervisores de los pedidos creados.
        """
        if fue_creado:
            concesionaria = form.cleaned_data.get('concesionaria')
            if self._hay_que_duplicar_pedido_para_toda_la_concesionaria(form):
                self._duplicar_pedido_para_cada_supervisor_de(concesionaria, pedido)
                return concesionaria.supervisores()
        return [pedido.obtener_supervisor()]

    def _hay_que_duplicar_pedido_para_toda_la_concesionaria(self, form):
        concesionaria = form.cleaned_data.get('concesionaria')
        debe_duplicar_a_toda_la_concesionaria = form.cleaned_data.get('crear_para_concesionaria')
        consecionaria_esta_definida = concesionaria is not None
        return debe_duplicar_a_toda_la_concesionaria and consecionaria_esta_definida

    def get_urls(self):
        urls = super(PedidoDeProspectoAdmin, self).get_urls()
        my_urls = [url(r'^admin_filtro_equipos/$', FiltroEquiposView.as_view()), ]
        return my_urls + urls

    class Media:
        js = (
            'js/jquery.min.js',
            'js/system_unavailable.js',
        )

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ['consumido', ]
        else:
            return ['consumido', 'finalizado', ]

    def porcentaje_entregados(self, instance):
        porcentaje = instance.porcentaje_entregado()

        if porcentaje < 50:
            color = 'red'
        elif porcentaje < 100:
            color = 'goldenrod'
        else:
            color = 'green'

        mensaje = '<span style="color: %s;">%.2f%%</span>' % (color, porcentaje)
        return mensaje

    porcentaje_entregados.short_description = "Porcentaje de entrega"
    porcentaje_entregados.allow_tags = True
    porcentaje_entregados.admin_order_field = '_porcentaje_entregado'

    def supervisor_link(self, instance):
        url = reverse("admin:vendedores_vendedor_change", args=(instance.supervisor.id,))
        return '<a href="%s">%s</a>' % (url, instance.supervisor)

    supervisor_link.allow_tags = True
    supervisor_link.short_description = "Supervisor"

    def get_changelist(self, request, **kwargs):
        changelist_class = super(PedidoDeProspectoAdmin, self).get_changelist(request, **kwargs)

        class CustomChangeList(changelist_class):
            def __init__(self, request, *args, **kwargs):
                self._PARAMETRO_DE_FILTRADO = 'lista_completa'
                super(CustomChangeList, self).__init__(request, *args, **kwargs)

            def get_filters_params(self, params=None):
                lookup_params = super(CustomChangeList, self).get_filters_params(params)
                if self._PARAMETRO_DE_FILTRADO in lookup_params:
                    del lookup_params[self._PARAMETRO_DE_FILTRADO]

                return lookup_params

        return CustomChangeList

    def get_queryset(self, request):
        pedidos = super(PedidoDeProspectoAdmin, self).get_queryset(request)
        if self._request_va_a_la_lista(request):
            ver_lista_completa = request.GET.get('lista_completa', False)
            if ver_lista_completa:
                self.paginator.per_page = self.DEFAULT_LIST_PER_PAGE
            else:
                pedidos = pedidos.del_mes()
                self.paginator.per_page = pedidos.count()
        return pedidos.anotar_porcentaje_entregado()

    def _request_va_a_la_lista(self, request):
        from django.urls import reverse
        request_va_a_la_lista = request.path == reverse(
            'admin:%s_pedidodeprospecto_changelist' % self.model._meta.app_label)
        return request_va_a_la_lista

    def _duplicar_pedido_para_cada_supervisor_de(self, concesionaria, pedido):
        for supervisor in concesionaria.supervisores():
            if not supervisor == pedido.supervisor:
                self._duplicar_pedido_para(supervisor, pedido)

    def _duplicar_pedido_para(self, supervisor, pedido):
        campanias = pedido.campanias.all()
        categorias = pedido.categorias.all()
        calidades = pedido.calidades()
        filtros = pedido.filtros.all()
        integraciones_crms = pedido.integraciones_con_crms()
        pedido.id = None
        pedido.supervisor = supervisor
        pedido.save()
        pedido.campanias = campanias
        pedido.categorias = categorias
        pedido.cambiar_calidades_por(calidades=calidades)
        pedido.save()
        # Replicar tambien los Filtros
        for filtro in filtros:
            filtro.id = None
            filtro.pedido = pedido
            filtro.save()
        # Replicar tambien las integraciones de crm creadas.
        for integracion in integraciones_crms:
            integracion.id = None
            integracion.pedido = pedido
            integracion.save()

    def duplicar_pedidos(self, request, queryset):
        duplicados = 0
        for pedido in queryset:
            # Guardamos relaciones antes de duplicar
            campanias = pedido.campanias.all()
            categorias = pedido.categorias.all()
            calidades = pedido.calidades()
            filtros = pedido.filtros.all()
            integraciones_crms = pedido.integraciones_con_crms()

            pedido.id = None  # Clonamos el objeto
            pedido.nombre += " (copia)"
            pedido.fecha = now().date()  # 👉 Fecha de comienzo en hoy
            pedido.consumido = 0  # 👉 Crédito consumido en 0
            pedido.save()

            pedido.campanias = campanias
            pedido.categorias = categorias
            pedido.cambiar_calidades_por(calidades=calidades)

            # Duplicamos filtros
            for filtro in filtros:
                filtro.id = None
                filtro.pedido = pedido
                filtro.save()

            # Duplicamos integraciones CRM
            for integracion in integraciones_crms:
                integracion.id = None
                integracion.pedido = pedido
                integracion.save()

            duplicados += 1

        self.message_user(request, ngettext(
            '%d pedido duplicado correctamente.',
            '%d pedidos duplicados correctamente.',
            duplicados,
        ) % duplicados, messages.SUCCESS)

    duplicar_pedidos.short_description = "Copiar pedidos seleccionados"


class ProveedorAutocomplete(autocomplete.Select2QuerySetView):
    def get_queryset(self):
        if not self.request.user.is_authenticated():
            return Proveedor.objects.none()

        proveedores = Proveedor.objects.all()

        if self.q:
            proveedores = proveedores.filter(source_id__istartswith=self.q)

        return proveedores


class CompraAdmin(admin.ModelAdmin):
    list_display = ('proveedor', 'mes', 'anio')
    list_filter = ('proveedor',)
    form = CompraAdminForm

    def response_add(self, request, obj, post_url_continue=None):
        agregar_compra_url = reverse('admin:agregar_compra')
        if agregar_compra_url == request.path:
            return HttpResponseRedirect(agregar_compra_url)
        else:
            return super(CompraAdmin, self).response_add(request, obj, post_url_continue)

    def has_add_permission(self, request):
        return request.user.is_authenticated

    def get_urls(self):
        urls = super(CompraAdmin, self).get_urls()
        pull_urls = [
            url(r'^agregar/$', self.add_view, name='agregar_compra'),
            url(r'^reportes/$', ReporteDeComprasView.as_view(), name='reporte-de-compras'),
            url(r'^proveedor-autocomplete/$', ProveedorAutocomplete.as_view(), name='proveedor-autocomplete'),
        ]
        return pull_urls + urls

    class Media:
        js = (
            'js/jquery.min.js',
        )


class CargaFallidaDeJotformAdmin(admin.ModelAdmin):
    list_display = ('fecha', 'error', 'exportado',)
    list_filter = ('exportado',)
    actions = ['exportar_a_csv']
    date_hierarchy = 'fecha'

    def get_readonly_fields(self, request, obj=None):
        return 'datos', 'fecha', 'error', 'exportado'

    def exportar_a_csv(self, request, queryset):
        exportador = ExportadorDeCargasFallidasACSV(queryset)
        response = exportador.response_para_exportar_a_csv()
        return response

    exportar_a_csv.short_description = "Exportar a CSV"

    def has_add_permission(self, request):
        return False


class VentaAdmin(admin.ModelAdmin):
    list_display = ('vendedor', 'precio', 'obtener_concesionaria', 'obtener_supervisor', 'modelo',
                    'estado', 'mes_de_venta')
    ordering = ['vendedor', 'precio', 'modelo', 'estado', 'vendedor__concesionaria', 'vendedor__supervisor',
                ]
    list_filter = (
        'estado', 'fecha_de_aprobacion', 'prospecto__campania', 'vendedor__concesionaria', 'vendedor__supervisor',
    )

    def get_queryset(self, request):
        qs = super(VentaAdmin, self).get_queryset(request)
        qs.select_related('vendedor', 'vendedor__concesionaria', 'vendedor__supervisor')
        qs = qs.annotate(
            mes_de_venta=Case(
                When(estado=Venta.APROBADA, then="fecha_de_aprobacion"),
                When(estado=Venta.PENDIENTE, then="fecha_de_realizacion"),
                default="fecha_de_realizacion",
                output_field=DateField()
            ))
        return qs

    def mes_de_venta(self, obj):
        return obj.mes_de_venta

    mes_de_venta.admin_order_field = 'mes_de_venta'

    def obtener_concesionaria(self, obj):
        return obj.vendedor.obtener_concesionaria()

    obtener_concesionaria.short_description = "Concesionaria"
    obtener_concesionaria.admin_order_field = 'vendedor__concesionaria'

    def obtener_supervisor(self, obj):
        return obj.vendedor.supervisor

    obtener_supervisor.short_description = "Supervisor"
    obtener_supervisor.admin_order_field = 'vendedor__supervisor'

    def get_readonly_fields(self, request, obj=None):
        if obj:
            return ['prospecto', ]
        else:
            return []


class ComentarioAdmin(admin.ModelAdmin):
    readonly_fields = ('prospecto', 'vendedor')


class LogDeExportacionDeProspectoAdmin(admin.ModelAdmin):
    model = LogDeExportacionDeProspecto
    list_display = ('user', 'fecha', 'cantidad')
    list_filter = ('user',)
    date_hierarchy = 'fecha'

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


class AliasAdmin(admin.ModelAdmin):
    list_filter = ('_marca',)


class AliasInline(admin.StackedInline):
    model = Alias
    fk_name = "_marca"
    fields = ["_nombre"]
    extra = 0
    verbose_name_plural = 'Aliases'


class MarcaAdmin(admin.ModelAdmin):
    model = Marca
    search_fields = ['_nombre', '_codigo', '_aliases___nombre']
    inlines = [AliasInline]

    def has_add_permission(self, request):
        return request.user.is_authenticated

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return ['_identificador', '_codigo']
        return ['_identificador']


class MarcaDeTarjetaDeCreditoAdmin(admin.ModelAdmin):
    model = MarcaDeTarjetaDeCredito
    search_fields = ['_nombre']

    def has_add_permission(self, request):
        return True

    def has_delete_permission(self, request, obj=None):
        return True


class ModeloAdmin(admin.ModelAdmin):
    model = Modelo
    list_filter = ('_marca',)

    def has_add_permission(self, request):
        return request.user.is_authenticated

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        if obj:  # editing an existing object
            return ['_identificador', '_codigo', '_marca']
        return ['_identificador']


class ProspectoAsignadoDesdeAsignacionInicialInline(admin.TabularInline):
    model = ProspectoAsignadoDesdeAsignacionInicial
    extra = 0
    readonly_fields = ['_tipo_de_asignacion', '_asignado', '_nombre_de_prospecto', '_telefono_del_prospecto',
                       '_marca_de_prospecto', '_link_al_prospecto']
    exclude = ['_prospecto']

    can_delete = False
    verbose_name = u'Asignacion'
    verbose_name_plural = u'Asignaciones'

    def _nombre_de_prospecto(self, obj):
        return obj.prospecto().obtener_nombre()
    _nombre_de_prospecto.short_description = "Nombre"

    def _marca_de_prospecto(self, obj):
        return obj.prospecto().obtener_marca().nombre()
    _marca_de_prospecto.short_description = "Marca"

    def _telefono_del_prospecto(self, obj):
        return obj.prospecto().obtener_telefono()
    _telefono_del_prospecto.short_description = "Teléfono principal"

    def _link_al_prospecto(self, obj):
        return '<a href="%(url)s">%(url)s</a>' % {
            'url': reverse('admin:prospectos_prospecto_change', args=(obj.prospecto_id(),)),
        }
    _link_al_prospecto.short_description = "Prospecto"
    _link_al_prospecto.allow_tags = True

    def has_add_permission(self, request):
        return False


class UsuariosEnRegistroDeResultadoDeAsignacionInicialFilter(admin.SimpleListFilter):
    title = 'Usuarios'

    # Parámetro que se usará en la URL del admin para este filtro
    parameter_name = 'staff_user'

    def lookups(self, request, model_admin):
        """
        Devuelve una lista de tuplas. Cada tupla contiene un identificador para la opción
        de filtro y la etiqueta que se mostrará en la interfaz del admin.
        """
        staff_users = User.objects.filter(is_staff=True)
        return [(user.id, user.username) for user in staff_users]

    def queryset(self, request, queryset):
        """
        Filtra el queryset basado en el valor seleccionado en el filtro.
        """
        if self.value():
            return queryset.filter(_usuario__id=self.value())
        return queryset


class RegistroDeResultadoDeAsignacionInicialAdmin(admin.ModelAdmin):
    list_display = ['_fecha', '_descripcion', '_ids_de_prospectos',  '_usuario', '_cantidad_de_datos_entregados',
                    '_cantidad_de_datos_no_entregados']
    search_fields = ['_asignaciones___prospecto__telefono']
    list_filter = [UsuariosEnRegistroDeResultadoDeAsignacionInicialFilter, '_descripcion']
    date_hierarchy = '_fecha'
    model = RegistroDeResultadoDeAsignacionInicial
    inlines = [ProspectoAsignadoDesdeAsignacionInicialInline]

    def _ids_de_prospectos(self, obj):
        limite = 10
        asignaciones = obj.ids_de_prospectos_asignados()
        string_de_continuacion = '...' if asignaciones.count() > limite else ''
        return ', '.join([str(id_de_prospecto) for id_de_prospecto in asignaciones[:limite]]) + string_de_continuacion
    _ids_de_prospectos.short_description = "Ids de prospectos asignados"

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        return ['_fecha', '_descripcion', '_usuario', '_cantidad_de_datos_entregados',
                '_cantidad_de_datos_no_entregados']

class PeticionDeProspectoPorParteDelVendedorAdmin(admin.ModelAdmin):
    list_display =  ['_vendedor', '_fecha_de_alta','_fecha_de_resolucion' , '_estado', '_detalle']
    model = PeticionDeProspectoPorParteDelVendedor

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        return ['_vendedor', '_fecha_de_alta','_fecha_de_resolucion' , '_estado', '_detalle']

class PermisoPedirProspectoSegunDisponibilidadAdmin(admin.ModelAdmin):
    list_display =  ['_vendedor', '_tiene_prospectos_disponibles']
    model = PermisoPedirProspectoSegunDisponibilidad
    search_fields = ['_vendedor__user__first_name', '_vendedor__user__last_name']
    list_filter = ['_tiene_prospectos_disponibles']

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        return ['_vendedor', '_tiene_prospectos_disponibles']

class CirculacionDeProspectoAdmin(admin.ModelAdmin):
    list_display =  ['_fecha', '_cedente', '_destinatario','_prospecto']
    search_fields = ['_cedente__user__first_name', '_cedente__user__last_name', '_destinatario__user__first_name', '_destinatario__user__last_name']
    date_hierarchy = '_fecha'
    model = CirculacionDeProspecto

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def get_readonly_fields(self, request, obj=None):
        return ['_cedente', '_destinatario','_prospecto' , '_fecha']


admin.site.register(Prospecto, ProspectoAdmin)
admin.site.register(MarcaDeTarjetaDeCredito, MarcaDeTarjetaDeCreditoAdmin)
admin.site.register(Marca, MarcaAdmin)
admin.site.register(Alias, AliasAdmin)
admin.site.register(Modelo, ModeloAdmin)
admin.site.register(Comentario, ComentarioAdmin)
admin.site.register(Llamado)
admin.site.register(Venta, VentaAdmin)
admin.site.register(MotivoDeFinalizacion)
admin.site.register(SubidaErronea, SubidaErroneaAdmin)
admin.site.register(Rechazo)
admin.site.register(PedidoDeProspecto, PedidoDeProspectoAdmin)
admin.site.register(Proveedor)
admin.site.register(Compra, CompraAdmin)
admin.site.register(LogDeExportacionDeProspecto, LogDeExportacionDeProspectoAdmin)
admin.site.register(CargaFallidaDeJotform, CargaFallidaDeJotformAdmin)
admin.site.register(LogDeErrorNormalizador, LogDeErrorAdmin)
admin.site.register(LogDeErrorChequeadorDeWhatsapp, LogDeErrorAdmin)
admin.site.register(LogDeErrorDeInformacionDeRedesSociales, LogDeErrorAdmin)
admin.site.register(LogDeErrorDeCRM, LogDeErrorAdmin)
admin.site.register(RegistroDeResultadoDeAsignacionInicial, RegistroDeResultadoDeAsignacionInicialAdmin)
admin.site.register(PermisoPedirProspectoSegunDisponibilidad, PermisoPedirProspectoSegunDisponibilidadAdmin)
admin.site.register(PeticionDeProspectoPorParteDelVendedor, PeticionDeProspectoPorParteDelVendedorAdmin)
admin.site.register(CirculacionDeProspecto, CirculacionDeProspectoAdmin)
