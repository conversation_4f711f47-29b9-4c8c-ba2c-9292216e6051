from django.utils import timezone

from core.tests.validators.base import Validator
from notificaciones.tests.soporte import NotificacionesTestHelper


class ValidadorDePropuesta(Validator):
    def __init__(self, testcase):
        super(ValidadorDePropuesta, self).__init__(testcase)
        self._notificaciones_helper = NotificacionesTestHelper.nuevo_para(self._testcase)

    def notificaciones_helper(self):
        return self._notificaciones_helper

    def assert_registro_de_envios(self, canitdad, forma_de_envio, vendedor):
        supervisor = vendedor.responsable()
        registros = supervisor.registros_diario_de_envio_de_propuestas
        registros = registros.para_forma_de_envio(forma_de_envio, timezone.now().date())
        self.assertEqual(registros.count(), 1)
        registro = registros.first()
        self.assertEqual(registro.cantidad(), canitdad)

    def assert_conversacion_whatsapp(self, prospecto, propuesta, texto_esperado=None):
        texto_esperado = texto_esperado or propuesta.texto_whatsapp()
        mensaje = self._notificaciones_helper.assert_conversacion_whatsapp_con_un_mensaje(
            prospecto, texto_esperado=texto_esperado)
        self.assertEqual(mensaje.origen().propuesta(), propuesta)

    def assert_conversacion_sms(self, prospecto, propuesta, texto_esperado=None):
        texto_esperado = texto_esperado or propuesta.texto_sms()
        mensaje = self._notificaciones_helper.assert_envio_de_sms_marcar_como_realizado_y_assert_conversacion(
                        prospecto, texto_esperado=texto_esperado)
        self.assertEqual(mensaje.origen().propuesta(), propuesta)

    def assert_conversacion_email(self, prospecto, propuesta, texto_esperado=None):
        texto_esperado = texto_esperado or propuesta.html_email()
        mensaje = self._notificaciones_helper.assert_conversacion_email_con_un_mensaje(
                        prospecto, texto_esperado=texto_esperado)
        self.assertEqual(mensaje.origen().propuesta(), propuesta)
