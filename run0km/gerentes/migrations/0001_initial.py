# -*- coding: utf-8 -*-


from django.db import models, migrations
import django.db.models.deletion
from django.conf import settings


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('concesionarias', '0007_concesionaria_sms_habilitado'),
    ]

    operations = [
        migrations.CreateModel(
            name='<PERSON>ere<PERSON>',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('concesionaria', models.ForeignKey(related_name='gerentes', on_delete=django.db.models.deletion.SET_NULL, blank=True, to='concesionarias.Concesionaria', null=True)),
                ('user', models.OneToOneField(related_name='gerente', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'gerente',
                'verbose_name_plural': 'gerentes',
            },
            bases=(models.Model,),
        ),
    ]
