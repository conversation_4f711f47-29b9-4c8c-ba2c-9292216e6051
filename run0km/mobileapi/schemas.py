# coding=utf-8
# http://cswr.github.io/JsonSchema/spec/multiple_types/
# https://spacetelescope.github.io/understanding-json-schema/structuring.html


def _base(model_id_name):
    return {
        "name": "Sincronizacion-prospectos-base",
        "definitions": {
            "command": {
                "type": "object",
                "properties": {
                    "operation": {"type": "string"},
                    "arguments": {
                        "type": "object",
                    },
                },
                "required": ["operation", "arguments"],
            }
        },
        "properties": {
            "sync_sequence_number": {"type": "number", "minimum": 0},
            "changes": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        model_id_name: {
                            "type": "number",
                            "minimum": 0
                        },
                        "actions": {
                            "type": "array",
                            "items": {"$ref": "#/definitions/command"},
                        },
                    },
                    "required": [model_id_name, "actions"],
                },
            },
        },
        "required": ["sync_sequence_number", "changes"],
    }


prospects_base = _base("id")
conversations_base = _base("id")

definitions = {
    "date": {
        "type": "string",
        # "format": "%Y-%m-%d",
        "format": "date",
        # "pattern": "[0-9]{4}-[0-9]{2}-[0-9]{2}$"
    },
    "datetime": {
        "type": "string",
        "format": "date-time",
        # "format": "%Y-%m-%d-%H:%M:%S",
        # "pattern": "[0-9]{4}-[0-9]{2}-[0-9]{2}-[0-9]{2}:[0-9]{2}:[0-9]{2}$"
    },
    "comment": {
        "type": "object",
        "properties": {
            "is_automatic": {"type": "boolean"},
            "text": {"type": "string", },
            "date": {"$ref": "#/definitions/datetime"},
        },
        "required": ["is_automatic", "text", "date"],
    },
    "call": {
        "type": "object",
        "properties": {
            "duration": {"type": ["number", "null"], "minimum": 0},
            "start_time": {"$ref": "#/definitions/datetime"},
        },
        "required": ["start_time"],
    },
    "answered_question": {
        "type": "object",
        "properties": {
            "question": {"type": "string", },
            "answer": {"type": "string", },
        },
        "required": ["question", "answer"],
    },
    "programmed_call": {
        "type": "object",
        "properties": {
            "date": {"$ref": "#/definitions/datetime"},
        },
        "required": ["date"],
    },
    "sale": {
        "type": "object",
        "properties": {
            "brand": {"type": "string", },
            "model": {"type": "string", },
            "price": {"type": "string", },
            "contract_number": {"type": "string"},
            "date": {"$ref": "#/definitions/date"},
        },
        "required": ["brand", "model", "price", "date"],
    },
    "whatsapp_message": {
        "type": "object",
        "properties": {
            "text": {"type": "string", },
            "date": {"$ref": "#/definitions/datetime"},
        },
        "required": ["text", "date"],
    },
}

arguments_add_made_call = {
    "name": "arguments_add_made_call",
    "definitions": definitions,
    "properties": {
        "call": {"$ref": "#/definitions/call"},
    },
    "required": ["call"],
}

arguments_send_proposal = {
    "name": "arguments_send_proposal",
    "definitions": definitions,
    "properties": {
        "id": {"type": "number", },
    },
    "required": ["id"],
}

arguments_add_comment = {
    "name": "arguments_add_comment",
    "definitions": definitions,
    "properties": {
        "comment": {"$ref": "#/definitions/comment"}
    },
    "required": ["comment"],
}

arguments_add_made_call_form = {
    "name": "arguments_add_made_call_form",
    "definitions": definitions,
    "properties": {
        "call": {"$ref": "#/definitions/call"},
        "answered_questions": {
            "type": "array",
            "items": {"$ref": "#/definitions/answered_question"},
        },
    },
    "required": ["call", "answered_questions"],
}

arguments_load_prospect_sale = {
    "name": "arguments_load_prospect_sale",
    "definitions": definitions,
    "properties": {
        "sale": {"$ref": "#/definitions/sale"}
    },
    "required": ["sale"],
}

arguments_end_prospect_tracking = {
    "name": "arguments_end_prospect_tracking",
    "definitions": definitions,
    "properties": {
        "reason": {"type": "string", },
        "other_reason_description": {"type": "string", },
        "comment": {"type": "string", },
    },
    # "required": ["reason"],
}

arguments_reactivate_prospect_tracking = {
    "name": "arguments_reactivate_prospect_tracking",
}

arguments_add_programmed_call = {
    "name": "arguments_add_programmed_call",
    "definitions": definitions,
    "properties": {
        "programmed_call": {"$ref": "#/definitions/programmed_call"}
    },
    "required": ["programmed_call"],
}

arguments_add_whatsapp_message_to_conversation = {
    "name": "arguments_add_whatsapp_message_to_conversation",
    "definitions": definitions,
    "properties": {
        "message": {"$ref": "#/definitions/whatsapp_message"}
    },
    "required": ["message"],
}

arguments_add_messages_to_prospect = {
    "name": "arguments_add_messages_to_prospect",
    "definitions": definitions,
    "properties": {
        "type": {"type": "string", },
        "messages": {
            "type": "array",
            "items": {"$ref": "#/definitions/whatsapp_message"}
        },
    },
    "required": ["type", "messages"],
}

arguments_mark_conversation_as_read = {
    "name": "arguments_mark_conversation_as_read",
}
