from django.conf import settings
from django.conf.urls import include, url
# Comment/uncomment the next two lines to disable/enable the admin:
from django.conf.urls.static import static
from django.contrib import admin
from django.contrib.auth import views as auth_views

from core.views import TerminosYCondicionesView
from occ.views import OCCVerificacionGoogle
from prospectos.views.resumen import ResumenView, HomeRedirectView
from users import views as user_views
from users.forms import AuthenticationForm

admin.autodiscover()
urlpatterns = [
                  # Comment/uncomment the next line to disable/enable the admin:
                  url(r'^admin/', include(admin.site.urls)),
                  # Login / Logout
                  url(r'^login/$', auth_views.login,
                      {'template_name': 'home.html', 'authentication_form': AuthenticationForm},
                      name='login'),
                  url(r'^logout/$', auth_views.logout_then_login, name='logout'),

                  url(r'^reset/1/$', user_views.password_reset, name='password_reset'),
                  url(r'^reset/2/$', auth_views.password_reset_done, name='password_reset_done'),
                  url(r'^reset/3/(?P<uidb64>[0-9A-Za-z]+)-(?P<token>.+)/$', auth_views.password_reset_confirm,
                      name='password_reset_confirm'),
                  url(r'^reset/4/$', auth_views.password_reset_complete, name='password_reset_complete'),

                  url(r'^prospectos/', include('prospectos.urls')),

                  url(r'^api/', include('api.urls')),
                  url(r'^mobile/', include('mobileapi.urls')),

                  url(r'^campanias/', include('campanias.urls')),
                  url(r'^alertas/', include('alertas.urls')),
                  url(r'^equipos/', include('equipos.urls')),
                  url(r'^vendedores/', include('vendedores.urls')),
                  url(r'^objetivos/', include('objetivos.urls')),
                  url(r'^conversaciones/', include('conversaciones.urls')),
                  url(r'^occ/', include('occ.urls')),
                  url(r'^reportes/', include('reportes.urls')),
                  url(r'^propuestas/', include('propuestas.urls')),

                  url(r'^personificar/$', user_views.personificar, name='personificar'),
                  url(r'^dejar-de-personificar/$', user_views.dejar_de_personificar, name='dejar-de-personificar'),
                  url(r'^google07c49a59444306f6', OCCVerificacionGoogle.as_view(), name='google-verificacion'),
                  url(r'^terms-and-conditions/$', TerminosYCondicionesView.as_view(), name='terminos-y-condiciones'),
                  # Examples:
                  # url(r'^$', 'run0km.views.home', name='home'),

                  # url(r'^run0km/', include('run0km.foo.urls')),

                  # Uncomment the admin/doc line below to enable admin documentation:
                  # url(r'^admin/doc/', include('django.contrib.admindocs.urls')),
                  #    url(r'^$', TemplateView.as_view(template_name="home.html"), name='home'),
                  url(r'^resumen/$', ResumenView.as_view(), name='resumen'),
                  url(r'^$', HomeRedirectView.as_view(), name='home_redirect'),
              ] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

if settings.DEBUG:
    import debug_toolbar

    urlpatterns += [url(r'^__debug__/', include(debug_toolbar.urls))]
