!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.stream=e():t.stream=e()}(this,function(){return function(t){function e(i){if(n[i])return n[i].exports;var r=n[i]={exports:{},id:i,loaded:!1};return t[i].call(r.exports,r,r.exports,e),r.loaded=!0,r.exports}var n={};return e.m=t,e.c=n,e.p="dist/",e(0)}([function(t,e,n){(function(e){"use strict";function i(t,n,i,s){if("undefined"!=typeof e&&e.env.STREAM_URL&&!t){var o=/https\:\/\/(\w+)\:(\w+)\@([\w-]*).*\?app_id=(\d+)/.exec(e.env.STREAM_URL);t=o[1],n=o[2];var a=o[3];i=o[4],void 0===s&&(s={}),"getstream"!==a&&(s.location=a)}return new r(t,n,i,s)}var r=n(2),s=n(5),o=n(3);t.exports.connect=i,t.exports.errors=s,t.exports.request=o,t.exports.Client=r}).call(e,n(1))},function(t){function e(){h&&c&&(h=!1,c.length?u=c.concat(u):l=-1,u.length&&n())}function n(){if(!h){var t=s(e);h=!0;for(var n=u.length;n;){for(c=u,u=[];++l<n;)c&&c[l].run();l=-1,n=u.length}c=null,h=!1,o(t)}}function i(t,e){this.fun=t,this.array=e}function r(){}var s,o,a=t.exports={};!function(){try{s=setTimeout}catch(t){s=function(){throw new Error("setTimeout is not defined")}}try{o=clearTimeout}catch(t){o=function(){throw new Error("clearTimeout is not defined")}}}();var c,u=[],h=!1,l=-1;a.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];u.push(new i(t,e)),1!==u.length||h||s(n,0)},i.prototype.run=function(){this.fun.apply(null,this.array)},a.title="browser",a.browser=!0,a.env={},a.argv=[],a.version="",a.versions={},a.on=r,a.addListener=r,a.once=r,a.off=r,a.removeListener=r,a.removeAllListeners=r,a.emit=r,a.binding=function(){throw new Error("process.binding is not supported")},a.cwd=function(){return"/"},a.chdir=function(){throw new Error("process.chdir is not supported")},a.umask=function(){return 0}},function(t,e,n){(function(e){"use strict";var i=n(3),r=n(4),s=n(7),o=n(5),a=n(6),c=n(9),u=n(11),h=n(9),l=n(15),f=n(16),d=function(){this.initialize.apply(this,arguments)};if(d.prototype={baseUrl:"https://api.getstream.io/api/",baseAnalyticsUrl:"https://analytics.getstream.io/analytics/",initialize:function(t,n,i,r){if(this.apiKey=t,this.apiSecret=n,this.appId=i,this.options=r||{},this.version=this.options.version||"v1.0",this.fayeUrl=this.options.fayeUrl||"https://faye.getstream.io/faye",this.fayeClient=null,this.group=this.options.group||"unspecified",this.subscriptions={},this.expireTokens=this.options.expireTokens?this.options.expireTokens:!1,this.location=this.options.location,this.location&&(this.baseUrl="https://"+this.location+"-api.getstream.io/api/"),"undefined"!=typeof e&&e.env.LOCAL&&(this.baseUrl="http://localhost:8000/api/"),"undefined"!=typeof e&&e.env.LOCAL_FAYE&&(this.fayeUrl="http://localhost:9999/faye/"),"undefined"!=typeof e&&e.env.STREAM_BASE_URL&&(this.baseUrl=e.env.STREAM_BASE_URL),this.handlers={},this.browser="undefined"!=typeof window,this.node=!this.browser,this.browser&&this.apiSecret)throw new o.FeedError("You are publicly sharing your private key. Dont use the private key while in the browser.")},on:function(t,e){this.handlers[t]=e},off:function(t){void 0===t?this.handlers={}:delete this.handlers[t]},send:function(){var t=Array.prototype.slice.call(arguments),e=t[0];t=t.slice(1),this.handlers[e]&&this.handlers[e].apply(this,t)},wrapPromiseTask:function(t,e,n){var i=this,r=this.wrapCallback(t);return function(t,s,o){t?n({error:t,response:s}):/^2/.test(""+s.statusCode)?e(o):n({error:o,response:s}),r.apply(i,arguments)}},wrapCallback:function(t){function e(){var e=Array.prototype.slice.call(arguments),i=["response"].concat(e);n.send.apply(n,i),void 0!==t&&t.apply(n,e)}var n=this;return e},userAgent:function(){var t=this.node?"node":"browser",e="unknown";return"stream-javascript-client-"+t+"-"+e},getReadOnlyToken:function(t,e){return this.feed(t,e).getReadOnlyToken()},getReadWriteToken:function(t,e){return this.feed(t,e).getReadWriteToken()},feed:function v(t,e,n,i,c){if(c=c||{},!t||!e)throw new o.FeedError('Please provide a feed slug and user id, ie client.feed("user", "1")');if(-1!==t.indexOf(":"))throw new o.FeedError('Please initialize the feed using client.feed("user", "1") not client.feed("user:1")');if(a.validateFeedSlug(t),a.validateUserId(e),!this.apiSecret&&!n)throw new o.FeedError("Missing token, in client side mode please provide a feed secret");if(this.apiSecret&&!n){var u=""+t+e;n=c.readOnly?this.getReadOnlyToken(t,e):s.sign(this.apiSecret,u)}var v=new r(this,t,e,n,i);return v},enrichUrl:function(t){var e=this.baseUrl+this.version+"/"+t;return e},enrichKwargs:function(t){t.url=this.enrichUrl(t.url),void 0===t.qs&&(t.qs={}),t.qs.api_key=this.apiKey,t.qs.location=this.group,t.json=!0;var e=t.signature||this.signature;return t.headers={},s.isJWTSignature(e)?(t.headers["stream-auth-type"]="jwt",e=e.split(" ").reverse()[0]):t.headers["stream-auth-type"]="simple",t.headers.Authorization=e,t.headers["X-Stream-Client"]=this.userAgent(),t},signActivity:function(t){return this.signActivities([t])[0]},signActivities:function(t){if(!this.apiSecret)return t;for(var e=0;e<t.length;e++){for(var n=t[e],i=n.to||[],r=[],s=0;s<i.length;s++){var o=i[s],a=o.split(":")[0],c=o.split(":")[1],u=this.feed(a,c).token,h=o+" "+u;r.push(h)}n.to=r}return t},getFayeAuthorization:function(){var t=this.apiKey,e=this;return{incoming:function(t,e){e(t)},outgoing:function(n,i){if(n.subscription&&e.subscriptions[n.subscription]){var r=e.subscriptions[n.subscription];n.ext={user_id:r.userId,api_key:t,signature:r.token}}i(n)}}},getFayeClient:function(){if(null===this.fayeClient){this.fayeClient=new f.Client(this.fayeUrl);var t=this.getFayeAuthorization();this.fayeClient.addExtension(t)}return this.fayeClient},get:function(t,e){return new u(function(n,r){this.send("request","get",t,e),t=this.enrichKwargs(t),t.method="GET";var s=this.wrapPromiseTask(e,n,r);i(t,s)}.bind(this))},post:function(t,e){return new u(function(n,r){this.send("request","post",t,e),t=this.enrichKwargs(t),t.method="POST";var s=this.wrapPromiseTask(e,n,r);i(t,s)}.bind(this))},"delete":function(t,e){return new u(function(n,r){this.send("request","delete",t,e),t=this.enrichKwargs(t),t.method="DELETE";var s=this.wrapPromiseTask(e,n,r);i(t,s)}.bind(this))},updateActivities:function(t,e){if(!(t instanceof Array))throw new TypeError("The activities argument should be an Array");var n=s.JWTScopeToken(this.apiSecret,"activities","*",{feedId:"*",expireTokens:this.expireTokens}),i={activities:t};return this.post({url:"activities/",body:i,signature:n},e)},updateActivity:function(t){return this.updateActivities([t])}},h&&(d.prototype.createRedirectUrl=function(t,e,n){var i=l.parse(t);if(!(i.host||i.hostname&&i.port||i.isUnix))throw new o.MissingSchemaError('Invalid URI: "'+l.format(i)+'"');var r=s.JWTScopeToken(this.apiSecret,"redirect_and_track","*",{userId:e,expireTokens:this.expireTokens}),c=this.baseAnalyticsUrl+"redirect/",u={auth_type:"jwt",authorization:r,url:t,api_key:this.apiKey,events:JSON.stringify(n)},f=a.rfc3986(h.stringify(u,null,null,{}));return c+"?"+f}),c)for(var p in c)c.hasOwnProperty(p)&&(d.prototype[p]=c[p]);t.exports=d}).call(e,n(1))},function(t){function e(t,s){if("function"!=typeof s)throw new Error("Bad callback given: "+s);if(!t)throw new Error("No options given");var o=t.onResponse;if(t="string"==typeof t?{uri:t}:JSON.parse(JSON.stringify(t)),t.onResponse=o,t.verbose&&(e.log=r()),t.url&&(t.uri=t.url,delete t.url),!t.uri&&""!==t.uri)throw new Error("options.uri is a required argument");if("string"!=typeof t.uri)throw new Error("options.uri must be a string");for(var c=["proxy","_redirectsFollowed","maxRedirects","followRedirect"],u=0;u<c.length;u++)if(t[c[u]])throw new Error("options."+c[u]+" is not supported");if(t.callback=s,t.method=t.method||"GET",t.headers=t.headers||{},t.body=t.body||null,t.timeout=t.timeout||e.DEFAULT_TIMEOUT,t.headers.host)throw new Error("Options.headers.host is not supported");t.json&&(t.headers.accept=t.headers.accept||"application/json","GET"!==t.method&&(t.headers["content-type"]="application/json"),"boolean"!=typeof t.json?t.body=JSON.stringify(t.json):"string"!=typeof t.body&&null!==t.body&&(t.body=JSON.stringify(t.body)));var h=function(t){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));return e.join("&")};if(t.qs){var l="string"==typeof t.qs?t.qs:h(t.qs);t.uri=-1!==t.uri.indexOf("?")?t.uri+"&"+l:t.uri+"?"+l}var f=function(t){var e={};e.boundry="-------------------------------"+Math.floor(1e9*Math.random());var n=[];for(var i in t)t.hasOwnProperty(i)&&n.push("--"+e.boundry+'\nContent-Disposition: form-data; name="'+i+'"\n\n'+t[i]+"\n");return n.push("--"+e.boundry+"--"),e.body=n.join(""),e.length=e.body.length,e.type="multipart/form-data; boundary="+e.boundry,e};if(t.form){if("string"==typeof t.form)throw"form name unsupported";if("POST"===t.method){var d=(t.encoding||"application/x-www-form-urlencoded").toLowerCase();switch(t.headers["content-type"]=d,d){case"application/x-www-form-urlencoded":t.body=h(t.form).replace(/%20/g,"+");break;case"multipart/form-data":var p=f(t.form);t.body=p.body,t.headers["content-type"]=p.type;break;default:throw new Error("unsupported encoding:"+d)}}}return t.onResponse=t.onResponse||i,t.onResponse===!0&&(t.onResponse=s,t.callback=i),!t.headers.authorization&&t.auth&&(t.headers.authorization="Basic "+a(t.auth.username+":"+t.auth.password)),n(t)}function n(t){function n(){l=!0;var n=new Error("ETIMEDOUT");return n.code="ETIMEDOUT",n.duration=t.timeout,e.log.error("Timeout",{id:u._id,milliseconds:t.timeout}),t.callback(n,u)}function i(){return l?e.log.debug("Ignoring timed out state change",{state:u.readyState,id:u.id}):(e.log.debug("State change",{state:u.readyState,id:u.id,timed_out:l}),1===u.readyState?e.log.debug("Request started",{id:u.id}):2===u.readyState?r():3===u.readyState?(r(),s()):4===u.readyState&&(r(),s(),a()),void 0)}function r(){if(!v.response){if(v.response=!0,e.log.debug("Got response",{id:u.id,status:u.status}),clearTimeout(u.timeoutTimer),u.statusCode=u.status,f&&0==u.statusCode){var n=new Error("CORS request rejected: "+t.uri);return n.cors="rejected",v.loading=!0,v.end=!0,t.callback(n,u)}t.onResponse(null,u)}}function s(){v.loading||(v.loading=!0,e.log.debug("Response body loading",{id:u.id}))}function a(){if(!v.end){if(v.end=!0,e.log.debug("Request done",{id:u.id}),u.body=u.responseText,t.json)try{u.body=JSON.parse(u.responseText)}catch(n){return t.callback(n,u)}t.callback(null,u,u.body)}}var u=new c,l=!1,f=o(t.uri),d="withCredentials"in u;if(h+=1,u.seq_id=h,u.id=h+": "+t.method+" "+t.uri,u._id=u.id,f&&!d){var p=new Error("Browser does not support cross-origin request: "+t.uri);return p.cors="unsupported",t.callback(p,u)}u.timeoutTimer=setTimeout(n,t.timeout);var v={response:!1,loading:!1,end:!1};u.onreadystatechange=i,u.open(t.method,t.uri,!0),f&&(u.withCredentials=!!t.withCredentials);for(var _ in t.headers)u.setRequestHeader(_,t.headers[_]);return u.send(t.body),u}function i(){}function r(){var t,e,n={},r=["trace","debug","info","warn","error"];for(e=0;e<r.length;e++)t=r[e],n[t]=i,"undefined"!=typeof console&&console&&console[t]&&(n[t]=s(console,t));return n}function s(t,e){function n(n,i){return"object"==typeof i&&(n+=" "+JSON.stringify(i)),t[e].call(t,n)}return n}function o(t){if(!window.location)return!1;var e,n=/^([\w\+\.\-]+:)(?:\/\/([^\/?#:]*)(?::(\d+))?)?/;try{e=location.href}catch(i){e=document.createElement("a"),e.href="",e=e.href}var r=n.exec(e.toLowerCase())||[],s=n.exec(t.toLowerCase()),o=!(!s||s[1]==r[1]&&s[2]==r[2]&&(s[3]||("http:"===s[1]?80:443))==(r[3]||("http:"===r[1]?80:443)));return o}function a(t){var e,n,i,r,s,o,a,c,u="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",h=0,l=0,f="",d=[];if(!t)return t;do e=t.charCodeAt(h++),n=t.charCodeAt(h++),i=t.charCodeAt(h++),c=e<<16|n<<8|i,r=c>>18&63,s=c>>12&63,o=c>>6&63,a=63&c,d[l++]=u.charAt(r)+u.charAt(s)+u.charAt(o)+u.charAt(a);while(h<t.length);switch(f=d.join(""),t.length%3){case 1:f=f.slice(0,-2)+"==";break;case 2:f=f.slice(0,-1)+"="}return f}var c=XMLHttpRequest;if(!c)throw new Error("missing XMLHttpRequest");e.log={trace:i,debug:i,info:i,warn:i,error:i};var u=18e4,h=0;e.withCredentials=!1,e.DEFAULT_TIMEOUT=u,e.defaults=function(t){var n=function(e){var n=function(n,i){n="string"==typeof n?{uri:n}:JSON.parse(JSON.stringify(n));for(var r in t)void 0===n[r]&&(n[r]=t[r]);return e(n,i)};return n},i=n(e);return i.get=n(e.get),i.post=n(e.post),i.put=n(e.put),i.head=n(e.head),i};var l=["get","put","post","head"];l.forEach(function(t){var n=t.toUpperCase(),i=t.toLowerCase();e[i]=function(t){"string"==typeof t?t={method:n,uri:t}:(t=JSON.parse(JSON.stringify(t)),t.method=n);var i=[t].concat(Array.prototype.slice.apply(arguments,[1]));return e.apply(this,i)}}),e.couch=function(t,n){function r(t,e,i){if(t)return n(t,e,i);if((e.statusCode<200||e.statusCode>299)&&i.error){t=new Error("CouchDB error: "+(i.error.reason||i.error.error));for(var r in i)t[r]=i[r];return n(t,e,i)}return n(t,e,i)}"string"==typeof t&&(t={uri:t}),t.json=!0,t.body&&(t.json=t.body),delete t.body,n=n||i;var s=e(t,r);return s},t.exports=e},function(t,e,n){"use strict";var i=n(5),r=n(6),s=n(7),o=function(){this.initialize.apply(this,arguments)};o.prototype={initialize:function(t,e,n,i){this.client=t,this.slug=e,this.userId=n,this.id=this.slug+":"+this.userId,this.token=i,this.feedUrl=this.id.replace(":","/"),this.feedTogether=this.id.replace(":",""),this.signature=this.feedTogether+" "+this.token,this.notificationChannel="site-"+this.client.appId+"-feed-"+this.feedTogether},addActivity:function(t,e){return t=this.client.signActivity(t),this.client.post({url:"feed/"+this.feedUrl+"/",body:t,signature:this.signature},e)},removeActivity:function(t,e){var n=t.foreignId?t.foreignId:t,i={};return t.foreignId&&(i.foreign_id="1"),this.client["delete"]({url:"feed/"+this.feedUrl+"/"+n+"/",qs:i,signature:this.signature},e)},addActivities:function(t,e){t=this.client.signActivities(t);var n={activities:t},i=this.client.post({url:"feed/"+this.feedUrl+"/",body:n,signature:this.signature},e);return i},follow:function(t,e,n,i){r.validateFeedSlug(t),r.validateUserId(e);var s,o=arguments[arguments.length-1];i=o.call?o:void 0;var a=t+":"+e;n&&!n.call&&"undefined"!=typeof n.limit&&null!==n.limit&&(s=n.limit);var c={target:a};return"undefined"!=typeof s&&null!==s&&(c.activity_copy_limit=s),this.client.post({url:"feed/"+this.feedUrl+"/following/",body:c,signature:this.signature},i)},unfollow:function(t,e,n,i){var s={},o={};"function"==typeof n&&(i=n),"object"==typeof n&&(s=n),"boolean"==typeof s.keepHistory&&s.keepHistory&&(o.keep_history="1"),r.validateFeedSlug(t),r.validateUserId(e);var a=t+":"+e,c=this.client["delete"]({url:"feed/"+this.feedUrl+"/following/"+a+"/",qs:o,signature:this.signature},i);return c},following:function(t,e){return void 0!==t&&t.filter&&(t.filter=t.filter.join(",")),this.client.get({url:"feed/"+this.feedUrl+"/following/",qs:t,signature:this.signature},e)},followers:function(t,e){return void 0!==t&&t.filter&&(t.filter=t.filter.join(",")),this.client.get({url:"feed/"+this.feedUrl+"/followers/",qs:t,signature:this.signature},e)},get:function(t,e){return t&&t.mark_read&&t.mark_read.join&&(t.mark_read=t.mark_read.join(",")),t&&t.mark_seen&&t.mark_seen.join&&(t.mark_seen=t.mark_seen.join(",")),this.client.get({url:"feed/"+this.feedUrl+"/",qs:t,signature:this.signature},e)},getFayeClient:function(){return this.client.getFayeClient()},subscribe:function(t){if(!this.client.appId)throw new i.SiteError("Missing app id, which is needed to subscribe, use var client = stream.connect(key, secret, appId);");return this.client.subscriptions["/"+this.notificationChannel]={token:this.token,userId:this.notificationChannel},this.getFayeClient().subscribe("/"+this.notificationChannel,t)},getReadOnlyToken:function(){var t=""+this.slug+this.userId;return s.JWTScopeToken(this.client.apiSecret,"*","read",{feedId:t,expireTokens:this.client.expireTokens})},getReadWriteToken:function(){var t=""+this.slug+this.userId;return s.JWTScopeToken(this.client.apiSecret,"*","*",{feedId:t,expireTokens:this.client.expireTokens})}},t.exports=o},function(t){"use strict";function e(t,e){this.message=t,Error.call(this,this.message),i?Error.captureStackTrace(this,e):this.stack=r?(new Error).stack:""}var n=t.exports,i="function"==typeof Error.captureStackTrace,r=!!(new Error).stack;n._Abstract=e,e.prototype=new Error,n.FeedError=function(t){e.call(this,t)},n.FeedError.prototype=new e,n.SiteError=function(t){e.call(this,t)},n.SiteError.prototype=new e,n.MissingSchemaError=function(t){e.call(this,t)},n.MissingSchemaError.prototype=new e},function(t,e,n){"use strict";function i(t){var e=t.split(":");if(2!==e.length)throw new a.FeedError("Invalid feedId, expected something like user:1 got "+t);var n=e[0],i=e[1];return r(n),s(i),t}function r(t){var e=c.test(t);if(!e)throw new a.FeedError("Invalid feedSlug, please use letters, numbers or _ got: "+t);return t}function s(t){var e=c.test(t);if(!e)throw new a.FeedError("Invalid feedSlug, please use letters, numbers or _ got: "+t);return t}function o(t){return t.replace(/[!'()*]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}var a=n(5),c=/^[\w-]+$/;e.validateFeedId=i,e.validateFeedSlug=r,e.validateUserId=s,e.rfc3986=o},function(t,e,n){"use strict";function i(t){var e=t.replace(/\+/g,"-").replace(/\//g,"_");return e.replace(/^=+/,"").replace(/=+$/,"")}function r(t){try{return f.atob(a(t))}catch(e){if("InvalidCharacterError"===e.name)return void 0;throw e}}function s(t){if("object"==typeof t)return t;try{return JSON.parse(t)}catch(e){return void 0}}function o(t){var e=4,n=t.length%e;if(!n)return t;for(var i=e-n;i--;)t+="=";return t}function a(t){var e=o(t).replace(/\-/g,"+").replace(/_/g,"/");return e}function c(t){var e=t.split(".",1)[0];return s(r(e))}var u=n(8),h=n(9),l=/^[a-zA-Z0-9\-_]+?\.[a-zA-Z0-9\-_]+?\.([a-zA-Z0-9\-_]+)?$/,f=n(10);e.headerFromJWS=c,e.sign=function(t,e){var n=new u.createHash("sha1").update(t).digest(),r=u.createHmac("sha1",n),s=r.update(e).digest("base64"),o=i(s);return o},e.JWTScopeToken=function(t,e,n,i){var r=i||{},s=r.expireTokens?!r.expireTokens:!0,o={resource:e,action:n};r.feedId&&(o.feed_id=r.feedId),r.userId&&(o.user_id=r.userId);var a=h.sign(o,t,{algorithm:"HS256",noTimestamp:s});return a},e.isJWTSignature=function(t){var e=t.split(" ")[1]||t;return l.test(e)&&!!c(e)}},function(){},function(){"use strict"},function(t,e){!function(){function t(t){this.message=t}var n=e,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";t.prototype=new Error,t.prototype.name="InvalidCharacterError",n.btoa||(n.btoa=function(e){for(var n,r,s=String(e),o=0,a=i,c="";s.charAt(0|o)||(a="=",o%1);c+=a.charAt(63&n>>8-o%1*8)){if(r=s.charCodeAt(o+=.75),r>255)throw new t("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");n=n<<8|r}return c}),n.atob||(n.atob=function(e){var n=String(e).replace(/=+$/,"");if(n.length%4==1)throw new t("'atob' failed: The string to be decoded is not correctly encoded.");for(var r,s,o=0,a=0,c="";s=n.charAt(a++);~s&&(r=o%4?64*r+s:s,o++%4)?c+=String.fromCharCode(255&r>>(-2*o&6)):0)s=i.indexOf(s);return c})}()},function(t,e,n){"use strict";var i=n(12);t.exports=i},function(t,e,n){"use strict";var i=n(13),r=0,s=1,o=2,a=function(t){return t},c=function(t){throw t},u=function(t){if(this._state=r,this._onFulfilled=[],this._onRejected=[],"function"==typeof t){var e=this;t(function(t){p(e,t)},function(t){_(e,t)})}};u.prototype.then=function(t,e){var n=new u;return h(this,t,n),l(this,e,n),n},u.prototype.catch=function(t){return this.then(null,t)};var h=function(t,e,n){"function"!=typeof e&&(e=a);var i=function(t){f(e,t,n)};t._state===r?t._onFulfilled.push(i):t._state===s&&i(t._value)},l=function(t,e,n){"function"!=typeof e&&(e=c);var i=function(t){f(e,t,n)};t._state===r?t._onRejected.push(i):t._state===o&&i(t._reason)},f=function(t,e,n){i(function(){d(t,e,n)})},d=function(t,e,n){var i;try{i=t(e)}catch(r){return _(n,r)}i===n?_(n,new TypeError("Recursive promise chain detected")):p(n,i)},p=function(t,e){var n,i,r=!1;try{if(n=typeof e,i=null!==e&&("function"===n||"object"===n)&&e.then,"function"!=typeof i)return v(t,e);i.call(e,function(e){r^(r=!0)&&p(t,e)},function(e){r^(r=!0)&&_(t,e)})}catch(s){if(!(r^(r=!0)))return;_(t,s)}},v=function(t,e){if(t._state===r){t._state=s,t._value=e,t._onRejected=[];for(var n,i=t._onFulfilled;n=i.shift();)n(e)}},_=function(t,e){if(t._state===r){t._state=o,t._reason=e,t._onFulfilled=[];for(var n,i=t._onRejected;n=i.shift();)n(e)}};u.resolve=u.accept=u.fulfill=function(t){return new u(function(e){e(t)})},u.reject=function(t){return new u(function(e,n){n(t)})},u.all=function(t){return new u(function(e,n){var i,r=[],s=t.length;if(0===s)return e(r);for(i=0;s>i;i++)(function(t,i){u.resolve(t).then(function(t){r[i]=t,0===--s&&e(r)},n)})(t[i],i)})},u.race=function(t){return new u(function(e,n){for(var i=0,r=t.length;r>i;i++)u.resolve(t[i]).then(e,n)})},u.deferred=u.pending=function(){var t={};return t.promise=new u(function(e,n){t.fulfill=t.resolve=e,t.reject=n}),t},t.exports=u},function(t,e,n){"use strict";function i(){if(c.length)throw c.shift()}function r(t){var e;e=a.length?a.pop():new s,e.task=t,o(e)}function s(){this.task=null}var o=n(14),a=[],c=[],u=o.makeRequestCallFromTimer(i);t.exports=r,s.prototype.call=function(){try{this.task.call()}catch(t){r.onerror?r.onerror(t):(c.push(t),u())}finally{this.task=null,a[a.length]=this}}},function(t,e){(function(e){"use strict";function n(t){a.length||(o(),c=!0),a[a.length]=t}function i(){for(;u<a.length;){var t=u;if(u+=1,a[t].call(),u>h){for(var e=0,n=a.length-u;n>e;e++)a[e]=a[e+u];a.length-=u,u=0}}a.length=0,u=0,c=!1}function r(t){var e=1,n=new l(t),i=document.createTextNode("");return n.observe(i,{characterData:!0}),function(){e=-e,i.data=e}}function s(t){return function(){function e(){clearTimeout(n),clearInterval(i),t()}var n=setTimeout(e,0),i=setInterval(e,50)}}t.exports=n;var o,a=[],c=!1,u=0,h=1024,l=e.MutationObserver||e.WebKitMutationObserver;o="function"==typeof l?r(i):s(i),n.requestFlush=o,n.makeRequestCallFromTimer=s}).call(e,function(){return this}())},function(){},function(t,e,n){"use strict";var i=n(17),r=n(18),s={VERSION:i.VERSION,Client:n(20),Scheduler:n(45)};r.wrapper=s,t.exports=s},function(t){t.exports={VERSION:"1.2.0",BAYEUX_VERSION:"1.0",ID_LENGTH:160,JSONP_CALLBACK:"jsonpcallback",CONNECTION_TYPES:["long-polling","cross-origin-long-polling","callback-polling","websocket","eventsource","in-process"],MANDATORY_CONNECTION_TYPES:["long-polling","callback-polling","in-process"]}},function(t,e,n){"use strict";var i=n(19),r={LOG_LEVELS:{fatal:4,error:3,warn:2,info:1,debug:0},writeLog:function(t,e){var n=r.logger||(r.wrapper||r).logger;if(n){var s=Array.prototype.slice.apply(t),o="[Faye",a=this.className,c=s.shift().replace(/\?/g,function(){try{return i(s.shift())}catch(t){return"[Object]"}});a&&(o+="."+a),o+="] ","function"==typeof n[e]?n[e](o+c):"function"==typeof n&&n(o+c)}}};for(var s in r.LOG_LEVELS)(function(t){r[t]=function(){this.writeLog(arguments,t)}})(s);t.exports=r},function(t){"use strict";t.exports=function(t){return JSON.stringify(t,function(t,e){return this[t]instanceof Array?this[t]:e})}},function(t,e,n){(function(e){"use strict";var i=n(13),r=n(21),s=(n(12),n(23)),o=n(24),a=n(25),c=n(17),u=n(22),h=n(26),l=n(27),f=n(18),d=n(28),p=n(30),v=n(32),_=n(46),g=n(47),m=n(48),y=n(49),b=r({className:"Client",UNCONNECTED:1,CONNECTING:2,CONNECTED:3,DISCONNECTED:4,HANDSHAKE:"handshake",RETRY:"retry",NONE:"none",CONNECTION_TIMEOUT:60,DEFAULT_ENDPOINT:"/bayeux",INTERVAL:0,initialize:function(t,n){this.info("New client created for ?",t),n=n||{},h(n,["interval","timeout","endpoints","proxy","retry","scheduler","websocketExtensions","tls","ca"]),this._channels=new p.Set,this._dispatcher=v.create(this,t||this.DEFAULT_ENDPOINT,n),this._messageId=0,this._state=this.UNCONNECTED,this._responseCallbacks={},this._advice={reconnect:this.RETRY,interval:1e3*(n.interval||this.INTERVAL),timeout:1e3*(n.timeout||this.CONNECTION_TIMEOUT)},this._dispatcher.timeout=this._advice.timeout/1e3,this._dispatcher.bind("message",this._receiveMessage,this),a.Event&&void 0!==e.onbeforeunload&&a.Event.on(e,"beforeunload",function(){o.indexOf(this._dispatcher._disabled,"autodisconnect")<0&&this.disconnect()},this)},addWebsocketExtension:function(t){return this._dispatcher.addWebsocketExtension(t)},disable:function(t){return this._dispatcher.disable(t)},setHeader:function(t,e){return this._dispatcher.setHeader(t,e)},handshake:function(t,n){if(this._advice.reconnect!==this.NONE&&this._state===this.UNCONNECTED){this._state=this.CONNECTING;var r=this;this.info("Initiating handshake with ?",s.stringify(this._dispatcher.endpoint)),this._dispatcher.selectTransport(c.MANDATORY_CONNECTION_TYPES),this._sendMessage({channel:p.HANDSHAKE,version:c.BAYEUX_VERSION,supportedConnectionTypes:this._dispatcher.getConnectionTypes()},{},function(s){s.successful?(this._state=this.CONNECTED,this._dispatcher.clientId=s.clientId,this._dispatcher.selectTransport(s.supportedConnectionTypes),this.info("Handshake successful: ?",this._dispatcher.clientId),this.subscribe(this._channels.getKeys(),!0),t&&i(function(){t.call(n)})):(this.info("Handshake unsuccessful"),e.setTimeout(function(){r.handshake(t,n)},1e3*this._dispatcher.retry),this._state=this.UNCONNECTED)},this)}},connect:function(t,e){if(this._advice.reconnect!==this.NONE&&this._state!==this.DISCONNECTED){if(this._state===this.UNCONNECTED)return this.handshake(function(){this.connect(t,e)},this);this.callback(t,e),this._state===this.CONNECTED&&(this.info("Calling deferred actions for ?",this._dispatcher.clientId),this.setDeferredStatus("succeeded"),this.setDeferredStatus("unknown"),this._connectRequest||(this._connectRequest=!0,this.info("Initiating connection for ?",this._dispatcher.clientId),this._sendMessage({channel:p.CONNECT,clientId:this._dispatcher.clientId,connectionType:this._dispatcher.connectionType},{},this._cycleConnection,this)))}},disconnect:function(){if(this._state===this.CONNECTED){this._state=this.DISCONNECTED,this.info("Disconnecting ?",this._dispatcher.clientId);var t=new m;return this._sendMessage({channel:p.DISCONNECT,clientId:this._dispatcher.clientId},{},function(e){e.successful?(this._dispatcher.close(),t.setDeferredStatus("succeeded")):t.setDeferredStatus("failed",_.parse(e.error))},this),this.info("Clearing channel listeners for ?",this._dispatcher.clientId),this._channels=new p.Set,t}},subscribe:function(t,e,n){if(t instanceof Array)return o.map(t,function(t){return this.subscribe(t,e,n)},this);var i=new y(this,t,e,n),r=e===!0,s=this._channels.hasSubscription(t);return s&&!r?(this._channels.subscribe([t],i),i.setDeferredStatus("succeeded"),i):(this.connect(function(){this.info("Client ? attempting to subscribe to ?",this._dispatcher.clientId,t),r||this._channels.subscribe([t],i),this._sendMessage({channel:p.SUBSCRIBE,clientId:this._dispatcher.clientId,subscription:t},{},function(e){if(!e.successful)return i.setDeferredStatus("failed",_.parse(e.error)),this._channels.unsubscribe(t,i);var n=[].concat(e.subscription);this.info("Subscription acknowledged for ? to ?",this._dispatcher.clientId,n),i.setDeferredStatus("succeeded")},this)},this),i)},unsubscribe:function(t,e){if(t instanceof Array)return o.map(t,function(t){return this.unsubscribe(t,e)},this);var n=this._channels.unsubscribe(t,e);n&&this.connect(function(){this.info("Client ? attempting to unsubscribe from ?",this._dispatcher.clientId,t),this._sendMessage({channel:p.UNSUBSCRIBE,clientId:this._dispatcher.clientId,subscription:t},{},function(t){if(t.successful){var e=[].concat(t.subscription);this.info("Unsubscription acknowledged for ? from ?",this._dispatcher.clientId,e)}},this)},this)},publish:function(t,e,n){h(n||{},["attempts","deadline"]);var i=new m;return this.connect(function(){this.info("Client ? queueing published message to ?: ?",this._dispatcher.clientId,t,e),this._sendMessage({channel:t,data:e,clientId:this._dispatcher.clientId},n,function(t){t.successful?i.setDeferredStatus("succeeded"):i.setDeferredStatus("failed",_.parse(t.error))},this)},this),i},_sendMessage:function(t,e,n,i){t.id=this._generateMessageId();var r=this._advice.timeout?1.2*this._advice.timeout/1e3:1.2*this._dispatcher.retry;this.pipeThroughExtensions("outgoing",t,null,function(t){t&&(n&&(this._responseCallbacks[t.id]=[n,i]),this._dispatcher.sendMessage(t,r,e||{}))},this)},_generateMessageId:function(){return this._messageId+=1,this._messageId>=Math.pow(2,32)&&(this._messageId=0),this._messageId.toString(36)},_receiveMessage:function(t){var e,n=t.id;void 0!==t.successful&&(e=this._responseCallbacks[n],delete this._responseCallbacks[n]),this.pipeThroughExtensions("incoming",t,null,function(t){t&&(t.advice&&this._handleAdvice(t.advice),this._deliverMessage(t),e&&e[0].call(e[1],t))},this)},_handleAdvice:function(t){u(this._advice,t),this._dispatcher.timeout=this._advice.timeout/1e3,this._advice.reconnect===this.HANDSHAKE&&this._state!==this.DISCONNECTED&&(this._state=this.UNCONNECTED,this._dispatcher.clientId=null,this._cycleConnection())},_deliverMessage:function(t){t.channel&&void 0!==t.data&&(this.info("Client ? calling listeners for ? with ?",this._dispatcher.clientId,t.channel,t.data),this._channels.distributeMessage(t))},_cycleConnection:function(){this._connectRequest&&(this._connectRequest=null,this.info("Closed connection for ?",this._dispatcher.clientId));var t=this;e.setTimeout(function(){t.connect()},this._advice.interval)}});u(b.prototype,l),u(b.prototype,d),u(b.prototype,f),u(b.prototype,g),t.exports=b}).call(e,function(){return this}())},function(t,e,n){"use strict";var i=n(22);t.exports=function(t,e){"function"!=typeof t&&(e=t,t=Object);var n=function(){return this.initialize?this.initialize.apply(this,arguments)||this:this},r=function(){};return r.prototype=t.prototype,n.prototype=new r,i(n.prototype,e),n}},function(t){"use strict";t.exports=function(t,e,n){if(!e)return t;for(var i in e)e.hasOwnProperty(i)&&(t.hasOwnProperty(i)&&n===!1||t[i]!==e[i]&&(t[i]=e[i]));return t}},function(t){"use strict";t.exports={isURI:function(t){return t&&t.protocol&&t.host&&t.path},isSameOrigin:function(t){return t.protocol===location.protocol&&t.hostname===location.hostname&&t.port===location.port},parse:function(t){if("string"!=typeof t)return t;var e,n,i,r,s,o,a={},c=function(e,n){t=t.replace(n,function(t){return a[e]=t,""}),a[e]=a[e]||""};for(c("protocol",/^[a-z]+\:/i),c("host",/^\/\/[^\/\?#]+/),/^\//.test(t)||a.host||(t=location.pathname.replace(/[^\/]*$/,"")+t),c("pathname",/^[^\?#]*/),c("search",/^\?[^#]*/),c("hash",/^#.*/),a.protocol=a.protocol||location.protocol,a.host?(a.host=a.host.substr(2),e=a.host.split(":"),a.hostname=e[0],a.port=e[1]||""):(a.host=location.host,a.hostname=location.hostname,a.port=location.port),a.pathname=a.pathname||"/",a.path=a.pathname+a.search,n=a.search.replace(/^\?/,""),i=n?n.split("&"):[],o={},r=0,s=i.length;s>r;r++)e=i[r].split("="),o[decodeURIComponent(e[0]||"")]=decodeURIComponent(e[1]||"");return a.query=o,a.href=this.stringify(a),a},stringify:function(t){var e=t.protocol+"//"+t.hostname;return t.port&&(e+=":"+t.port),e+=t.pathname+this.queryString(t.query)+(t.hash||"")},queryString:function(t){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));return 0===e.length?"":"?"+e.join("&")}}},function(t){"use strict";t.exports={commonElement:function(t,e){for(var n=0,i=t.length;i>n;n++)if(-1!==this.indexOf(e,t[n]))return t[n];return null},indexOf:function(t,e){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;i>n;n++)if(t[n]===e)return n;return-1},map:function(t,e,n){if(t.map)return t.map(e,n);var i=[];if(t instanceof Array)for(var r=0,s=t.length;s>r;r++)i.push(e.call(n||null,t[r],r));else for(var o in t)t.hasOwnProperty(o)&&i.push(e.call(n||null,o,t[o]));
return i},filter:function(t,e,n){if(t.filter)return t.filter(e,n);for(var i=[],r=0,s=t.length;s>r;r++)e.call(n||null,t[r],r)&&i.push(t[r]);return i},asyncEach:function(t,e,n,i){var r=t.length,s=-1,o=0,a=!1,c=function(){return o-=1,s+=1,s===r?n&&n.call(i):(e(t[s],h),void 0)},u=function(){if(!a){for(a=!0;o>0;)c();a=!1}},h=function(){o+=1,u()};h()}}},function(t,e){(function(e){"use strict";var n={_registry:[],on:function(t,e,n,i){var r=function(){n.call(i)};t.addEventListener?t.addEventListener(e,r,!1):t.attachEvent("on"+e,r),this._registry.push({_element:t,_type:e,_callback:n,_context:i,_handler:r})},detach:function(t,e,n,i){for(var r,s=this._registry.length;s--;)r=this._registry[s],t&&t!==r._element||e&&e!==r._type||n&&n!==r._callback||i&&i!==r._context||(r._element.removeEventListener?r._element.removeEventListener(r._type,r._handler,!1):r._element.detachEvent("on"+r._type,r._handler),this._registry.splice(s,1),r=null)}};void 0!==e.onunload&&n.on(e,"unload",n.detach,n),t.exports={Event:n}}).call(e,function(){return this}())},function(t,e,n){"use strict";var i=n(24);t.exports=function(t,e){for(var n in t)if(i.indexOf(e,n)<0)throw new Error("Unrecognized option: "+n)}},function(t,e,n){(function(e){"use strict";var i=n(12);t.exports={then:function(t,e){var n=this;return this._promise||(this._promise=new i(function(t,e){n._resolve=t,n._reject=e})),0===arguments.length?this._promise:this._promise.then(t,e)},callback:function(t,e){return this.then(function(n){t.call(e,n)})},errback:function(t,e){return this.then(null,function(n){t.call(e,n)})},timeout:function(t,n){this.then();var i=this;this._timer=e.setTimeout(function(){i._reject(n)},1e3*t)},setDeferredStatus:function(t,n){this._timer&&e.clearTimeout(this._timer),this.then(),"succeeded"===t?this._resolve(n):"failed"===t?this._reject(n):delete this._promise}}}).call(e,function(){return this}())},function(t,e,n){"use strict";var i=n(22),r=n(29),s={countListeners:function(t){return this.listeners(t).length},bind:function(t,e,n){var i=Array.prototype.slice,r=function(){e.apply(n,i.call(arguments))};return this._listeners=this._listeners||[],this._listeners.push([t,e,n,r]),this.on(t,r)},unbind:function(t,e,n){this._listeners=this._listeners||[];for(var i,r=this._listeners.length;r--;)i=this._listeners[r],i[0]===t&&(!e||i[1]===e&&i[2]===n)&&(this._listeners.splice(r,1),this.removeListener(t,i[3]))}};i(s,r.prototype),s.trigger=s.emit,t.exports=s},function(t){function e(t,e){if(t.indexOf)return t.indexOf(e);for(var n=0;n<t.length;n++)if(e===t[n])return n;return-1}function n(){}var i="function"==typeof Array.isArray?Array.isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)};t.exports=n,n.prototype.emit=function(t){if("error"===t&&(!this._events||!this._events.error||i(this._events.error)&&!this._events.error.length))throw arguments[1]instanceof Error?arguments[1]:new Error("Uncaught, unspecified 'error' event.");if(!this._events)return!1;var e=this._events[t];if(!e)return!1;if("function"==typeof e){switch(arguments.length){case 1:e.call(this);break;case 2:e.call(this,arguments[1]);break;case 3:e.call(this,arguments[1],arguments[2]);break;default:var n=Array.prototype.slice.call(arguments,1);e.apply(this,n)}return!0}if(i(e)){for(var n=Array.prototype.slice.call(arguments,1),r=e.slice(),s=0,o=r.length;o>s;s++)r[s].apply(this,n);return!0}return!1},n.prototype.addListener=function(t,e){if("function"!=typeof e)throw new Error("addListener only takes instances of Function");return this._events||(this._events={}),this.emit("newListener",t,e),this._events[t]?i(this._events[t])?this._events[t].push(e):this._events[t]=[this._events[t],e]:this._events[t]=e,this},n.prototype.on=n.prototype.addListener,n.prototype.once=function(t,e){var n=this;return n.on(t,function i(){n.removeListener(t,i),e.apply(this,arguments)}),this},n.prototype.removeListener=function(t,n){if("function"!=typeof n)throw new Error("removeListener only takes instances of Function");if(!this._events||!this._events[t])return this;var r=this._events[t];if(i(r)){var s=e(r,n);if(0>s)return this;r.splice(s,1),0==r.length&&delete this._events[t]}else this._events[t]===n&&delete this._events[t];return this},n.prototype.removeAllListeners=function(t){return 0===arguments.length?(this._events={},this):(t&&this._events&&this._events[t]&&(this._events[t]=null),this)},n.prototype.listeners=function(t){return this._events||(this._events={}),this._events[t]||(this._events[t]=[]),i(this._events[t])||(this._events[t]=[this._events[t]]),this._events[t]}},function(t,e,n){"use strict";var i=n(21),r=n(22),s=n(28),o=n(31),a=i({initialize:function(t){this.id=this.name=t},push:function(t){this.trigger("message",t)},isUnused:function(){return 0===this.countListeners("message")}});r(a.prototype,s),r(a,{HANDSHAKE:"/meta/handshake",CONNECT:"/meta/connect",SUBSCRIBE:"/meta/subscribe",UNSUBSCRIBE:"/meta/unsubscribe",DISCONNECT:"/meta/disconnect",META:"meta",SERVICE:"service",expand:function(t){var e=this.parse(t),n=["/**",t],i=e.slice();i[i.length-1]="*",n.push(this.unparse(i));for(var r=1,s=e.length;s>r;r++)i=e.slice(0,r),i.push("**"),n.push(this.unparse(i));return n},isValid:function(t){return o.CHANNEL_NAME.test(t)||o.CHANNEL_PATTERN.test(t)},parse:function(t){return this.isValid(t)?t.split("/").slice(1):null},unparse:function(t){return"/"+t.join("/")},isMeta:function(t){var e=this.parse(t);return e?e[0]===this.META:null},isService:function(t){var e=this.parse(t);return e?e[0]===this.SERVICE:null},isSubscribable:function(t){return this.isValid(t)?!this.isMeta(t)&&!this.isService(t):null},Set:i({initialize:function(){this._channels={}},getKeys:function(){var t=[];for(var e in this._channels)t.push(e);return t},remove:function(t){delete this._channels[t]},hasSubscription:function(t){return this._channels.hasOwnProperty(t)},subscribe:function(t,e){for(var n,i=0,r=t.length;r>i;i++){n=t[i];var s=this._channels[n]=this._channels[n]||new a(n);s.bind("message",e)}},unsubscribe:function(t,e){var n=this._channels[t];return n?(n.unbind("message",e),n.isUnused()?(this.remove(t),!0):!1):!1},distributeMessage:function(t){for(var e=a.expand(t.channel),n=0,i=e.length;i>n;n++){var r=this._channels[e[n]];r&&r.trigger("message",t)}}})}),t.exports=a},function(t){"use strict";t.exports={CHANNEL_NAME:/^\/(((([a-z]|[A-Z])|[0-9])|(\-|\_|\!|\~|\(|\)|\$|\@)))+(\/(((([a-z]|[A-Z])|[0-9])|(\-|\_|\!|\~|\(|\)|\$|\@)))+)*$/,CHANNEL_PATTERN:/^(\/(((([a-z]|[A-Z])|[0-9])|(\-|\_|\!|\~|\(|\)|\$|\@)))+)*\/\*{1,2}$/,ERROR:/^([0-9][0-9][0-9]:(((([a-z]|[A-Z])|[0-9])|(\-|\_|\!|\~|\(|\)|\$|\@)| |\/|\*|\.))*(,(((([a-z]|[A-Z])|[0-9])|(\-|\_|\!|\~|\(|\)|\$|\@)| |\/|\*|\.))*)*:(((([a-z]|[A-Z])|[0-9])|(\-|\_|\!|\~|\(|\)|\$|\@)| |\/|\*|\.))*|[0-9][0-9][0-9]::(((([a-z]|[A-Z])|[0-9])|(\-|\_|\!|\~|\(|\)|\$|\@)| |\/|\*|\.))*)$/,VERSION:/^([0-9])+(\.(([a-z]|[A-Z])|[0-9])(((([a-z]|[A-Z])|[0-9])|\-|\_))*)*$/}},function(t,e,n){(function(e){"use strict";var i=n(21),r=n(23),s=n(33),o=n(22),a=n(18),c=n(28),u=n(34),h=n(45),l=i({className:"Dispatcher",MAX_REQUEST_SIZE:2048,DEFAULT_RETRY:5,UP:1,DOWN:2,initialize:function(t,e,n){this._client=t,this.endpoint=r.parse(e),this._alternates=n.endpoints||{},this.cookies=s.CookieJar&&new s.CookieJar,this._disabled=[],this._envelopes={},this.headers={},this.retry=n.retry||this.DEFAULT_RETRY,this._scheduler=n.scheduler||h,this._state=0,this.transports={},this.wsExtensions=[],this.proxy=n.proxy||{},"string"==typeof this._proxy&&(this._proxy={origin:this._proxy});var i=n.websocketExtensions;if(i){i=[].concat(i);for(var o=0,a=i.length;a>o;o++)this.addWebsocketExtension(i[o])}this.tls=n.tls||{},this.tls.ca=this.tls.ca||n.ca;for(var c in this._alternates)this._alternates[c]=r.parse(this._alternates[c]);this.maxRequestSize=this.MAX_REQUEST_SIZE},endpointFor:function(t){return this._alternates[t]||this.endpoint},addWebsocketExtension:function(t){this.wsExtensions.push(t)},disable:function(t){this._disabled.push(t)},setHeader:function(t,e){this.headers[t]=e},close:function(){var t=this._transport;delete this._transport,t&&t.close()},getConnectionTypes:function(){return u.getConnectionTypes()},selectTransport:function(t){u.get(this,t,this._disabled,function(t){this.debug("Selected ? transport for ?",t.connectionType,r.stringify(t.endpoint)),t!==this._transport&&(this._transport&&this._transport.close(),this._transport=t,this.connectionType=t.connectionType)},this)},sendMessage:function(t,e,n){n=n||{};var i,r=t.id,s=n.attempts,o=n.deadline&&(new Date).getTime()+1e3*n.deadline,a=this._envelopes[r];a||(i=new this._scheduler(t,{timeout:e,interval:this.retry,attempts:s,deadline:o}),a=this._envelopes[r]={message:t,scheduler:i}),this._sendEnvelope(a)},_sendEnvelope:function(t){if(this._transport&&!t.request&&!t.timer){var n=t.message,i=t.scheduler,r=this;if(!i.isDeliverable())return i.abort(),delete this._envelopes[n.id],void 0;t.timer=e.setTimeout(function(){r.handleError(n)},1e3*i.getTimeout()),i.send(),t.request=this._transport.sendMessage(n)}},handleResponse:function(t){var n=this._envelopes[t.id];void 0!==t.successful&&n&&(n.scheduler.succeed(),delete this._envelopes[t.id],e.clearTimeout(n.timer)),this.trigger("message",t),this._state!==this.UP&&(this._state=this.UP,this._client.trigger("transport:up"))},handleError:function(t,n){var i=this._envelopes[t.id],r=i&&i.request,s=this;if(r){r.then(function(t){t&&t.abort&&t.abort()});var o=i.scheduler;o.fail(),e.clearTimeout(i.timer),i.request=i.timer=null,n?this._sendEnvelope(i):i.timer=e.setTimeout(function(){i.timer=null,s._sendEnvelope(i)},1e3*o.getInterval()),this._state!==this.DOWN&&(this._state=this.DOWN,this._client.trigger("transport:down"))}}});l.create=function(t,e,n){return new l(t,e,n)},o(l.prototype,c),o(l.prototype,a),t.exports=l}).call(e,function(){return this}())},function(t){"use strict";t.exports={}},function(t,e,n){"use strict";var i=n(35);i.register("websocket",n(37)),i.register("eventsource",n(41)),i.register("long-polling",n(42)),i.register("cross-origin-long-polling",n(43)),i.register("callback-polling",n(44)),t.exports=i},function(t,e,n){(function(e){"use strict";var i=n(21),r=n(33).Cookie,s=n(12),o=n(23),a=n(24),c=n(22),u=n(18),h=n(36),l=n(30),f=c(i({className:"Transport",DEFAULT_PORTS:{"http:":80,"https:":443,"ws:":80,"wss:":443},SECURE_PROTOCOLS:["https:","wss:"],MAX_DELAY:0,batching:!0,initialize:function(t,n){this._dispatcher=t,this.endpoint=n,this._outbox=[],this._proxy=c({},this._dispatcher.proxy),this._proxy.origin||"undefined"==typeof e||(this._proxy.origin=a.indexOf(this.SECURE_PROTOCOLS,this.endpoint.protocol)>=0?e.env.HTTPS_PROXY||e.env.https_proxy:e.env.HTTP_PROXY||e.env.http_proxy)},close:function(){},encode:function(){return""},sendMessage:function(t){return this.debug("Client ? sending message to ?: ?",this._dispatcher.clientId,o.stringify(this.endpoint),t),this.batching?(this._outbox.push(t),this._flushLargeBatch(),t.channel===l.HANDSHAKE?this._publish(.01):(t.channel===l.CONNECT&&(this._connectMessage=t),this._publish(this.MAX_DELAY))):s.resolve(this.request([t]))},_makePromise:function(){var t=this;this._requestPromise=this._requestPromise||new s(function(e){t._resolvePromise=e})},_publish:function(t){return this._makePromise(),this.addTimeout("publish",t,function(){this._flush(),delete this._requestPromise},this),this._requestPromise},_flush:function(){this.removeTimeout("publish"),this._outbox.length>1&&this._connectMessage&&(this._connectMessage.advice={timeout:0}),this._resolvePromise(this.request(this._outbox)),this._connectMessage=null,this._outbox=[]},_flushLargeBatch:function(){var t=this.encode(this._outbox);if(!(t.length<this._dispatcher.maxRequestSize)){var e=this._outbox.pop();this._makePromise(),this._flush(),e&&this._outbox.push(e)}},_receive:function(t){if(t){t=[].concat(t),this.debug("Client ? received from ? via ?: ?",this._dispatcher.clientId,o.stringify(this.endpoint),this.connectionType,t);for(var e=0,n=t.length;n>e;e++)this._dispatcher.handleResponse(t[e])}},_handleError:function(t){t=[].concat(t),this.debug("Client ? failed to send to ? via ?: ?",this._dispatcher.clientId,o.stringify(this.endpoint),this.connectionType,t);for(var e=0,n=t.length;n>e;e++)this._dispatcher.handleError(t[e])},_getCookies:function(){var t=this._dispatcher.cookies,e=o.stringify(this.endpoint);return t?a.map(t.getCookiesSync(e),function(t){return t.cookieString()}).join("; "):""},_storeCookies:function(t){var e,n=this._dispatcher.cookies,i=o.stringify(this.endpoint);if(t&&n){t=[].concat(t);for(var s=0,a=t.length;a>s;s++)e=r.parse(t[s]),n.setCookieSync(e,i)}}}),{get:function(t,e,n,i,r){var s=t.endpoint;a.asyncEach(this._transports,function(s,o){var c=s[0],u=s[1],h=t.endpointFor(c);return a.indexOf(n,c)>=0?o():a.indexOf(e,c)<0?(u.isUsable(t,h,function(){}),o()):(u.isUsable(t,h,function(e){if(!e)return o();var n=u.hasOwnProperty("create")?u.create(t,h):new u(t,h);i.call(r,n)}),void 0)},function(){throw new Error("Could not find a usable connection type for "+o.stringify(s))})},register:function(t,e){this._transports.push([t,e]),e.prototype.connectionType=t},getConnectionTypes:function(){return a.map(this._transports,function(t){return t[0]})},_transports:[]});c(f.prototype,u),c(f.prototype,h),t.exports=f}).call(e,n(1))},function(t,e){(function(e){"use strict";t.exports={addTimeout:function(t,n,i,r){if(this._timeouts=this._timeouts||{},!this._timeouts.hasOwnProperty(t)){var s=this;this._timeouts[t]=e.setTimeout(function(){delete s._timeouts[t],i.call(r)},1e3*n)}},removeTimeout:function(t){this._timeouts=this._timeouts||{};var n=this._timeouts[t];n&&(e.clearTimeout(n),delete this._timeouts[t])},removeAllTimeouts:function(){this._timeouts=this._timeouts||{};for(var t in this._timeouts)this.removeTimeout(t)}}}).call(e,function(){return this}())},function(t,e,n){(function(e){"use strict";var i=n(21),r=n(12),s=n(38),o=n(23),a=n(25),c=n(39),u=n(22),h=n(19),l=n(40),f=n(27),d=n(35),p=u(i(d,{UNCONNECTED:1,CONNECTING:2,CONNECTED:3,batching:!1,isUsable:function(t,e){this.callback(function(){t.call(e,!0)}),this.errback(function(){t.call(e,!1)}),this.connect()},request:function(t){this._pending=this._pending||new s;for(var e=0,n=t.length;n>e;e++)this._pending.add(t[e]);var i=this,o=new r(function(e){i.callback(function(n){n&&1===n.readyState&&(n.send(h(t)),e(n))}),i.connect()});return{abort:function(){o.then(function(t){t.close()})}}},connect:function(){if(!p._unloaded&&(this._state=this._state||this.UNCONNECTED,this._state===this.UNCONNECTED)){this._state=this.CONNECTING;var t=this._createSocket();if(!t)return this.setDeferredStatus("failed");var e=this;t.onopen=function(){t.headers&&e._storeCookies(t.headers["set-cookie"]),e._socket=t,e._state=e.CONNECTED,e._everConnected=!0,e._ping(),e.setDeferredStatus("succeeded",t)};var n=!1;t.onclose=t.onerror=function(){if(!n){n=!0;var i=e._state===e.CONNECTED;t.onopen=t.onclose=t.onerror=t.onmessage=null,delete e._socket,e._state=e.UNCONNECTED,e.removeTimeout("ping");var r=e._pending?e._pending.toArray():[];delete e._pending,i||e._everConnected?(e.setDeferredStatus("unknown"),e._handleError(r,i)):e.setDeferredStatus("failed")}},t.onmessage=function(t){var n;try{n=JSON.parse(t.data)}catch(i){}if(n){n=[].concat(n);for(var r=0,s=n.length;s>r;r++)void 0!==n[r].successful&&e._pending.remove(n[r]);e._receive(n)}}}},close:function(){this._socket&&this._socket.close()},_createSocket:function(){var t=p.getSocketUrl(this.endpoint),e=this._dispatcher.headers,n=this._dispatcher.wsExtensions,i=this._getCookies(),r=this._dispatcher.tls,s={extensions:n,headers:e,proxy:this._proxy,tls:r};return""!==i&&(s.headers.Cookie=i),l.create(t,[],s)},_ping:function(){this._socket&&1===this._socket.readyState&&(this._socket.send("[]"),this.addTimeout("ping",this._dispatcher.timeout/2,this._ping,this))}}),{PROTOCOLS:{"http:":"ws:","https:":"wss:"},create:function(t,e){var n=t.transports.websocket=t.transports.websocket||{};return n[e.href]=n[e.href]||new this(t,e),n[e.href]},getSocketUrl:function(t){return t=c(t),t.protocol=this.PROTOCOLS[t.protocol],o.stringify(t)},isUsable:function(t,e,n,i){this.create(t,e).isUsable(n,i)}});u(p.prototype,f),a.Event&&void 0!==e.onbeforeunload&&a.Event.on(e,"beforeunload",function(){p._unloaded=!0}),t.exports=p}).call(e,function(){return this}())},function(t,e,n){"use strict";var i=n(21);t.exports=i({initialize:function(){this._index={}},add:function(t){var e=void 0!==t.id?t.id:t;return this._index.hasOwnProperty(e)?!1:(this._index[e]=t,!0)},forEach:function(t,e){for(var n in this._index)this._index.hasOwnProperty(n)&&t.call(e,this._index[n])},isEmpty:function(){for(var t in this._index)if(this._index.hasOwnProperty(t))return!1;return!0},member:function(t){for(var e in this._index)if(this._index[e]===t)return!0;return!1},remove:function(t){var e=void 0!==t.id?t.id:t,n=this._index[e];return delete this._index[e],n},toArray:function(){var t=[];return this.forEach(function(e){t.push(e)}),t}})},function(t){"use strict";var e=function(t){var n,i,r;if(t instanceof Array){for(n=[],i=t.length;i--;)n[i]=e(t[i]);return n}if("object"==typeof t){n=null===t?null:{};for(r in t)n[r]=e(t[r]);return n}return t};t.exports=e},function(t,e){(function(e){"use strict";var n=e.MozWebSocket||e.WebSocket;t.exports={create:function(t){return new n(t)}}}).call(e,function(){return this}())},function(t,e,n){(function(e){"use strict";var i=n(21),r=n(23),s=n(39),o=n(22),a=n(27),c=n(35),u=n(42),h=o(i(c,{initialize:function(t,n){if(c.prototype.initialize.call(this,t,n),!e.EventSource)return this.setDeferredStatus("failed");this._xhr=new u(t,n),n=s(n),n.pathname+="/"+t.clientId;var i=new e.EventSource(r.stringify(n)),o=this;i.onopen=function(){o._everConnected=!0,o.setDeferredStatus("succeeded")},i.onerror=function(){o._everConnected?o._handleError([]):(o.setDeferredStatus("failed"),i.close())},i.onmessage=function(t){var e;try{e=JSON.parse(t.data)}catch(n){}e?o._receive(e):o._handleError([])},this._socket=i},close:function(){this._socket&&(this._socket.onopen=this._socket.onerror=this._socket.onmessage=null,this._socket.close(),delete this._socket)},isUsable:function(t,e){this.callback(function(){t.call(e,!0)}),this.errback(function(){t.call(e,!1)})},encode:function(t){return this._xhr.encode(t)},request:function(t){return this._xhr.request(t)}}),{isUsable:function(t,e,n,i){var r=t.clientId;return r?(u.isUsable(t,e,function(r){return r?(this.create(t,e).isUsable(n,i),void 0):n.call(i,!1)},this),void 0):n.call(i,!1)},create:function(t,e){var n=t.transports.eventsource=t.transports.eventsource||{},i=t.clientId,o=s(e);return o.pathname+="/"+(i||""),o=r.stringify(o),n[o]=n[o]||new this(t,e),n[o]}});o(h.prototype,a),t.exports=h}).call(e,function(){return this}())},function(t,e,n){(function(e){"use strict";var i=n(21),r=n(23),s=n(25),o=n(22),a=n(19),c=n(35),u=o(i(c,{encode:function(t){return a(t)},request:function(t){var n,i=this.endpoint.href,r=this;if(e.XMLHttpRequest)n=new XMLHttpRequest;else{if(!e.ActiveXObject)return this._handleError(t);n=new ActiveXObject("Microsoft.XMLHTTP")}n.open("POST",i,!0),n.setRequestHeader("Content-Type","application/json"),n.setRequestHeader("Pragma","no-cache"),n.setRequestHeader("X-Requested-With","XMLHttpRequest");var o=this._dispatcher.headers;for(var a in o)o.hasOwnProperty(a)&&n.setRequestHeader(a,o[a]);var c=function(){n.abort()};return void 0!==e.onbeforeunload&&s.Event.on(e,"beforeunload",c),n.onreadystatechange=function(){if(n&&4===n.readyState){var i=null,o=n.status,a=n.responseText,u=o>=200&&300>o||304===o||1223===o;if(void 0!==e.onbeforeunload&&s.Event.detach(e,"beforeunload",c),n.onreadystatechange=function(){},n=null,!u)return r._handleError(t);try{i=JSON.parse(a)}catch(h){}i?r._receive(i):r._handleError(t)}},n.send(this.encode(t)),n}}),{isUsable:function(t,e,n,i){var s="ReactNative"===navigator.product||r.isSameOrigin(e);n.call(i,s)}});t.exports=u}).call(e,function(){return this}())},function(t,e,n){(function(e){"use strict";var i=n(21),r=n(38),s=n(23),o=n(22),a=n(19),c=n(35),u=o(i(c,{encode:function(t){return"message="+encodeURIComponent(a(t))},request:function(t){var n,i=e.XDomainRequest?XDomainRequest:XMLHttpRequest,r=new i,o=++u._id,a=this._dispatcher.headers,c=this;if(r.open("POST",s.stringify(this.endpoint),!0),r.setRequestHeader){r.setRequestHeader("Pragma","no-cache");for(n in a)a.hasOwnProperty(n)&&r.setRequestHeader(n,a[n])}var h=function(){return r?(u._pending.remove(o),r.onload=r.onerror=r.ontimeout=r.onprogress=null,r=null,void 0):!1};return r.onload=function(){var e;try{e=JSON.parse(r.responseText)}catch(n){}h(),e?c._receive(e):c._handleError(t)},r.onerror=r.ontimeout=function(){h(),c._handleError(t)},r.onprogress=function(){},i===e.XDomainRequest&&u._pending.add({id:o,xhr:r}),r.send(this.encode(t)),r}}),{_id:0,_pending:new r,isUsable:function(t,n,i,r){if(s.isSameOrigin(n))return i.call(r,!1);if(e.XDomainRequest)return i.call(r,n.protocol===location.protocol);if(e.XMLHttpRequest){var o=new XMLHttpRequest;return i.call(r,void 0!==o.withCredentials)}return i.call(r,!1)}});t.exports=u}).call(e,function(){return this}())},function(t,e,n){(function(e){"use strict";var i=n(21),r=n(23),s=n(39),o=n(22),a=n(19),c=n(35),u=o(i(c,{encode:function(t){var e=s(this.endpoint);return e.query.message=a(t),e.query.jsonp="__jsonp"+u._cbCount+"__",r.stringify(e)},request:function(t){var n=document.getElementsByTagName("head")[0],i=document.createElement("script"),o=u.getCallbackName(),c=s(this.endpoint),h=this;c.query.message=a(t),c.query.jsonp=o;var l=function(){if(!e[o])return!1;e[o]=void 0;try{delete e[o]}catch(t){}i.parentNode.removeChild(i)};return e[o]=function(t){l(),h._receive(t)},i.type="text/javascript",i.src=r.stringify(c),n.appendChild(i),i.onerror=function(){l(),h._handleError(t)},{abort:l}}}),{_cbCount:0,getCallbackName:function(){return this._cbCount+=1,"__jsonp"+this._cbCount+"__"},isUsable:function(t,e,n,i){n.call(i,!0)}});t.exports=u}).call(e,function(){return this}())},function(t,e,n){"use strict";var i=n(22),r=function(t,e){this.message=t,this.options=e,this.attempts=0};i(r.prototype,{getTimeout:function(){return this.options.timeout},getInterval:function(){return this.options.interval},isDeliverable:function(){var t=this.options.attempts,e=this.attempts,n=this.options.deadline,i=(new Date).getTime();return void 0!==t&&e>=t?!1:void 0!==n&&i>n?!1:!0},send:function(){this.attempts+=1},succeed:function(){},fail:function(){},abort:function(){}}),t.exports=r},function(t,e,n){"use strict";var i=n(21),r=n(31),s=i({initialize:function(t,e,n){this.code=t,this.params=Array.prototype.slice.call(e),this.message=n},toString:function(){return this.code+":"+this.params.join(",")+":"+this.message}});s.parse=function(t){if(t=t||"",!r.ERROR.test(t))return new s(null,[],t);var e=t.split(":"),n=parseInt(e[0]),i=e[1].split(","),t=e[2];return new s(n,i,t)};var o={versionMismatch:[300,"Version mismatch"],conntypeMismatch:[301,"Connection types not supported"],extMismatch:[302,"Extension mismatch"],badRequest:[400,"Bad request"],clientUnknown:[401,"Unknown client"],parameterMissing:[402,"Missing required parameter"],channelForbidden:[403,"Forbidden channel"],channelUnknown:[404,"Unknown channel"],channelInvalid:[405,"Invalid channel"],extUnknown:[406,"Unknown extension"],publishFailed:[407,"Failed to publish"],serverError:[500,"Internal server error"]};for(var a in o)(function(t){s[t]=function(){return new s(o[t][0],arguments,o[t][1]).toString()}})(a);t.exports=s},function(t,e,n){"use strict";var i=n(22),r=n(18),s={addExtension:function(t){this._extensions=this._extensions||[],this._extensions.push(t),t.added&&t.added(this)},removeExtension:function(t){if(this._extensions)for(var e=this._extensions.length;e--;)this._extensions[e]===t&&(this._extensions.splice(e,1),t.removed&&t.removed(this))},pipeThroughExtensions:function(t,e,n,i,r){if(this.debug("Passing through ? extensions: ?",t,e),!this._extensions)return i.call(r,e);var s=this._extensions.slice(),o=function(e){if(!e)return i.call(r,e);var a=s.shift();if(!a)return i.call(r,e);var c=a[t];return c?(c.length>=3?a[t](e,n,o):a[t](e,o),void 0):o(e)};o(e)}};i(s,r),t.exports=s},function(t,e,n){"use strict";var i=n(21),r=n(27);t.exports=i(r)},function(t,e,n){"use strict";var i=n(21),r=n(22),s=n(27),o=i({initialize:function(t,e,n,i){this._client=t,this._channels=e,this._callback=n,this._context=i,this._cancelled=!1},withChannel:function(t,e){return this._withChannel=[t,e],this},apply:function(t,e){var n=e[0];this._callback&&this._callback.call(this._context,n.data),this._withChannel&&this._withChannel[0].call(this._withChannel[1],n.channel,n.data)},cancel:function(){this._cancelled||(this._client.unsubscribe(this._channels,this),this._cancelled=!0)},unsubscribe:function(){this.cancel()}});r(o.prototype,s),t.exports=o}])});