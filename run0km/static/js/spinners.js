var spin_opts = {
    lines: 7, length: 7, width: 2, radius: 2, corners: 1, rotate: 0, direction: 1,
    color: '#00A', speed: 1, trail: 60, className: 'spinner',
    zIndex: 1, // The z-index (defaults to 1)
    top: '12px', // Top position relative to parent
    left: '12px' // Left position relative to parent
  };

var spinners = {};

function AgregarSpinner(holder_id, spinner_options){
    var spinner;
    var options = spinner_options;
    if (spinner_options === undefined)
      options = spin_opts;
    var holder = document.getElementById(holder_id);
    if (spinners[holder_id]===undefined){
      spinner = new Spinner(options).spin(holder);
      spinners[holder_id] = spinner;
    }
    else{
      spinner = spinners[holder_id];
      spinner.spin(holder);
    }
    return spinner;
}