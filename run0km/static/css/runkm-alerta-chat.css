/* Popup Chat */
#cont-popup-alerta-chat{
    display: block;
    width: 597px;
    background-color: #20509A;
    padding: 40px;
    border-radius: 20px;
    box-sizing: border-box;
}

#cont-popup-alerta-chat button{
    -webkit-transition: all 0.2s ease;
	-moz-transition: all 0.2s ease;
	-o-transition: all 0.2s ease;
	transition: all 0.2s ease;
}

.alerta_de_chat .ui-dialog-titlebar {
    display: none
}

.alerta_de_chat {
  border: none;
  background: transparent;
}

/* Titulo Alerta Chat */
#cont-popup-alerta-chat h1.con-alerta-titulo {
    display: block;
    float: left;
    font-style: normal;
    font-family: Arial;
    font-variant: normal;
    text-transform: capitalize;
    padding-left: 37px;
    padding-top: 0;
    text-shadow: 2px 2px 3px rgba(0,0,0,.8);
    background: url(../css/images/chats/flecha-alerta-chat-titulo.png) no-repeat 4px 6px;
}
#cont-popup-alerta-chat span.alerta-titlulo-1 {
    display: block;
    font-size: 51px;
    color: #fff;
    line-height: 37px;
    margin-top: 9px;
    margin-left: 0;
}
#cont-popup-alerta-chat span.alerta-titlulo-2 {
    font-size: 45px;
    margin-left: 18px;
    line-height: 43px;
    display: inline-block;
}
#cont-popup-alerta-chat span.alerta-titlulo-3 {
    font-size: 81px;
    margin-left: 140px;
    line-height: 68px;
    display: block;
    position: absolute;
    margin-top: -66px;
}
/* Tiempo para responder */
#cont-popup-alerta-chat div.cont-tiempo-respuesta{
    display: block;
    float: right;
    margin-top: -3px;
    width: 227px;
}
#cont-popup-alerta-chat span.tiempo-para-responder{
    display: block;
    margin: 0 auto;
    text-align: center;
    width: 196px;
    color: #fff;
    line-height: 34px;
    font-size: 12px;
    text-transform: uppercase;
    height: 31px;
    background-color: #053A6D;
    border-radius: 10px 10px 0 0;
}
#cont-popup-alerta-chat span.alerta-chat-tiempo{/* Date Time */
    display: block;
    margin: 0 auto;
    text-align: center;
    color: #3caeed;
    font-size: 39px;
    line-height: 65px;
    height: 65px;
    background-color: #fff;
    box-shadow: inset 0px 0px 10px rgba(0,0,0,.8);
    border-radius: 7px;
}
#cont-popup-alerta-chat .clear{
    clear: both;
}
/* Datos del contacto */
#cont-popup-alerta-chat div.alerta-datos-cliente{
    display: block;
    margin: 41px auto 0;
    min-height: 180px;
    width: 100%;
    background-color: #0C2F65;
    color: #fff;
    padding: 20px;
    box-sizing: border-box;
    border-radius: 15px;
}
#cont-popup-alerta-chat div.alerta-datos-cliente table {
    width: 100%;
}

#cont-popup-alerta-chat div.alerta-datos-cliente table tr td {
    display: inline-block;
}

#cont-popup-alerta-chat div.alerta-datos-cliente table tr td span.titulo {
    width: 97px;
    display: block;
    font-size: 17px;
    font-weight: bold;
    height: 22px;
    text-align: right;
}

#cont-popup-alerta-chat div.alerta-datos-cliente table tr td span.dato {
    padding-left: 9px;
}

#cont-popup-alerta-chat div.alerta-contBTverMas button {/* Bot�n ver mas */
    display: block;
    float: right;
    background-color: #146e9e;
    border: 0;
    border-radius: 15px;
    font-size: 12px;
    text-transform: uppercase;
    color: #ffffff;
    line-height: 18px;
    padding-right: 12px;
    padding-left: 11px;
    box-shadow: 2px 2px 4px rgba(0,0,0,.6), inset 4px 4px 12px rgba(255,255,255,.2);
}
/* Botones aceptar Rechazar */
#cont-popup-alerta-chat div.alerta-cont-bt-ar {
    margin-top: 56px;
    text-align: center;
}
#cont-popup-alerta-chat div.alerta-cont-bt-ar button {
    display: inline-block;
    background-color: transparent;
    border: 0;
    line-height: 58px;
    width: 197px;
    padding-right: 31px;
    font-size: 15px;
    font-weight: bold;
    text-transform: uppercase;
    color: #fff;
    background-image: url(../css/images/chats/alerta-aceptar.jpg);
    background-position: center 3px;
    background-repeat: no-repeat;
}

#cont-popup-alerta-chat div.alerta-cont-bt-ar button.rechazar {
    background-image: url(../css/images/chats/alerta-rechazar.jpg);
    background-position: center 3px;
    background-repeat: no-repeat;
}
#cont-popup-alerta-chat div.alerta-cont-bt-ar button:hover{
    background-position: 22px 3px;    
}