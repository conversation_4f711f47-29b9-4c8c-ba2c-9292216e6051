import json
import traceback

from django.db import models


class LogDeError(models.Model):
    """
    El LogDeError fue creado para ser extendido por proxies. Cuidado con la forma de usarlos.
    https://docs.djangoproject.com/en/1.8/topics/db/models/#proxy-models
    """
    CODIGO_LOG_DE_ERROR_NORMALIZADOR = 'N'
    CODIGO_LOG_DE_ERROR_CHEQUEADOR_DE_WHATSAPP = 'W'
    CODIGO_LOG_DE_ERROR_ACTUALIZACION_DE_ESTADO_SMS = 'S'
    CODIGO_LOG_DE_ERROR_INFORMACION_DE_REDES_SOCIALES = 'R'
    CODIGO_LOG_DE_ERROR_CRM = 'C'

    # El codigo es para ser usado por sus proxies
    codigo = models.CharField(max_length=3, default='', blank=True)
    fecha = models.DateTimeField(auto_now_add=True)
    tipo = models.CharField(max_length=128)
    descripcion = models.TextField(max_length=512, blank=True)
    traceback_info = models.TextField(max_length=8192, blank=True, null=True)
    request = models.TextField(blank=True, null=True)
    response = models.TextField(blank=True, null=True)
    metadata = models.TextField(max_length=256, default='{}')

    class Meta:
        verbose_name_plural = "Logs de Errores"

    @classmethod
    def nuevo(cls, tipo, descripcion, traceback_info, request, response, metadata=None):
        descripcion_recortada = descripcion[0:512]
        traceback_info_recortada = traceback_info[0:8192]
        error_log = cls(codigo=cls.codigo_de_log(), tipo=tipo, descripcion=descripcion_recortada,
                        traceback_info=traceback_info_recortada,
                        request=request, response=response, metadata=cls._convertir_a_string(metadata))
        error_log.full_clean()
        error_log.save()
        return error_log

    @classmethod
    def guardar_log(cls, error, descripcion=None, traceback_info=None, request=None, response=None, metadata=None):
        if traceback_info is None:
            traceback_info = traceback.format_exc()
        if not descripcion:
            descripcion = str(error)
        tipo = error.error_type()
        log = cls.nuevo(tipo, descripcion, traceback_info, request, response, metadata)
        return log

    @classmethod
    def guardar_error_de_comunicacion_log(cls, error, descripcion=None, traceback_info=None, metadata=None):
        request = error.request
        response = error.response
        log = cls.guardar_log(error, descripcion, traceback_info, request, response, metadata)
        return log

    @classmethod
    def codigo_de_log(cls):
        raise NotImplementedError('subclass responsibility')

    def obtener_metadata(self):
        return json.loads(self.metadata)

    @classmethod
    def _convertir_a_string(cls, metadata):
        if metadata is None:
            return '{}'
        else:
            return json.dumps(metadata)