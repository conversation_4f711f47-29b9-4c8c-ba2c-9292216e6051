from django.contrib import admin, messages


from crms.tasks import enviar_a_crm
from log_de_errores.models import LogDeError
from prospectos.models import LogDeErrorDeCRM


class LogDeErrorAdmin(admin.ModelAdmin):
    list_display = ('tipo', 'fecha', 'descripcion', 'traceback_info')
    list_filter = ('tipo', )
    date_hierarchy = 'fecha'
    actions = ['reintentar_integracion']

    def has_add_permission(self, request):
            return False

    def get_readonly_fields(self, request, obj=None):
        return 'tipo', 'fecha', 'descripcion', 'traceback_info', 'request', 'response', 'codigo', 'metadata'

    def reintentar_integracion(self, request, queryset):
        for log_de_error in queryset.filter(tipo=LogDeErrorDeCRM.codigo_de_log()):
            metadata = log_de_error.obtener_metadata()
            enviar_a_crm.delay(metadata['prospecto_id'], metadata['opcion_id'])

        self.message_user(request, f'Se ha enviado el pedido de integración', messages.SUCCESS)

    reintentar_integracion.short_description = 'Reintentar integracion'

admin.site.register(LogDeError, LogDeErrorAdmin)