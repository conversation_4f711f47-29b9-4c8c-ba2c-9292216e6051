# -*- coding: utf-8 -*-


from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('log_de_errores', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='logdeerror',
            options={'verbose_name_plural': 'Logs de Errores'},
        ),
        migrations.AddField(
            model_name='logdeerror',
            name='codigo',
            field=models.CharField(default=b'', max_length=3, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='logdeerror',
            name='request',
            field=models.TextField(max_length=2048, null=True, blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='logdeerror',
            name='response',
            field=models.TextField(max_length=2048, null=True, blank=True),
            preserve_default=True,
        ),
    ]
