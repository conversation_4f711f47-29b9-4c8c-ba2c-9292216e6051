import re

from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.mail import EmailMultiAlternatives

from conversaciones.meta.criterio_de_seleccion_para_operador_de_meta import CriterioDeSeleccionParaOperadorDeMeta
from conversaciones.models import MensajeDeWhatsappConProspecto, Conversacion
from conversaciones.models.enlace_con_meta import EnlaceConMeta
from core.models import Sistema
from lib.api_client import ClientConnectionError
from whatsapp.meta.models.conversation_in_meta import ConversationInMeta
from whatsapp.meta.models.message_in_meta import MessageInMeta
from whatsapp.meta.models.operador import Operador


class CanalDeComunicacionDeWhatsappViaMeta:

    def __init__(self, servicio_de_meta, notificador, criterio_seleccion_de_operador, url_de_notificaciones_de_estado,
                 logger):
        self._servicio_de_meta = servicio_de_meta
        self._notificador = notificador
        self._criterio_seleccion_de_operador = criterio_seleccion_de_operador
        self._url_de_notificaciones_de_estado = url_de_notificaciones_de_estado
        self._logger = logger
        self._cantidad_de_dias_de_vigencia_de_los_enlaces = Sistema.instance().tiempo_antiguedad_maximo_para_enlace_vigente

    @classmethod
    def nuevo(cls, servicio_de_meta, notificador, logger=None):
        return cls(
            servicio_de_meta=servicio_de_meta,
            notificador=notificador, criterio_seleccion_de_operador=CriterioDeSeleccionParaOperadorDeMeta(),
            url_de_notificaciones_de_estado=settings.WS_URL_STATUS_NOTIFICATION,
            logger=logger)

    def obtener_url_de_notificaciones_de_estado(self):
        return self._url_de_notificaciones_de_estado

    def enviar_mensaje_a(self, prospecto, texto, telefono_destinatario):
        """
        Esto está incompleto, habría que ver si el operador tiene un template de la categoría Incontactable.
        el modelo MetaTemplate debería tener "categorías" de templates.
        """
        if prospecto.esta_finalizado():
            raise ValidationError('No es posible enviar un mensaje a este número')

        operador = self.operador_para_prospecto(prospecto)
        if operador is None:
            raise ValidationError('No es posible enviar un mensaje a este número')

        telefono_sin_codigo_de_pais = self._telefono_sin_codigo_pais_de(telefono_destinatario)
        self._validar_que_no_existen_enlaces_vigentes_para(operador, prospecto, telefono_sin_codigo_de_pais)
        conversacion = self.conversacion_con_operador(telefono_sin_codigo_de_pais, operador)

        if conversacion is None:
            self._enviar_mensaje_para_empezar_conversacion(prospecto, texto, telefono_sin_codigo_de_pais, operador)

        # Existe conversacion y esta la ventana abierta
        elif conversacion.is_open():
            self._enlazar_con_meta_si_corresponde(
                operador=operador,
                prospecto=prospecto,
                telefono_destinatario=telefono_sin_codigo_de_pais,
                debe_actualizar=False)
            self._enviar_mensaje_a(prospecto, texto, telefono_sin_codigo_de_pais, conversacion)

        # Existe conversacion y ya enviamos template, pero esto no verifica de cuando es la conversacion
        elif conversacion.has_template_sent() and not conversacion.is_expired():
            self._enlazar_con_meta_si_corresponde(
                operador=operador,
                prospecto=prospecto,
                telefono_destinatario=telefono_sin_codigo_de_pais,
                debe_actualizar=False)
            self._agregar_mensaje_pendiente(conversacion, prospecto, telefono_sin_codigo_de_pais, texto)

        else:
            self._enviar_mensaje_para_empezar_conversacion(prospecto, texto, telefono_sin_codigo_de_pais, operador)

    def actualizar_desde(self, meta_changes):
        if meta_changes.is_change_of_statuses():
            mensajes_de_meta = self._actualizar_estado_y_conversacion_desde(meta_changes)
        else:
            mensajes_de_meta = [self._actualizar_respuestas_desde(meta_changes)]
        for mensaje_de_meta in mensajes_de_meta:
            self._enviar_mensajes_pendientes_si_la_ventana_esta_abierta(mensaje_de_meta.conversation())

    def operador_para_prospecto(self, prospecto):
        return self._criterio_seleccion_de_operador.seleccionar_para_prospecto(prospecto)

    def existe_un_enlace_entre(self, operador, telefono_destinatario, prospecto):
        return EnlaceConMeta.objects.con(
            operador=operador,
            telefono_destinatario=telefono_destinatario,
            prospecto=prospecto).exists()

    def enlaces_entre(self, operador, telefonos_destinatarios):
        enlaces = EnlaceConMeta.objects.con_algun_telefono_destinatario(telefonos_destinatarios=telefonos_destinatarios)
        if operador is not None:
            enlaces = enlaces.con_operador(operador=operador)
        return enlaces

    def tiene_un_mensaje_con_el_id_de_meta(self, id_de_mensaje_en_meta):
        return MessageInMeta.objects.with_meta_id(id_de_mensaje_en_meta).exists()

    def mensaje_con_el_id_de_meta(self, id_de_mensaje_en_meta):
        # Investigar si Meta nos garantiza que el ID sea unico. En tal caso usar unique=True en el campo y un get.
        mensaje = MessageInMeta.objects.with_meta_id(id_de_mensaje_en_meta).last()
        if mensaje is None:
            raise ValueError('No se encontró el mensaje con el id de meta {}'.format(id_de_mensaje_en_meta))
        return mensaje

    def mensaje_con_telefono(self, telefono):
        return MessageInMeta.objects.with_phone_number(telefono).last()

    def tiene_un_mensaje_en_espera_con_texto(self, texto):
        return self.cantidad_de_mensajes_de_espera_con_texto(texto) > 0

    def cantidad_de_mensajes_de_espera_con_texto(self, texto):
        return MessageInMeta.objects.pending_messages().with_text(texto).count()

    def conversacion_con_operador(self, telefono_destinatario, operador):
        message = MessageInMeta.objects.with_phone_number(
            phone_number=telefono_destinatario).with_operator(operador).last()

        return message.conversation() if message else None

    def cantidad_de_conversaciones_con_operador(self, operador):
        return ConversationInMeta.objects.with_operator(operador).count()

    def _validar_que_no_existen_enlaces_vigentes_para(self, operador, prospecto, telefono_sin_codigo_de_pais):
        if self._existen_enlaces_vigentes_para(operador, prospecto, telefono_sin_codigo_de_pais):
            raise ValidationError('No es posible iniciar conversación con este número')

    def _telefono_sin_codigo_pais_de(self, telefono_destinatario):
        variantes_de_telefono = self._variantes_de_telefono(telefono_destinatario)
        telefono_destinatario = variantes_de_telefono[1]
        return telefono_destinatario

    def _existen_enlaces_vigentes_para(self, operador, prospecto, telefono_destinatario):
        enlaces = self.enlaces_entre(
            operador=operador,
            telefonos_destinatarios=self._variantes_de_telefono(telefono_destinatario))
        enlaces_de_otros_vendedores = enlaces.excluir_con_vendedor(vendedor=prospecto.obtener_vendedor())
        enlaces_vigentes = [enlace for enlace in enlaces_de_otros_vendedores if enlace.esta_vigente(
            dias_maximos=self._cantidad_de_dias_de_vigencia_de_los_enlaces)]
        return enlaces_vigentes

    def _enviar_mensaje_para_empezar_conversacion(self, prospecto, texto, telefono_destinatario, operador):
        nombre_del_template = operador.template_por_defecto().obtener_nombre()
        try:
            respuesta = self._servicio_de_meta.iniciar_conversacion(
                telefono_destinatario=telefono_destinatario,
                marca=prospecto.obtener_marca().nombre(),
                dni=prospecto.obtener_valor_campo_extra('DNI', ''),
                id_operador=operador.obtener_id_operador(),
                nombre_template=nombre_del_template,
                url_notificacion=self._url_de_notificaciones_de_estado)
        except ClientConnectionError as error:
            self._log_error(error)
            raise ValueError("No es posible enviar el mensaje")
        else:
            self._log_info(f'Envio de template a prospecto con id:{prospecto.id} a telefono {telefono_destinatario}',
                           respuesta)
            id_de_meta = respuesta['messages'][0]['id']
            status_en_meta = respuesta['messages'][0]['message_status']
            conversacion = ConversationInMeta.new(operador)
            MessageInMeta.new_template(meta_id=id_de_meta, status=status_en_meta, template_name=nombre_del_template,
                                       phone_number=telefono_destinatario, conversation=conversacion)
            self._enlazar_con_meta_si_corresponde(
                operador=operador,
                prospecto=prospecto,
                telefono_destinatario=telefono_destinatario,
                debe_actualizar=True)
            self._agregar_mensaje_pendiente(conversacion, prospecto, telefono_destinatario, texto)

    def _enlazar_con_meta_si_corresponde(self, operador, prospecto, telefono_destinatario, debe_actualizar):
        enlace, _ = EnlaceConMeta.objects.get_or_create(
            _operador=operador,
            _telefono_destinatario=telefono_destinatario,
            _prospecto=prospecto)
        if debe_actualizar:
            enlace.save()
        return enlace

    def _agregar_mensaje_pendiente(self, conversacion, prospecto, telefono_destinatario, texto):
        mensaje_meta = conversacion.add_pending_message(telefono_destinatario, texto)
        self._crear_nuevo_mensaje_a_prospecto(mensaje_meta, prospecto, MensajeDeWhatsappConProspecto.VENDEDOR)

    def _enviar_mensaje_a(self, prospecto, texto, telefono_destinatario, conversacion):
        try:
            respuesta = self._servicio_de_meta.enviar_mensaje(
                telefono_destinatario=telefono_destinatario,
                marca=prospecto.obtener_marca().nombre(),
                dni=prospecto.obtener_valor_campo_extra('DNI', ''),
                id_operador=conversacion.operator().obtener_id_operador(),
                texto=texto,
                url_notificacion=self._url_de_notificaciones_de_estado)
        except ClientConnectionError as error:
            self._log_error(error)
        else:
            self._log_info(f'Envio de mensaje a prospecto con id:{prospecto.id} a telefono {telefono_destinatario}',
                           respuesta)
            id_de_meta = respuesta['messages'][0]['id']
            mensaje_meta = conversacion.add_sent_message(id_from_meta=id_de_meta, phone_number=telefono_destinatario,
                                                         text=texto)
            self._crear_nuevo_mensaje_a_prospecto(mensaje_meta, prospecto, MensajeDeWhatsappConProspecto.VENDEDOR)

    def _crear_nuevo_mensaje_a_prospecto(self, mensaje_meta, prospecto, emisor):
        mensaje = MensajeDeWhatsappConProspecto.nuevo_desde_meta(
            prospecto=prospecto,
            mensaje_meta=mensaje_meta,
            emisor=emisor)
        Conversacion.nuevo_mensaje(mensaje)

    def _actualizar_estado_y_conversacion_desde(self, meta_changes):
        return meta_changes.with_statuses_changes_do(self._actualizar_estado_de_mensaje,
                                                     self._actualizar_estado_fallido_de_mensaje)

    def _actualizar_estado_de_mensaje(self, id_meta, status, conversation_data):
        mensaje_meta = self._actualizar_estado_del_mensaje_meta(id_meta, status)
        self._actualizar_conversacion_desde(mensaje_meta.conversation(), conversation_data)
        return mensaje_meta

    def _actualizar_estado_del_mensaje_meta(self, id_meta, status):
        mensaje_meta = self.mensaje_con_el_id_de_meta(id_meta)
        try:
            mensaje_meta.change_status_to(status=status)
        except ValidationError as err:
            pass
        return mensaje_meta

    def _actualizar_estado_fallido_de_mensaje(self, id_meta, operador_id, telefono_destinatario, errores):
        mensaje_meta = self._actualizar_estado_del_mensaje_meta(id_meta, MessageInMeta.FAILED)
        body = self._crear_contenido_del_mail_con_detalles_de(errores, id_meta)
        email = EmailMultiAlternatives(subject=f'Whatsapp Meta Failed Error - Operador:{operador_id} - Receptor:{telefono_destinatario}',
                                       body=body,
                                       from_email=settings.DEFAULT_FROM_EMAIL,
                                       to=[settings.EMAIL_WHATSAPP_META_NOTIFICATION])
        email.send()
        return mensaje_meta

    def _crear_contenido_del_mail_con_detalles_de(self, errores, id_meta):
        body = f'Whatsapp Meta ID:{id_meta}\n'
        for error in errores:
            linea = f'Code: {error.code()} - Title: {error.title()} - Detail : {error.detail()} - Href: {error.href()}\n'
            body = body + linea
        return body

    def _actualizar_conversacion_desde(self, conversacion, meta_conversation_changes):
        if meta_conversation_changes is not None:
            conversacion.change_meta_id(meta_id=meta_conversation_changes.id_meta())
            expiration_datetime = meta_conversation_changes.expiration_datetime()
            if expiration_datetime:
                conversacion.change_expiration_datetime(expiration_datetime=expiration_datetime)

    def _actualizar_respuestas_desde(self, meta_changes):
        telefono_de_la_respuesta = meta_changes.phone_number()
        texto_de_la_respuesta = meta_changes.message()
        id_meta_del_operador = meta_changes.operator_meta()
        operador = self._operador_con_id(id_meta_del_operador)
        mensaje_con_telefono_de_prospecto = MessageInMeta.objects.with_any_phone_numbers(
            phone_numbers=self._variantes_de_telefono(
                telefono_prospecto=telefono_de_la_respuesta)).with_operator(operator=operador).last()
        if mensaje_con_telefono_de_prospecto:
            conversacion_prospecto = mensaje_con_telefono_de_prospecto.conversation()
        else:
            conversacion_prospecto = ConversationInMeta.new(operator=operador)
        mensaje_meta = MessageInMeta.new_received(meta_id=None, status=MessageInMeta.RECEIVED,
                                                  phone_number=telefono_de_la_respuesta,
                                                  conversation=conversacion_prospecto, text=texto_de_la_respuesta)
        prospecto = self._obtener_prospecto(telefono_prospecto=telefono_de_la_respuesta, operador=operador)
        if prospecto is not None:
            self._crear_nuevo_mensaje_a_prospecto(mensaje_meta, prospecto, MensajeDeWhatsappConProspecto.CLIENTE)
            self._notificador.notificar_nueva_respuesta_de(prospecto)
        else:
            raise RuntimeError(f'No existe un prospecto para el telefono {telefono_de_la_respuesta}')
        return mensaje_meta

    def _operador_con_id(self, id_meta_del_operador):
        try:
            operador = Operador.objects.get(id_operador=id_meta_del_operador)
        except Operador.DoesNotExist:
            raise ValidationError('No existen remitentes para asignar respuesta')
        return operador

    def _obtener_prospecto(self, telefono_prospecto, operador):
        variantes_de_telefono = self._variantes_de_telefono(telefono_prospecto)
        enlace = self._obtener_enlace_entre(operador, variantes_de_telefono)
        if enlace is not None:
            return enlace.obtener_prospecto()
        else:
            from prospectos.models import Prospecto
            return Prospecto.objects.con_algun_telefono_extra(
                telefonos=variantes_de_telefono).last()

    def _obtener_enlace_entre(self, operador, variantes_de_telefono):
        enlaces = self.enlaces_entre(operador=operador, telefonos_destinatarios=variantes_de_telefono)
        enlace = enlaces.ordenar_del_mas_recientemente_actualizado_al_mas_antiguo().last()
        return enlace

    def _variantes_de_telefono(self, telefono_prospecto):
        codigo_pais = '549'
        if telefono_prospecto.startswith(codigo_pais):
            telefono_sin_caracteristica_de_pais = re.sub(r'^' + codigo_pais, '', telefono_prospecto)
            return [telefono_prospecto, telefono_sin_caracteristica_de_pais]
        else:
            return [codigo_pais + telefono_prospecto, telefono_prospecto]

    def _enviar_mensajes_pendientes_si_la_ventana_esta_abierta(self, conversacion_meta):
        if conversacion_meta.is_open():
            self._enviar_mensajes_pendientes_de_la_conversacion(conversacion_meta)

    def _enviar_mensajes_pendientes_de_la_conversacion(self, conversacion_meta):
        mensajes_a_enviar = conversacion_meta.pending_messages()
        for mensaje_a_enviar in mensajes_a_enviar:
            self._enviar_mensaje_pendiente(mensaje_a_enviar)

    def _enviar_mensaje_pendiente(self, mensaje_a_enviar):
        telefono_destinatario = mensaje_a_enviar.phone_number()
        try:
            respuesta = self._servicio_de_meta.enviar_mensaje(
                telefono_destinatario=telefono_destinatario,
                texto=mensaje_a_enviar.text(), marca=None, dni=None,
                id_operador=mensaje_a_enviar.conversation().operator().obtener_id_operador(),
                url_notificacion=self._url_de_notificaciones_de_estado)
        except ClientConnectionError as error:
            self._log_error(error)
        else:
            self._log_info(f'Envio de mensaje pendiente a telefono {telefono_destinatario}', respuesta)
            id_de_meta = respuesta['messages'][0]['id']
            mensaje_a_enviar.change_status_to(mensaje_a_enviar.ACCEPTED)
            mensaje_a_enviar.change_meta_id(id_de_meta)

    def _log_error(self, error):
        if self._logger is not None:
            self._logger.error(error)

    def _log_info(self, detalle, respuesta_del_servicio):
        if self._logger is not None:
            self._logger.info(detalle)
            self._logger.info(respuesta_del_servicio)
