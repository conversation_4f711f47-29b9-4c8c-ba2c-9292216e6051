from datetime import timed<PERSON><PERSON>

from django.core.exceptions import ValidationError
from freezegun import freeze_time

from conversaciones.medios import MedioWhatsapp
from conversaciones.models import MensajeDeWhatsappConProspecto
from conversaciones.tests.whatsapp_usando_meta.conversacion_meta_fixture import ConversacionMetaFixture
from core.models import Sistema
from prospectos.models import Prospecto
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from testing.base import BaseFixturedTest
from testing.test_utils import reload_model


class TestConversacionConProspectoMedianteMeta(BaseFixturedTest):

    def setUp(self):
        super().setUp()
        self._conversacion_meta_fixture = ConversacionMetaFixture(self.fixture, self.creador_de_contexto)
        # Notificaciones
    def test_los_mensajes_en_espera_son_parte_de_las_conversaciones_de_delivery(self):
        # Dado / Parte del setup

        # Cuando
        self._conversacion_meta_fixture.crear_gestor_para_vendedor_uno().enviar_a(
            self._conversacion_meta_fixture.prospecto_de_vendedor_uno(),
            texto=self._conversacion_meta_fixture.texto_enviado_por_el_vendedor(), medio=MedioWhatsapp.nuevo())

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno(), cantidad_de_mensajes=1,
            tipo=MedioWhatsapp.tipo())

    def test_las_respuestas_del_destinatario_son_parte_de_las_conversaciones_en_delivery(self):
        # Dado
        self._conversacion_meta_fixture.crear_gestor_para_vendedor_uno().enviar_a(
            self._conversacion_meta_fixture.prospecto_de_vendedor_uno(),
            texto=self._conversacion_meta_fixture.texto_enviado_por_el_vendedor(), medio=MedioWhatsapp.nuevo())

        numero_de_telefono_del_remitente = '549' + self._conversacion_meta_fixture.telefono_uno()
        meta = self._conversacion_meta_fixture.operador().obtener_id_operador()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente, meta)

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta().actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno(), cantidad_de_mensajes=2,
            tipo=MedioWhatsapp.tipo())
        conversacion = self._conversacion_meta_fixture.crear_gestor_para_vendedor_uno().conversacion_multimedia_para(
            self._conversacion_meta_fixture.prospecto_de_vendedor_uno())
        respuesta_de_cliente = conversacion.mensajes()[0]
        self.assertTrue(respuesta_de_cliente.proveniente_de_cliente())
        self.assertEqual(self._conversacion_meta_fixture.texto_entrante_de_prospecto(), respuesta_de_cliente.obtener_texto())

    def test_al_recibir_una_respuesta_de_un_telefono_normalizado_se_asocia_al_prospecto_con_su_telefono_sin_normalizar(
            self):
        # Dado
        # Normalizado significa con el codigo de pais!
        numero_de_telefono_del_remitente = '549' + self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno().telefono_para_whatsapp()
        meta = self._conversacion_meta_fixture.operador().obtener_id_operador()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente, meta)

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta().actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_nuevo_de_vendedor_uno(), cantidad_de_mensajes=1,
            tipo=MedioWhatsapp.tipo())
        respuesta_de_cliente = MensajeDeWhatsappConProspecto.objects.filter(
            _prospecto=self._conversacion_meta_fixture.prospecto_nuevo_de_vendedor_uno()).last()
        self.assertTrue(respuesta_de_cliente.proveniente_de_cliente())
        self.assertEqual(self._conversacion_meta_fixture.texto_entrante_de_prospecto(), respuesta_de_cliente.obtener_texto())

        # TODO: VERIFICAR SI ES UN MENSAJE O DOS LOS QUE DEBERÍAN TENER EN LA CONVERSACION

    def test_al_recibir_una_respuesta_del_destinatario_y_no_existe_enlace_se_utiliza_el_prospecto_mas_nuevo_que_contenga_su_telefono(
            self):
        # Dado
        remitente = self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno().telefono_para_whatsapp()
        meta = self._conversacion_meta_fixture.operador().obtener_id_operador()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            remitente, meta)

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta().actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_no_existe_conversacion_via_medio_con(
            prospecto=self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno(), tipo=MedioWhatsapp.tipo())
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_nuevo_de_vendedor_uno(), cantidad_de_mensajes=1,
            tipo=MedioWhatsapp.tipo())
        respuesta_de_cliente = MensajeDeWhatsappConProspecto.objects.filter(
            _prospecto=self._conversacion_meta_fixture.prospecto_nuevo_de_vendedor_uno()).last()
        self.assertTrue(respuesta_de_cliente.proveniente_de_cliente())
        self.assertEqual(self._conversacion_meta_fixture.texto_entrante_de_prospecto(), respuesta_de_cliente.obtener_texto())

    def test_al_recibir_una_respuesta_del_destinatario_sin_operador_se_asigna_al_prospecto_con_enlace_con_actualizacion_mas_reciente(
            self):
        # Dado
        self._conversacion_meta_fixture.crear_gestor_para_vendedor_uno().enviar_a(
            prospecto=self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno(),
            texto=self._conversacion_meta_fixture.texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())
        remitente = self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno().telefono_para_whatsapp()
        meta = self._conversacion_meta_fixture.operador().obtener_id_operador()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            remitente, meta)

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta().actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno(), cantidad_de_mensajes=2,
            tipo=MedioWhatsapp.tipo())

    def test_al_recibir_una_respuesta_del_destinatario_se_utliza_el_prospecto_con_un_enlace(self):
        # Dado
        self._enviar_whatsapp_al_prospecto_vendedor_dos()
        remitente = self._conversacion_meta_fixture.telefono_uno()
        meta = self._conversacion_meta_fixture.operador().obtener_id_operador()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            remitente, meta)

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta().actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_dos(), cantidad_de_mensajes=2,
            tipo=MedioWhatsapp.tipo())
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_no_existe_conversacion_via_medio_con(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno(), tipo=MedioWhatsapp.tipo())

    def test_al_recibir_una_respuesta_del_destinatario_buscamos_el_enlace_actualizado_mas_recientemente(self):
        """En el enlace vigente es el último enlace actualizado"""
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._finalizar_prospecto_del_vendedor_uno()
        self._enviar_whatsapp_al_prospecto_vendedor_dos()
        self._finalizar_prospecto_del_vendedor_dos()
        self._reactivar_seguimiento_prospecto_del_vendedor_uno()
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        remitente = self._conversacion_meta_fixture.telefono_uno()
        meta = self._conversacion_meta_fixture.operador().obtener_id_operador()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            remitente, meta)

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta().actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno(), cantidad_de_mensajes=3,
            tipo=MedioWhatsapp.tipo())
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_dos(), cantidad_de_mensajes=1,
            tipo=MedioWhatsapp.tipo())

    def test_al_recibir_una_respuesta_no_contampla_como_receptor_un_telefono_extra_inactivo(
            self):
        # Dado
        telefono_nuevo = '48764401'
        self._configurar_telefono_prospecto_viejo_de_venedor_uno(telefono=telefono_nuevo)
        gestor = GestorDeProspecto.nuevo_para(rol=self._conversacion_meta_fixture.vendedor_uno())
        telefono_extra = gestor.agregar_telefono_extra_a_prospecto(
            self._conversacion_meta_fixture.prospecto_nuevo_de_vendedor_uno(),
            telefono=telefono_nuevo)
        gestor.toggle_telefono_extra_activo(prospecto=self._conversacion_meta_fixture.prospecto_nuevo_de_vendedor_uno(),
                                            id_telefono=telefono_extra.id)

        self._conversacion_meta_fixture.crear_gestor_para_vendedor_uno().enviar_a(
            self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno(),
            texto=self._conversacion_meta_fixture.texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        # Normalizado significa con el codigo de pais!
        numero_de_telefono_del_remitente = telefono_nuevo
        meta = self._conversacion_meta_fixture.operador().obtener_id_operador()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente, meta)

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta().actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno(), cantidad_de_mensajes=2,
            tipo=MedioWhatsapp.tipo())

    def test_al_tener_mas_de_un_numero_de_telefono_se_asocia_segun_un_telefono_activo(self):
        # Dado
        gestor = GestorDeProspecto.nuevo_para(rol=self._conversacion_meta_fixture.vendedor_uno())
        gestor.agregar_telefono_extra_a_prospecto(self._conversacion_meta_fixture.prospecto_de_vendedor_uno(),
                                                  telefono='48764401')
        self._conversacion_meta_fixture.crear_gestor_para_vendedor_uno().enviar_a(
            self._conversacion_meta_fixture.prospecto_de_vendedor_uno(),
            texto=self._conversacion_meta_fixture.texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        numero_de_telefono_del_remitente = '48764401'
        meta = self._conversacion_meta_fixture.operador().obtener_id_operador()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente, meta)

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta().actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno(), cantidad_de_mensajes=2,
            tipo=MedioWhatsapp.tipo())
        respuesta_de_cliente = MensajeDeWhatsappConProspecto.objects.filter(
            _prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno()).last()
        self.assertTrue(respuesta_de_cliente.proveniente_de_cliente())
        self.assertEqual(self._conversacion_meta_fixture.texto_entrante_de_prospecto(), respuesta_de_cliente.obtener_texto())

    def test_al_recibir_una_respuesta_no_contampla_como_receptor_un_telefono_principal_inactivo(
            self):
        # Dado
        telefono_nuevo = '48764401'
        self._configurar_telefono_prospecto_viejo_de_venedor_uno(telefono=telefono_nuevo)
        gestor = GestorDeProspecto.nuevo_para(rol=self._conversacion_meta_fixture.vendedor_uno())
        gestor.agregar_telefono_extra_a_prospecto(
            prospecto=self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno(), telefono=telefono_nuevo)
        gestor.toggle_telefono_activo(prospecto=self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno())

        self._conversacion_meta_fixture.crear_gestor_para_vendedor_uno().enviar_a(
            prospecto=self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno(),
            texto=self._conversacion_meta_fixture.texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        meta = self._conversacion_meta_fixture.operador().obtener_id_operador()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            telefono_nuevo, meta)

        # Cuando
        self._conversacion_meta_fixture.canal_via_meta().actualizar_desde(notificacion_con_respuesta_del_destinatario)

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno(),
            cantidad_de_mensajes=2,
            tipo=MedioWhatsapp.tipo())
        conversacion = self._conversacion_meta_fixture.canal_via_meta().conversacion_con_operador(
            telefono_destinatario=telefono_nuevo,
            operador=self._conversacion_meta_fixture.operador())
        self.assertTrue(conversacion.is_open())

    def test_al_iniciar_una_conversacion_se_crea_un_enlace_entre_operador_receptor_prospecto(self):
        # Dado Parte del setup

        # Cuando
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        # Entonces
        self.assertTrue(self._conversacion_meta_fixture.canal_via_meta().existe_un_enlace_entre(
            operador=self._conversacion_meta_fixture.canal_via_meta().operador_para_prospecto(
                self._conversacion_meta_fixture.prospecto_de_vendedor_uno()),
            telefono_destinatario=self._conversacion_meta_fixture.telefono_uno(),
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno()))

    def test_si_ya_existe_un_enlace_para_otro_vendedor_lanza_error(self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        # Cuando / Entonces
        self.assertEqual(self._conversacion_meta_fixture.prospecto_de_vendedor_uno().telefono_para_whatsapp(),
                         self._conversacion_meta_fixture.prospecto_de_vendedor_dos().telefono_para_whatsapp())
        self.assertRaisesMessage(ValidationError, 'No es posible iniciar conversación con este número',
                                 self._enviar_whatsapp_al_prospecto_vendedor_dos)

    def test_si_ya_existe_un_enlace_con_otro_vendedor_con_variante_de_numero_lanza_error(self):
        # Dado
        self._configurar_al_prospecto_vendedor_dos_con_mismo_telefono_prospecto_vendedor_uno_con_codigo_pais()
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        # Cuando / Entonces
        self.assertRaisesMessage(ValidationError, 'No es posible iniciar conversación con este número',
                                 self._enviar_whatsapp_al_prospecto_vendedor_dos)

    def test_cuando_el_vendedor_tiene_enlace_con_el_prospecto_puede_enviar_multiples_mensajes(self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._recibir_respuesta_del_prospecto_vendedor_uno()

        # Cuando
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno(), cantidad_de_mensajes=3,
            tipo=MedioWhatsapp.tipo())

    def test_solo_el_vendedor_con_un_enlace_previo_puede_enviarle_whatsapp_a_prospectos_nuevos_con_mismo_destinatario(
            self):
        # Dado
        self._conversacion_meta_fixture.crear_gestor_para_vendedor_uno().enviar_a(
            prospecto=self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno(),
            texto=self._conversacion_meta_fixture.texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        # Cuando
        self._conversacion_meta_fixture.crear_gestor_para_vendedor_uno().enviar_a(
            prospecto=self._conversacion_meta_fixture.prospecto_nuevo_de_vendedor_uno(),
            texto=self._conversacion_meta_fixture.texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_nuevo_de_vendedor_uno(), cantidad_de_mensajes=1,
            tipo=MedioWhatsapp.tipo())

    def test_si_al_enviar_un_mensaje_ya_existe_un_enlace_debe_actualizarse_la_fecha_de_ultima_actualizacion(self):
        with freeze_time("2025-03-15 09:00:00") as tiempo_freezado:
            # Dado
            self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
            tiempo_freezado.tick(timedelta(hours=24) + timedelta(seconds=1))

            # Cuando
            self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

            # Entonces
            enlaces = self._conversacion_meta_fixture.canal_via_meta().enlaces_entre(
                operador=self._conversacion_meta_fixture.canal_via_meta().operador_para_prospecto(
                    self._conversacion_meta_fixture.prospecto_de_vendedor_uno()),
                telefonos_destinatarios=[self._conversacion_meta_fixture.telefono_uno()])
            self.assertEqual(enlaces.count(), 1)
            enlace = enlaces.first()
            self.assertTrue(enlace.tiene_fecha_ultima_actualizacion(self._conversacion_meta_fixture.calendario().now()))

    def test_al_enviar_un_nuevo_mensaje_al_mismo_destinario_pero_con_codigo_pais_actualiza_el_enlace_existente(self):
        with freeze_time("2025-03-15 09:00:00") as tiempo_freezado:
            # Dado
            self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
            tiempo_freezado.tick(timedelta(hours=24) + timedelta(seconds=1))
            self._configurar_al_prospecto_vendedor_uno_con_mismo_telefono_agregando_codigo_pais()

            # Cuando
            self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()

            # Entonces
            enlaces = self._conversacion_meta_fixture.canal_via_meta().enlaces_entre(
                operador=self._conversacion_meta_fixture.canal_via_meta().operador_para_prospecto(
                    self._conversacion_meta_fixture.prospecto_de_vendedor_uno()),
                telefonos_destinatarios=[self._conversacion_meta_fixture.telefono_uno()])
            self.assertEqual(enlaces.count(), 1)
            enlace = enlaces.first()
            self.assertTrue(enlace.tiene_fecha_ultima_actualizacion(self._conversacion_meta_fixture.calendario().now()))

    def test_enlaces_previos_caducos_por_prospectos_finalizados_no_restringen_envio_de_mensajes_a_otro_vendedor(self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._finalizar_prospecto_del_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_dos()

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_dos(), cantidad_de_mensajes=1,
            tipo=MedioWhatsapp.tipo())

    def test_si_el_prospecto_esta_finalizado_no_permite_enviar_mensaje_por_meta(self):
        """Si el prospecto está finalizado no permite enviar mensaje // Sujeto a revisión con Nico"""
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._finalizar_prospecto_del_vendedor_uno()

        # Cuando / Entonces
        self.assertRaisesMessage(ValidationError, 'No es posible enviar un mensaje a este número',
                                 self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno)

    def test_enlaces_previos_caducos_por_prospectos_vendidos_no_restringen_envio_de_mensajes_a_otro_vendedor(self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._cargar_venta_al_prospecto_del_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_dos()

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_dos(), cantidad_de_mensajes=1,
            tipo=MedioWhatsapp.tipo())

    def test_cuando_los_enlaces_previos_estan_caducos_con_ventana_cerrada_otro_vendedor_puede_generar_un_nuevo_enlace(
            self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._finalizar_prospecto_del_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_dos()

        # Entonces
        self.assertTrue(self._conversacion_meta_fixture.canal_via_meta().existe_un_enlace_entre(
            operador=self._conversacion_meta_fixture.canal_via_meta().operador_para_prospecto(
                self._conversacion_meta_fixture.prospecto_de_vendedor_uno()),
            telefono_destinatario=self._conversacion_meta_fixture.telefono_uno(),
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_dos()))

    def test_cuando_los_enlaces_previos_estan_caducos_con_ventana_abierta_otro_vendedor_puede_generar_un_nuevo_enlace(
            self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._recibir_respuesta_del_prospecto_vendedor_uno()
        self._finalizar_prospecto_del_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_dos()

        # Entonces
        self.assertTrue(self._conversacion_meta_fixture.canal_via_meta().existe_un_enlace_entre(
            operador=self._conversacion_meta_fixture.canal_via_meta().operador_para_prospecto(
                self._conversacion_meta_fixture.prospecto_de_vendedor_uno()),
            telefono_destinatario=self._conversacion_meta_fixture.telefono_uno(),
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_dos()))

    def test_no_permite_enviar_whatsapp_si_existe_mas_de_un_enlace_posterior_entre_operador_y_destinatario(self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._finalizar_prospecto_del_vendedor_uno()
        self._enviar_whatsapp_al_prospecto_vendedor_dos()
        self._reactivar_seguimiento_prospecto_del_vendedor_uno()

        # Cuando / Entonces
        self.assertRaisesMessage(ValidationError, 'No es posible iniciar conversación con este número',
                                 self._conversacion_meta_fixture.crear_gestor_para_vendedor_uno().enviar_a,
                                 prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno(),
                                 texto=self._conversacion_meta_fixture.texto_enviado_por_el_vendedor(),
                                 medio=MedioWhatsapp.nuevo())

    def test_solo_considera_vigente_al_ultimo_enlace_actualizado_entre_operador_y_destinatario(self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._finalizar_prospecto_del_vendedor_uno()
        self._enviar_whatsapp_al_prospecto_vendedor_dos()
        self._reactivar_seguimiento_prospecto_del_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_dos()

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_dos(), cantidad_de_mensajes=2,
            tipo=MedioWhatsapp.tipo())

    def test_enlaces_previos_caducos_por_dias_de_aniguedad_vendidos_no_restringen_envio_de_mensajes_a_otro_vendedor(
            self):
        with freeze_time("2025-03-15 09:00:00") as tiempo_freezado:
            # Dado
            self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
            tiempo_freezado.tick(timedelta(
                days=Sistema.instance().tiempo_antiguedad_maximo_para_enlace_vigente) + timedelta(seconds=1))

            # Cuando
            self._enviar_whatsapp_al_prospecto_vendedor_dos()

            # Entonces
            self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
                prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_dos(), cantidad_de_mensajes=1,
                tipo=MedioWhatsapp.tipo())

    def test_los_enlaces_caducan_cuando_sus_prospectos_dejan_de_estar_asignados_a_un_vendedor(self):
        # Dado
        self._conversacion_meta_fixture.enviar_mensaje_whatsapp_a_prospecto_uno_de_vendedor_uno()
        self._rechazar_prospecto_de_vendedor_uno()

        # Cuando
        self._enviar_whatsapp_al_prospecto_vendedor_dos()

        # Entonces
        self._conversacion_meta_fixture.inicializar_notificaciones_test_helper(self).assert_conversacion_con_cantidad_de_mensajes(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_dos(), cantidad_de_mensajes=1,
            tipo=MedioWhatsapp.tipo())

    def _recibir_respuesta_del_prospecto_vendedor_uno(self):
        numero_de_telefono_del_remitente = self._conversacion_meta_fixture.telefono_uno()
        meta = self._conversacion_meta_fixture.operador().obtener_id_operador()
        notificacion_con_respuesta_del_destinatario = self._conversacion_meta_fixture.respuesta_del_usuario_a_un_mensaje(
            numero_de_telefono_del_remitente, meta)

        self._conversacion_meta_fixture.canal_via_meta().actualizar_desde(notificacion_con_respuesta_del_destinatario)

    def _enviar_whatsapp_al_prospecto_vendedor_dos(self):
        self._conversacion_meta_fixture.crear_gestor_para_vendedor_dos().enviar_a(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_dos(),
            texto=self._conversacion_meta_fixture.texto_enviado_por_el_vendedor(),
            medio=MedioWhatsapp.nuevo())

    def _configurar_telefono_prospecto_viejo_de_venedor_uno(self, telefono):
        self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno().telefono = telefono
        self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno().save()
        reload_model(self._conversacion_meta_fixture.prospecto_viejo_de_vendedor_uno())

    def _configurar_al_prospecto_vendedor_dos_con_mismo_telefono_prospecto_vendedor_uno_con_codigo_pais(self):
        prospecto_vendedor_dos_largo = '549' + self._conversacion_meta_fixture.telefono_uno()
        self._conversacion_meta_fixture.prospecto_de_vendedor_dos().telefono = prospecto_vendedor_dos_largo
        self._conversacion_meta_fixture.prospecto_de_vendedor_dos().save()

    def _configurar_al_prospecto_vendedor_uno_con_mismo_telefono_agregando_codigo_pais(self):
        telefono_con_codigo_pais = '549' + self._conversacion_meta_fixture.prospecto_de_vendedor_uno().telefono
        self._conversacion_meta_fixture.prospecto_de_vendedor_uno().telefono = telefono_con_codigo_pais
        self._conversacion_meta_fixture.prospecto_de_vendedor_uno().save()

    def _cargar_venta_al_prospecto_del_vendedor_uno(self):
        gestor_del_prospecto = GestorDeProspecto.nuevo_para(self._conversacion_meta_fixture.vendedor_uno())
        gestor_del_prospecto.cargar_venta(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno(), marca='ford', modelo='ecosport',
            fecha_de_realizacion=self._conversacion_meta_fixture.calendario().now(), numero_de_contrato='1', precio=100000)

    def _finalizar_prospecto_del_vendedor_uno(self):
        gestor_del_prospecto = GestorDeProspecto.nuevo_para(self._conversacion_meta_fixture.vendedor_uno())
        gestor_del_prospecto.finalizar_prospecto(prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno())

    def _finalizar_prospecto_del_vendedor_dos(self):
        gestor_del_prospecto = GestorDeProspecto.nuevo_para(self._conversacion_meta_fixture.vendedor_dos())
        gestor_del_prospecto.finalizar_prospecto(prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_dos())

    def _reactivar_seguimiento_prospecto_del_vendedor_uno(self):
        gestor_del_prospecto = GestorDeProspecto.nuevo_para(self._conversacion_meta_fixture.vendedor_uno())
        gestor_del_prospecto.reactivar_seguimiento(
            prospecto=self._conversacion_meta_fixture.prospecto_de_vendedor_uno())

    def _rechazar_prospecto_de_vendedor_uno(self):
        gestor = GestorDeProspecto.nuevo_para(rol=self._conversacion_meta_fixture.supervisor_uno())
        prospectos = Prospecto.objects.con_ids([self._conversacion_meta_fixture.prospecto_de_vendedor_uno().id])
        gestor.rechazar_prospectos(prospectos=prospectos)
