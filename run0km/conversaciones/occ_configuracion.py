# coding=utf-8
from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import get_template
from django.utils.encoding import force_str

from vendedores.gestor import GestorDeVendedores


class OCCConfiguracion(object):
    """
        TODO: La responsabilidad de habilitar el servicio a cada vendedor deberia ser del cargo supervisor,
         pero lo tenemos modelado con string, reificar el concepto y pasar esta responsabilidad
    """
    def __init__(self, **kwargs):
        self.gestor_de_vendedores = GestorDeVendedores.nuevo()

    def tiene_permiso_para_modificar_servicio_whatsapp(self, user):
        return user.is_vendedor() and user.vendedor.es_supervisor()

    def habilitar_servicio_whatsapp(self, supervisor):
        concesionaria = supervisor.concesionaria
        config_de_servicios = concesionaria.configuracion_de_servicios()
        if not config_de_servicios.whatsapp_habilitado():
            email = MailPorPedidoDeServicioDeOCC(supervisor)
            email.enviar_whatsApp()

        for vendedor in supervisor.vendedores.all():
            vendedor.habilitar_whatsapp()

    def deshabilitar_servicio_whatsapp(self, supervisor):
        for vendedor in supervisor.vendedores.all():
            vendedor.deshabilitar_whatsapp()
            vendedor.deshabilitar_mensaje_bienvenida()
            vendedor.deshabilitar_mensaje_incontactables()

    # Servicio de Chat
    def habilitar_servicio_chat(self, vendedor):
        self.gestor_de_vendedores.configurar_habilitacion_de_chat(vendedor=vendedor, chat_habilitado=True)

    def deshabilitar_servicio_chat(self, vendedor):
        self.gestor_de_vendedores.configurar_habilitacion_de_chat(vendedor=vendedor, chat_habilitado=False)

    def puede_modificar_servicio_de_chat(self, vendedor):
        return vendedor.configuracion_de_servicios().puede_modificar_servicio_de_chat()

    def tiene_servicio_chat_habilitado(self, vendedor):
        return vendedor.configuracion_de_servicios().habilitado_para_chatear()

    # Servicio de SMS
    def tiene_servicio_whatsapp_habilitado(self, supervisor):
        return supervisor.vendedores.filter(configuracion_servicios___whatsapp_habilitado=True).exists()

    def tiene_permiso_para_modificar_servicio_sms(self, user):
        return user.is_vendedor() and user.vendedor.es_supervisor()

    def habilitar_servicio_sms(self, supervisor):
        if not supervisor.concesionaria.configuracion_de_servicios().sms_habilitado():
            email = MailPorPedidoDeServicioDeOCC(supervisor)
            email.enviar_sms()

        supervisor.habilitar_sms()
        for vendedor in supervisor.vendedores.all():
            vendedor.habilitar_sms()

    def deshabilitar_servicio_sms(self, supervisor):
        supervisor.deshabilitar_sms()
        for vendedor in supervisor.vendedores.all():
            vendedor.deshabilitar_sms()

    def tiene_servicio_sms_habilitado(self, supervisor):
        return supervisor.vendedores.filter(configuracion_servicios___sms_habilitado=True).exists()

    # Servicio de Redes
    def tiene_permiso_para_modificar_servicio_redes(self, user):
        return user.is_vendedor() and user.vendedor.es_supervisor()

    def habilitar_servicio_redes(self, supervisor):
        self.gestor_de_vendedores.configurar_habilitacion_de_las_redes(vendedor=supervisor, redes_habilitado=True)
        for vendedor in supervisor.vendedores.all():
            self.gestor_de_vendedores.configurar_habilitacion_de_las_redes(vendedor=vendedor, redes_habilitado=True)

    def deshabilitar_servicio_redes(self, supervisor):
        self.gestor_de_vendedores.configurar_habilitacion_de_las_redes(vendedor=supervisor, redes_habilitado=False)
        for vendedor in supervisor.vendedores.all():
            self.gestor_de_vendedores.configurar_habilitacion_de_las_redes(vendedor=vendedor, redes_habilitado=False)

    def tiene_servicio_redes_habilitado(self, supervisor):
        return supervisor.vendedores.filter(configuracion_servicios___redes_habilitado=True).exists()

    def tiene_servicio_propuestas_habilitado(self, rol):
        return rol.configuracion_de_servicios().puede_crear_propuestas()

    def tiene_permiso_para_modificar_servicio_mensaje_bienvenida(self, user):
        return user.is_vendedor()

    def tiene_permiso_para_modificar_servicio_mensaje_incontactables(self, user):
        return user.is_vendedor()

    def tiene_permiso_para_modificar_servicio_de_propuestas(self, user):
        return user.role().configuracion_de_servicios().puede_modificar_servicio_de_propuestas()

    def tiene_permiso_para_modificar_servicio_notificaciones(self, user):
        return True

    def habilitar_servicio_mensaje_bienvenida(self, vendedor):
        vendedor.configuracion_de_servicios().habilitar_mensaje_bienvenida()

    def deshabilitar_servicio_mensaje_bienvenida(self, vendedor):
        vendedor.configuracion_de_servicios().deshabilitar_mensaje_bienvenida()

    def habilitar_servicio_mensaje_incontactables(self, vendedor):
        vendedor.configuracion_de_servicios().habilitar_mensaje_incontactables()

    def habilitar_creacion_de_propuestas(self, rol):
        rol.configuracion_de_servicios().habilitar_creacion_de_propuestas()

    def habilitar_servicio_notificaciones(self, vendedor):
        self.gestor_de_vendedores.configurar_habilitacion_de_notificaciones(vendedor=vendedor,
                                                                            notificaciones_habilitadas=True)

    def deshabilitar_servicio_mensaje_incontactables(self, vendedor):
        vendedor.configuracion_de_servicios().deshabilitar_mensaje_incontactables()

    def deshabilitar_servicio_notificaciones(self, vendedor):
        self.gestor_de_vendedores.configurar_habilitacion_de_notificaciones(vendedor=vendedor,
                                                                            notificaciones_habilitadas=False)

    def deshabilitar_creacion_de_propuestas(self, rol):
        rol.configuracion_de_servicios().deshabilitar_creacion_de_propuestas()

    def tiene_servicio_mensaje_bienvenida_habilitado(self, vendedor):
        return vendedor.configuracion_de_servicios().mensaje_bienvenida_habilitado()

    def puede_modificar_servicio_mensaje_bienvenida(self, vendedor):
        return vendedor.configuracion_de_servicios().habilitado_para_mensajear_por_whatsapp()

    def tiene_servicio_mensaje_incontactables(self, vendedor):
        return vendedor.configuracion_de_servicios().servicio_incontactables_habilitado()

    def puede_modificar_servicio_incontactables(self, vendedor):
        return vendedor.configuracion_de_servicios().habilitado_para_mensajear_por_whatsapp()

    def vendedores_pueden_crear_propuestas(self, supervisor):
        return supervisor.configuracion_de_servicios().vendedores_pueden_crear_propuestas()

    # Llamados por VoIP
    def tiene_permiso_para_modificar_servicio_de_llamados(self, rol):
        return rol.configuracion_de_servicios().tiene_permiso_para_modificar_servicio_de_llamados()

    def staff_a_cargo_puede_utilizar_servicio_de_llamados(self, rol):
        return rol.configuracion_de_servicios().staff_a_cargo_puede_utilizar_servicio_de_llamados()

    def habilitar_servicio_de_llamados_a_staff_a_cargo_de(self, rol):
        return rol.configuracion_de_servicios().habilitar_servicio_de_llamados_a_staff_a_cargo()

    def deshabilitar_servicio_de_llamados_a_staff_a_cargo_de(self, rol):
        return rol.configuracion_de_servicios().deshabilitar_llamados_de_llamados_a_staff_a_cargo()


class MailPorPedidoDeServicioDeOCC(object):
    def __init__(self, supervisor):
        self.supervisor = supervisor

    def enviar_whatsApp(self):
        self._enviar('WhatsApp', settings.SUBJECT_PEDIDO_SERVICIO_WHATSAPP)

    def enviar_chat(self):
        self._enviar('Chat', settings.SUBJECT_PEDIDO_SERVICIO_CHAT)

    def enviar_sms(self):
        self._enviar('SMS', settings.SUBJECT_PEDIDO_SERVICIO_SMS)

    def _enviar(self, servicio, subject):
        nombre = self.supervisor.user.get_full_name()
        concesionaria = force_str(self.supervisor.concesionaria)

        texto = "El supervisor %s ha activado el servicio de %s para sus vendedores, pero " \
                "la concesionaria %s aún no tiene lo habilitado." % (nombre, servicio, concesionaria)
        template = get_template('emails/mail_pedido_habilitar_servicio_occ.html')

        context = {'nombre': nombre,
                   'servicio': servicio,
                   'concesionaria': concesionaria,
                   }
        html_msg = template.render(context)

        email = EmailMultiAlternatives(subject % (nombre, concesionaria),
                                       texto,
                                       settings.DEFAULT_FROM_EMAIL,
                                       [settings.EMAIL_LOGISTICA])
        email.attach_alternative(html_msg, "text/html")
        email.send()
