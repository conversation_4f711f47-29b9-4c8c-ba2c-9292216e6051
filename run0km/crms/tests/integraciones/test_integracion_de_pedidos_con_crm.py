from lib.api_client import UnexpectedStatus, ClientErrorResponse
from crms.models import ConfiguracionDeTecnom, ConfiguracionDeMaipu, ConfiguracionDePilot, ConfiguracionDeSirena, \
    ConfiguracionDeGoogleSpreadsheet
from prospectos.models import LogDeErrorDeCRM, Marca, Modelo, TelefonoExtra
from prospectos.models.gestor.gestor_de_prospecto import GestorDeProspecto
from prospectos.tests.distribucion.pedidos.test_pedidos_core import PedidosTest
from testing.test_utils import reload_model


class CRMSendRequestMock(object):
    DEFAULT_REQUEST = 'default request'
    DEFAULT_RESPONSE = 'default response'
    DEFAULT_MENSAJE = 'default mensaje'
    DEFAULT_DATA = 'default data'

    def succefully(self, request):
        pass

    def fail_unexpected_status(self, request):
        raise UnexpectedStatus(message=self.DEFAULT_MENSAJE, request=self.DEFAULT_REQUEST,
                               response=self.DEFAULT_RESPONSE)

    # noinspection PyUnusedLocal
    def fail_response_error(self, request):
        fake_response = type('FakeResponse', (object,), {})()
        fake_response.content = self.DEFAULT_RESPONSE
        raise ClientErrorResponse(message=self.DEFAULT_MENSAJE,
                                  data=self.DEFAULT_DATA,
                                  request=self.DEFAULT_REQUEST,
                                  response=fake_response)


class IntegracionDePedidosConCRMTest(PedidosTest):

    # Tests Sugeridos
    # def test_cuando_el_crm_responde_status_code_inesperado_se_asigna_el_prospecto_al_pedido_y_se_registrar_el_error(
    #         self, send_request_mock, mock_on_commit_func):

    # def test_cuando_el_crm_responde_error_se_asigna_el_prospecto_al_pedido_y_se_registrar_el_error(
    #         self, send_request_mock, mock_on_commit_func)

    # def test_cuando_el_crm_responde_exito_se_asigna_el_prospecto_y_no_se_regsitran_errores(
    # self, enviar_prospecto_mock, mock_on_commit_func):

    def setUp(self):
        super(IntegracionDePedidosConCRMTest, self).setUp()
        LogDeErrorDeCRM.objects.all().delete()
        self.configuracion = self._crear_configuracion()

    def _crear_configuracion(self):
        raise NotImplementedError('Subclass responsibility')

    def _request_enviado_para(self, prospecto, configuracion):
        """
            No valida la generacion del request esto es validado en
                test_la_generacion_de_request_desde_prospecto_se_realiza_correctamente
        """
        return configuracion.integrador_de_prospectos().generar_lead_para(prospecto)

    def _crear_pedido_con_integracion_para(self, configuracion):
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(
            vendedor=self.vendedor_uno,
            campania=self.campania_uno,
        )
        self.creador_de_contexto.crear_opcion_crm_para_configuracion(pedido, configuracion=configuracion)
        return pedido

    def _crear_prospecto(self):
        celular = '11778899'
        email = '<EMAIL>'
        nombre = 'nombre_1'
        nombre_de_marca = 'Marqua_1'
        nombre_de_modelos = 'Modelo_1'
        mensaje = 'hola que tal'
        campo_extra = {'transcriptUrl': 'http://www.google.com'}
        prospecto = self._crear_prospecto_con(
            celular, email, nombre, nombre_de_marca, [nombre_de_modelos], mensaje=mensaje,
            campos_extra=campo_extra)
        return prospecto

    def _crear_prospecto_con(self, celular, email, nombre, nombre_de_marca, nombres_de_modelos,
                             mensaje='', telefono_extra=None, campos_extra=None):
        marca = self._crear_marcas_y_modelos(
            nombre_de_marca=nombre_de_marca, nombres_de_modelos=nombres_de_modelos)
        modelos = marca.modelos()
        prospecto = self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=self.vendedor_uno, supervisor=self.vendedor_uno.responsable(), telefono=celular,
            mensaje=mensaje, email=email, es_telefono_movil=True, nombre=nombre, proveedor='proveedor_1')
        if telefono_extra:
            TelefonoExtra.nuevo(prospecto=prospecto, vendedor=self.vendedor_uno, prefijo='', telefono=telefono_extra)

        if campos_extra:
            for nombre, valor in campos_extra.items():
                self.creador_de_contexto.agregar_campo_extra_a(prospecto, nombre, valor)

        gestor = GestorDeProspecto.nuevo_para(rol=self.vendedor_uno)
        gestor.reemplazar_marca_de(prospecto=prospecto, marca=marca)
        gestor.cambiar_modelos_de(prospecto=prospecto, modelos=modelos)
        prospecto = reload_model(prospecto)
        return prospecto

    def _crear_marcas_y_modelos(self, nombre_de_marca, nombres_de_modelos):
        marca = Marca.nueva_no_normalizada(nombre=('%s' % nombre_de_marca), identificador=37497623, logo='',
                                           codigo=nombre_de_marca, habilitada=True)
        for nombre in nombres_de_modelos:
            Modelo.nuevo(nombre=nombre, codigo=nombre, marca=marca)
        return marca

    def _assert_no_se_registro_ningun_error(self):
        self.assertFalse(LogDeErrorDeCRM.objects.exists())

    def _assert_se_registro_error_de_crm(self, tipo_de_error, prospecto, pedido):
        self.assertEqual(LogDeErrorDeCRM.objects.count(), 1)
        log = LogDeErrorDeCRM.objects.first()
        self.assertEqual(log.tipo, tipo_de_error)
        self.assertEqual(str(log.request), CRMSendRequestMock.DEFAULT_REQUEST)
        self.assertEqual(str(log.response), CRMSendRequestMock.DEFAULT_RESPONSE)
        self.assertEqual(log.descripcion, CRMSendRequestMock.DEFAULT_MENSAJE)
        metadata_esperada = {
            'prospecto_id': prospecto.id,
            'opcion_id': self._cliente_crm_de(pedido).id,
        }
        self.assertEqual(log.obtener_metadata(), metadata_esperada)

    def _assert_cliente_crm_fue_llamado(self, send_request_mock):
        self.assertEqual(send_request_mock.call_count, 1)

    def _assert_envio_a_crm_con(self, enviar_prospecto_mock, prospecto):
        prospecto.refresh_from_db()
        request = self._request_enviado_para(prospecto, configuracion=self.configuracion)
        enviar_prospecto_mock.assert_called_once_with(request)

    def _crear_configuracion_tecnom(self, nombre_de_proveedor='', vendor_email=None,
                                    vendor_name='<EMAIL>', subdominio='one'):
        configuracion = ConfiguracionDeTecnom.nuevo(
            descripcion='Test', usuario='hola', contrasenia='aloh', vendor_name=vendor_name,
            sub_dominio=subdominio, vendor_email=vendor_email, nombre_de_proveedor=nombre_de_proveedor
        )
        return configuracion

    def _crear_configuracion_maipu(self):
        return ConfiguracionDeMaipu.nuevo(descripcion='Por defecto Testing')

    def _crear_configuracion_pilot(self):
        return ConfiguracionDePilot.nuevo(
            descripcion='por defecto', appkey='1', suborigin_id='2', proveedor='carlos',
            business_type=ConfiguracionDePilot.PLAN_DE_AHORRO)

    def _crear_configuracion_sirena(self):
        return ConfiguracionDeSirena.nuevo(descripcion='Por defecto Testing')

    def _crear_configuracion_google_spreadsheet(self):
        return ConfiguracionDeGoogleSpreadsheet.nuevo(descripcion='Por defecto Testing')

    def _cliente_crm_de(self, pedido):
        return pedido.integraciones_con_crms_con_nombre(self.configuracion.nombre_del_crm())[0]

