import mock
from django.test import override_settings

from lib.api_client import UnexpectedStatus, ClientErrorResponse
from crms.tests.integraciones.test_integracion_de_pedidos_con_crm import IntegracionDePedidosConCRMTest, \
    CRMSendRequestMock
from prospectos.tests.distribucion.pedidos.test_pedidos_core import mock_on_commit


@override_settings(PILOT_CODIGO_DE_ORIGEN='1')
@override_settings(PILOT_API_KEY='key2')
@override_settings(PILOT_DEBUG=True)
@mock.patch('django.db.transaction.on_commit', side_effect=mock_on_commit)
class IntegracionDePedidosDeProspectoConPilotTest(IntegracionDePedidosConCRMTest):
    @mock.patch("lib.client_pilot.PilotSender.call", side_effect=CRMSendRequestMock().fail_unexpected_status)
    def test_cuando_el_crm_responde_status_code_inesperado_se_asigna_el_prospecto_al_pedido_y_se_registrar_el_error(
            self, send_request_mock, mock_on_commit_func):
        # Dado
        pedido = self._crear_pedido_con_integracion_para(self.configuracion)
        prospecto = self._crear_nuevo_prospecto()

        # Cuando
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)

        # Entonces
        self._assert_cliente_crm_fue_llamado(send_request_mock)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(
            prospecto.id, pedido.id, a_vendedor=pedido.vendedor, credito_consumido=2 + self.valor_de_categoria)
        self._assert_se_registro_error_de_crm(tipo_de_error=UnexpectedStatus.error_type(), prospecto=prospecto,
                                              pedido=pedido)

    @mock.patch("lib.client_pilot.PilotSender.call", side_effect=CRMSendRequestMock().fail_response_error)
    def test_cuando_el_crm_responde_error_se_asigna_el_prospecto_al_pedido_y_se_registrar_el_error(
            self, send_request_mock, mock_on_commit_func):
        # Dado
        pedido = self._crear_pedido_con_integracion_para(self.configuracion)
        prospecto = self._crear_nuevo_prospecto()

        # Cuando
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)

        # Entonces
        self._assert_cliente_crm_fue_llamado(send_request_mock)
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(
            prospecto.id, pedido.id, a_vendedor=pedido.vendedor, credito_consumido=2 + self.valor_de_categoria)
        self._assert_se_registro_error_de_crm(tipo_de_error=ClientErrorResponse.error_type(), prospecto=prospecto,
                                              pedido=pedido)

    @mock.patch("lib.client_pilot.PilotSender.call", side_effect=CRMSendRequestMock().succefully)
    def test_cuando_el_crm_responde_exito_se_asigna_el_prospecto_y_no_se_regsitran_errores(
            self, enviar_prospecto_mock, mock_on_commit_func):
        # Dado
        pedido = self._crear_pedido_con_integracion_para(self.configuracion)
        prospecto = self._crear_nuevo_prospecto()

        # Cuando
        self.admin_de_pedidos.asignar_prospecto_automaticamente(prospecto)

        # Entonces
        self._assert_asignacion_de_prospecto_y_consumo_en_pedido(
            prospecto.id, pedido.id, a_vendedor=pedido.vendedor, credito_consumido=2 + self.valor_de_categoria)
        self._assert_no_se_registro_ningun_error()
        self._assert_envio_a_crm_con(enviar_prospecto_mock, prospecto)

    def _crear_configuracion(self):
        return self._crear_configuracion_pilot()
