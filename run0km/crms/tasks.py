from importlib.metadata import metadata

from lib.api_client import ClientComunicationError, ClientValidationError
from core.locker.errors import ResourceLockedError
from crms.models import OpcionClienteCRM
from prospectos.models import Prospecto, LogDeErrorDeCRM
from run0km import celery_app as app


@app.task(name="crms.tasks.enviar_a_crm", bind=True, default_retry_delay=20, max_retries=5, retry_backoff=3)
def enviar_a_crm(self, prospecto_pk, opcion_pk):
    prospecto = Prospecto.objects.get(pk=prospecto_pk)
    cliente_crm = OpcionClienteCRM.con_id(identificador=opcion_pk)
    metadata = {
        'prospecto_id': prospecto_pk,
        'opcion_id': opcion_pk
    }
    try:
        cliente_crm.enviar_prospecto(prospecto)
    except ClientValidationError as exc:
        LogDeErrorDeCRM.guardar_log(exc, metadata=metadata)
    except ClientComunicationError as exc:
        LogDeErrorDeCRM.guardar_error_de_comunicacion_log(exc, metadata=metadata)
    except ResourceLockedError as exc:
        raise self.retry(exc=exc)
