# coding=utf-8
from django.conf import settings
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models import Q

from core.validators import alphanumeric
from crms.integraciones import IntegradorConPilot, IntegradorConTecnom, IntegradorConMaipu
from crms.integraciones.google_spreadsheet import IntegradorConGoogleSpreadsheet
from crms.integraciones.sirena import IntegradorConSirena
from crms.querysets import ConfiguracionQuerySet, OpcionClienteCRMQuerySet


class ConfiguracionDeCRM(models.Model):
    _descripcion = models.CharField(max_length=128)

    objects = ConfiguracionQuerySet().as_manager()

    class Meta:
        abstract = True

    def __str__(self):
        return self._descripcion

    def descripcion(self):
        return self._descripcion

    def integrador_de_prospectos(self):
        integrador_class = self.integrador_de_prospectos_class()
        integrador = integrador_class.nuevo_para(self)
        return integrador

    def integrador_de_prospectos_class(self):
        raise NotImplementedError('subclass responsibility')

    @classmethod
    def nombre_del_crm(cls):
        raise NotImplementedError('subclass responsibility')

    @classmethod
    def query_de_content_types(cls):
        """
            No deberia ser responsabilidad de esta clase.
            Responde una query (Q) que filtra por los tipos de todas las subclases. Por ejemplo responde una query
            (no queryset) que al aplicarla da como resultado:

            [
                <ContentType: configuracion de maipu>,
                <ContentType: configuracion de pilot>,
                <ContentType: configuracion de tecnom>
            ]
        """

        modelos = []
        for subclase in cls.__subclasses__():
            nombre_de_modelo = subclase._meta.object_name.lower()
            modelos.append(nombre_de_modelo)
        return Q(model__in=modelos)

    @classmethod
    def content_types(cls):
        content_types = ContentType.objects.filter(cls.query_de_content_types())
        return content_types


class ConfiguracionDePilot(ConfiguracionDeCRM):
    CONVENCIONAL = 1
    USADO = 2
    PLAN_DE_AHORRO = 3
    BUSSINESS_TYPE = [
        (CONVENCIONAL, "1 (Convencional/0km)"),
        (USADO, "2 (Usado)"),
        (PLAN_DE_AHORRO, "3 (Plan de ahorro)"),
    ]

    _appkey = models.CharField(max_length=128)
    _suborigin_id = models.CharField(max_length=64)
    _proveedor = models.CharField(
        max_length=128, default='', blank=True,
        help_text='Este valor será agregado al campo "pilot_provider_service"')
    _business_type = models.IntegerField(choices=BUSSINESS_TYPE, default=PLAN_DE_AHORRO)

    def proveedor(self):
        return self._proveedor

    def appkey(self):
        return self._appkey

    def suborigin_id(self):
        return self._suborigin_id

    def integrador_de_prospectos_class(self):
        return IntegradorConPilot

    def business_type(self):
        return self._business_type

    @classmethod
    def nuevo(cls, descripcion, appkey, suborigin_id, business_type, proveedor=''):
        configuracion = cls(_descripcion=descripcion, _appkey=appkey,
                            _suborigin_id=suborigin_id, _proveedor=proveedor, _business_type=business_type)
        configuracion.full_clean()
        configuracion.save()

        return configuracion

    @classmethod
    def nombre_del_crm(cls):
        return 'Pilot'


class ConfiguracionDeTecnom(ConfiguracionDeCRM):
    _usuario = models.CharField(max_length=128)
    _contrasenia = models.CharField(max_length=128)
    _vendor_name = models.CharField(max_length=128, blank=True, default='')
    _nombre_de_proveedor = models.CharField(
        max_length=128, default='', blank=True,
        help_text='El dato será enviado en provider > name (origen)')
    _sub_dominio = models.CharField(
        max_length=64, validators=[alphanumeric],
        help_text='Por ejemplo para "https://autotag.tecnomcrm.com/" debe configurar "autotag"'
    )
    _vendor_email = models.EmailField(null=True, blank=True)

    def usuario(self):
        return self._usuario

    def contrasenia(self):
        return self._contrasenia

    def vendor_name(self):
        return self._vendor_name

    def vendor_email(self):
        return self._vendor_email

    def nombre_de_proveedor(self):
        return self._nombre_de_proveedor

    def url(self):
        return settings.TECNOM_TEMPLATE_URL % self._sub_dominio

    def integrador_de_prospectos_class(self):
        return IntegradorConTecnom

    @classmethod
    def nuevo(cls, descripcion, usuario, contrasenia, vendor_name, sub_dominio,
              vendor_email=None, nombre_de_proveedor=''):
        configuracion = cls(_descripcion=descripcion, _usuario=usuario,
                            _contrasenia=contrasenia, _vendor_name=vendor_name,
                            _sub_dominio=sub_dominio, _vendor_email=vendor_email,
                            _nombre_de_proveedor=nombre_de_proveedor)
        configuracion.full_clean()
        configuracion.save()

        return configuracion

    @classmethod
    def nombre_del_crm(cls):
        return 'Tecnom'


class ConfiguracionDeMaipu(ConfiguracionDeCRM):

    def url(self):
        return settings.MAIPU_URL

    def integrador_de_prospectos_class(self):
        return IntegradorConMaipu

    @classmethod
    def nuevo(cls, descripcion):
        configuracion = cls(_descripcion=descripcion)
        configuracion.full_clean()
        configuracion.save()
        return configuracion

    @classmethod
    def nombre_del_crm(cls):
        return 'Maipu'


class ConfiguracionDeSirena(ConfiguracionDeCRM):
    @classmethod
    def nuevo(cls, descripcion):
        configuracion = cls(_descripcion=descripcion)
        configuracion.full_clean()
        configuracion.save()

        return configuracion

    def integrador_de_prospectos_class(self):
        return IntegradorConSirena

    @classmethod
    def nombre_del_crm(cls):
        return 'Sirena'


class ConfiguracionDeGoogleSpreadsheet(ConfiguracionDeCRM):
    @classmethod
    def nuevo(cls, descripcion):
        configuracion = cls(_descripcion=descripcion)
        configuracion.full_clean()
        configuracion.save()

        return configuracion

    def integrador_de_prospectos_class(self):
        return IntegradorConGoogleSpreadsheet

    @classmethod
    def nombre_del_crm(cls):
        return 'Google Spreadsheet'


class OpcionClienteCRM(models.Model):
    """
        Mis instancias la integracion PedidoDeProspecto con un CRM. Indica con qué configuración se va a integrar.Su
        responsabilidad principal es #enviar_prospecto


        Nota: Esta clase deberia llamarse algo asi como IntegracionDePedidoConClienteCRM.

        Uso GenericForeingKey en lugar de una herencia multi-tabla porque es dificultoso la migracion de datos,
        en los constraints, ya que esta herencia remueve los campos ids de las subclases.
    """

    _configuracion_content_type = models.ForeignKey(
        ContentType, limit_choices_to=ConfiguracionDeCRM.query_de_content_types(), verbose_name='CRM')
    _configuracion_id = models.PositiveIntegerField(verbose_name='Configuración')
    _configuracion = GenericForeignKey('_configuracion_content_type', '_configuracion_id')
    _pedido = models.ForeignKey('prospectos.PedidoDeProspecto', related_name='clientes_crms')

    objects = OpcionClienteCRMQuerySet.as_manager()

    class Meta:
        unique_together = ('_configuracion_content_type', '_configuracion_id', '_pedido')

    @classmethod
    def nuevo(cls, pedido, configuracion):
        nueva_opcion = cls(_pedido=pedido, _configuracion=configuracion)
        nueva_opcion.full_clean()
        nueva_opcion.save()

        return nueva_opcion

    @classmethod
    def con_id(cls, identificador):
        return cls.objects.get(pk=identificador)

    def enviar_prospecto(self, prospecto):
        integrador = self._configuracion.integrador_de_prospectos()
        integrador.evaluar_con(prospecto)

    def configuracion(self):
        return self._configuracion

    def clonar_para(self, pedido):
        self.id = None
        self._pedido = pedido
        self.save()

    def tiene_configuracion_con_nombre(self, nombre):
        return self.configuracion().nombre_del_crm() == nombre