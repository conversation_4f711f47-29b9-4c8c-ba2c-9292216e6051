# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2024-12-18 14:54
from __future__ import unicode_literals

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('crms', '0025_auto_20240229_1246'),
    ]

    operations = [
        migrations.AlterField(
            model_name='configuraciondepilot',
            name='_business_type',
            field=models.IntegerField(choices=[(1, '1 (Convencional/0km)'), (2, '2 (Usado)'), (3, '3 (<PERSON>)')], default=3),
        ),
        migrations.AlterField(
            model_name='configuraciondepilot',
            name='_proveedor',
            field=models.CharField(blank=True, default='', help_text='Este valor será agregado al campo "pilot_provider_service"', max_length=128),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='configuraciondetecnom',
            name='_nombre_de_proveedor',
            field=models.CharField(blank=True, default='', help_text='El dato será enviado en provider > name (origen)', max_length=128),
        ),
        migrations.AlterField(
            model_name='configuraciondetecnom',
            name='_sub_dominio',
            field=models.CharField(help_text='Por ejemplo para "https://autotag.tecnomcrm.com/" debe configurar "autotag"', max_length=64, validators=[django.core.validators.RegexValidator('^[0-9a-zA-Z]*$', 'Solo se permiten caracteres alfanumericos.')]),
        ),
        migrations.AlterField(
            model_name='configuraciondetecnom',
            name='_vendor_name',
            field=models.CharField(blank=True, default='', max_length=128),
        ),
    ]
