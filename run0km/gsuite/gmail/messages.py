import base64
import email
import re

from django.utils import timezone


class GMailMessage(object):
    _SENDER_RE = r'(?P<name>^.*) <(?P<email>.*)>'

    def __init__(self, identifier, subject, body, sender, receiver, date_time, thread_id, history_id):
        super(GMailMessage, self).__init__()
        self._id = identifier
        self._subject = subject
        self._body = body
        self._sender = sender
        self._receiver = receiver
        self._date_time = date_time
        self._thread_id = thread_id
        self._history_id = history_id

    def id(self):
        return self._id

    def subject(self):
        return self._subject

    def date_time(self):
        return self._date_time

    def body(self):
        return self._body

    def sender(self):
        return self._sender

    def receiver(self):
        return self._receiver

    def thread_id(self):
        return self._thread_id

    def history_id(self):
        return self._history_id

    @classmethod
    def new_for(cls, identifier, subject, body, sender, receiver, date_time, thread_id, history_id):
        return cls(identifier, subject, body, sender, receiver, date_time, thread_id, history_id)

