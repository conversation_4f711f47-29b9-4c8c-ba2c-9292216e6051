EMPTY_ACCOUNT_FRIENDS = {
    "TotalRecords": 0,
    "Records": []
}

ALL_ACCOUNT_FRIENDS = {
    "TotalRecords": 2,
    "Records": [
        {
            "Id": 6,
            "Created": "2017-06-08T18:08:01.617",
            "Name": "<PERSON>",
            "ProfileLink": "https://www.facebook.com/profile.php?id=*********",
            "ImageLink": "https://graph.facebook.com/*********/picture?type=normal",
            "Accounts": {
                "$type": "System.Collections.Generic.HashSet`1[[NCB.Entities.AccountContact, NCB.Entities]], System.Core",
                "$values": []
            }
        },
        {
            "Id": 23,
            "Created": "2017-05-08T18:08:01.617",
            "Name": "<PERSON>",
            "ProfileLink": "https://www.facebook.com/profile.php?id=*********",
            "ImageLink": "https://graph.facebook.com/*********/picture?type=normal",
            "Accounts": {
                "$type": "System.Collections.Generic.HashSet`1[[NCB.Entities.AccountContact, NCB.Entities]], System.Core",
                "$values": []
            }
        },
    ]
}

EMPTY_SENT_FRIENDSHIP_REQUESTS = {
    "TotalRecords": 0,
    "Records": []
}

ALL_SENT_FRIENDSHIP_REQUESTS = {
    "TotalRecords": 2,
    "Records": [
        {
            "Id": 160,
            "Processor": "FacebookAddFriendProcessor",
            "Success": False,
            "Created": "2017-07-04T19:39:40.833",
            "TokenId": 4,
            "CreatedBy": 1,
            "IsNew": True,
            "Token": None,
            "CreatedByToken": None,
            "Message": {
                "$type": "NCB.Entities.ExceptionQueueLogMessage, NCB.Entities",
                "Code": -**********,
                "Message": "{\"errorMessage\":\"Unable to find element with id 'fb-timeline-cover-name'\",\"request\":{\"headers\":{\"Accept-Encoding\":\"gzip,deflate\",\"Connection\":\"Keep-Alive\",\"Content-Length\":\"47\",\"Content-Type\":\"application/json; charset=utf-8\",\"Host\":\"localhost:4181\",\"User-Agent\":\"Apache-HttpClient/4.5.2 (Java/1.8.0_121)\"},\"httpVersion\":\"1.1\",\"method\":\"POST\",\"post\":\"{\\\"using\\\":\\\"id\\\",\\\"value\\\":\\\"fb-timeline-cover-name\\\"}\",\"url\":\"/element\",\"urlParsed\":{\"anchor\":\"\",\"query\":\"\",\"file\":\"element\",\"directory\":\"/\",\"path\":\"/element\",\"relative\":\"/element\",\"port\":\"\",\"host\":\"\",\"password\":\"\",\"user\":\"\",\"userInfo\":\"\",\"authority\":\"\",\"protocol\":\"\",\"source\":\"/element\",\"queryKey\":{},\"chunks\":[\"element\"]},\"urlOriginal\":\"/session/7a98a0d0-60f0-11e7-a178-2388378308e4/element\"}}\nFor documentation on this error, please visit: http://seleniumhq.org/exceptions/no_such_element.html\nBuild info: version: '3.0.1', revision: '1969d75', time: '2016-10-18 09:48:19 -0700'\nSystem info: host: 's50-63-165-73', ip: '************', os.name: 'Windows Server 2012 R2', os.arch: 'x86', os.version: '6.3', java.version: '1.8.0_121'\nDriver info: driver.version: unknown",
                "QueueMessage": {
                    "$type": "NCB.Entities.FacebookFriendRequest, NCB.Entities",
                    "To": "<EMAIL>",
                    "BrandId": 1,
                    "GroupId": None,
                    "AccountId": 2,
                    "TransactionId": "1234xcv"
                }
            }
        },
        {
            "Id": 159,
            "Processor": "FacebookAddFriendProcessor",
            "Success": True,
            "Created": "2017-07-04T19:36:49.35",
            "TokenId": 4,
            "CreatedBy": 1,
            "IsNew": True,
            "Token": None,
            "CreatedByToken": None,
            "Message": {
                "$type": "NCB.Entities.ExceptionQueueLogMessage, NCB.Entities",
                "Code": -**********,
                "Message": "No se pudo enviar una solicitud de amistad a '<EMAIL>' o ya es un amigo de la cuenta '<EMAIL>'.",
                "QueueMessage": {
                    "$type": "NCB.Entities.FacebookFriendRequest, NCB.Entities",
                    "To": "<EMAIL>",
                    "BrandId": 1,
                    "GroupId": None,
                    "AccountId": 2,
                    "TransactionId": "1564841sdcdscdx4sd5f4ds511cfzxdc1zdx21ds"
                }
            }
        }
    ]
}

ALL_GENERATED_CONTACTS = {
    "TotalRecords": 5,
    "Records": [
        {
            "Id": 159,
            "Created": "2017-07-12T20:26:22.2",
            "Name": "Matias Wolansky",
            "ProfileLink": "https://www.facebook.com/profile.php?id=**********",
            "ImageLink": "https://graph.facebook.com/**********/picture?type=normal",
            "Accounts": {
                "$type": "System.Collections.Generic.HashSet`1[[NCB.Entities.AccountContact, NCB.Entities]], System.Core",
                "$values": []
            }
        },
        {
            "Id": 5,
            "Created": "2017-07-07T14:36:17.393",
            "Name": "Victor Ferres Serra",
            "ProfileLink": "https://www.facebook.com/profile.php?id=***************",
            "ImageLink": "https://graph.facebook.com/***************/picture?type=normal",
            "Accounts": {
                "$type": "System.Collections.Generic.HashSet`1[[NCB.Entities.AccountContact, NCB.Entities]], System.Core",
                "$values": []
            }
        },
        {
            "Id": 7,
            "Created": "2017-07-07T14:36:17.393",
            "Name": "Victor Ferres Serra",
            "ProfileLink": "https://www.facebook.com/profile.php?id=***************",
            "ImageLink": "https://graph.facebook.com/***************/picture?type=normal",
            "Accounts": {
                "$type": "System.Collections.Generic.HashSet`1[[NCB.Entities.AccountContact, NCB.Entities]], System.Core",
                "$values": []
            }
        },
        {
            "Id": 7,
            "Created": "2017-07-07T14:36:17.393",
            "Name": "Victor Ferres Serra",
            "ProfileLink": "https://www.facebook.com/profile.php?id=***************",
            "ImageLink": "https://graph.facebook.com/***************/picture?type=normal",
            "Accounts": {
                "$type": "System.Collections.Generic.HashSet`1[[NCB.Entities.AccountContact, NCB.Entities]], System.Core",
                "$values": []
            }
        },
        {
            "Id": 6,
            "Created": "2017-06-08T18:08:01.617",
            "Name": "Nahuel Laski",
            "ProfileLink": "https://www.facebook.com/profile.php?id=**********",
            "ImageLink": "https://graph.facebook.com/**********/picture?type=normal",
            "Accounts": {
                "$type": "System.Collections.Generic.HashSet`1[[NCB.Entities.AccountContact, NCB.Entities]], System.Core",
                "$values": []
            }
        }
    ]
}
