# coding=utf-8
import json

import requests
from rest_framework import status

from lib.api_client.api import ApiClient
from lib.api_client.errors import UnexpectedStatus, ClientValidationError, ClientErrorResponse


class MaipuSender(ApiClient):
    _HEADERS = {
        'Content-Type': 'application/json',
        'Authorization': 'Basic UHJvcGlvLk1haXB1OlJQTUlLNjU4SlZBT1hXTFFUQzBCUzQ5Rjc='
    }

    def name(self):
        return 'Maipu'

    def serialize(self, request):
        return request.as_dict()

    def headers(self):
        return self._HEADERS

    def _load_content(self, response):
        return json.loads(response.content)

    def _send_request(self, headers, request_data, **kwargs):
        response = super(MaipuSender, self)._send_request(
            headers=headers, request_data=request_data, **kwargs)

        maipu_response = MaipuResponse(request_data, response)
        maipu_response.validate()
        return maipu_response.response_dict()

    def _post(self, url, headers, data, **kwargs):
        return requests.post(url=url, json=data, headers=headers, **kwargs)

    @classmethod
    def new_for(cls, url):
        return cls(url, cls.POST)


class MaipuResponse(object):
    def __init__(self, request, response):
        self._request = request
        self._response = response

    def validate(self):
        if self._response.status_code != status.HTTP_200_OK:
            raise UnexpectedStatus(message='The response status should be 200 but is %s' % self._response.status_code,
                                   request=self._request, response=self._response)

        response_dict = self.response_dict()

        if 'd' not in response_dict:
            raise ClientErrorResponse(message="Ha ocurrido un error. Intente nuevamente", data=response_dict,
                                      request=self._request, response=self._response)

        response_data = response_dict.get('d')
        if not response_data.get('estado', False):
            raise ClientErrorResponse(message=response_data.get("mensaje"), data=response_data,
                                      request=self._request, response=self._response)

    def response_dict(self):
        return self._load_content(self._response)

    def _load_content(self, response):
        return json.loads(response.content)


class MaipuLead(object):
    required_fields = ['date', 'name', 'email', 'phone', 'message', 'province', 'brand']

    def __init__(self, date, name, email, phone, message, province, brand, username, lastname=None, phone_prefix=None,
                 campaign=None, model=None, region=None):
        self.date = date
        self.name = name
        self.lastname = lastname
        self.email = email
        self.phone_prefix = phone_prefix
        self.phone = phone
        self.campaign = campaign
        self.message = message
        self.province = province
        self.region = region
        self.brand = brand
        self.model = model
        self.username = username

    @classmethod
    def _validate_arguments(cls, **kwargs):
        for key, value in list(kwargs.items()):
            if cls._is_required(key) and not value:
                raise ClientValidationError(key=key)

    @classmethod
    def new_with(cls, date, name, email, phone, message, province, brand, username, lastname=None, phone_prefix=None,
                 campaign=None, model=None, region=None):

        cls._validate_arguments(date=date, name=name, lastname=lastname, email=email, phone_prefix=phone_prefix,
                                phone=phone, campaign=campaign, message=message, province=province, region=region,
                                brand=brand, model=model, username=username)

        return cls(date=date, name=name, lastname=lastname, email=email, phone_prefix=phone_prefix, phone=phone,
                   campaign=campaign, message=message, province=province, region=region, brand=brand, model=model,
                   username=username)

    def as_dict(self):
        request = {
            'input': {
                'fecha': self.date,
                'nombre': self.name,
                'apellido': self.lastname,
                'email': self.email,
                'prefijoTelefonico': self.phone_prefix,
                'telefono': self.phone,
                'campania': self.campaign,
                'mensaje': self.message,
                'provincia': self.province,
                'localidad': self.region,
                'marca': self.brand,
                'modelo': self.model,
                'nombreDeUsuario': self.username
            }
        }
        return request

    def __hash__(self):
        return hash(self.as_dict())

    def __eq__(self, other):
        return self.as_dict() == other.as_dict()

    @classmethod
    def _is_required(cls, field_name):
        return field_name in cls.required_fields
