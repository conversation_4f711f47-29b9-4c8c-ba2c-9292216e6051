import json
from requests.exceptions import RequestEx<PERSON>, HTTPError
import requests
from lib.redescover.errors import SocialNetworksConnectionError, SocialNetworksParseResponseError, \
    SocialNetworksTokenResponseError, SocialNetworkInvalidRequestError


class SocialNetworksService(object):
    _default_url = 'http://www.smsmasivos.biz/Enriquecimiento.WebService/api/Service/Submit'

    def __init__(self, url=_default_url, is_test=False):
        self.url = url
        self.is_test = is_test

    def call(self, token, title, queries):
        headers = {'Content-type': 'application/json', 'Accept': 'application/json, text/javascript'}
        request_data = self._build_request_data(token, title, queries, self.is_test)

        try:
            response = requests.post(self.url, data=request_data, headers=headers)
        except RequestException as exc:
            raise SocialNetworksConnectionError(str(exc), request=request_data, response=None)
        try:
            response.raise_for_status()
        except HTTPError as exc:
            raise SocialNetworksConnectionError(str(exc), request=request_data, response=response.content)
        else:
            return response

    def _build_request_data(self, token, title, queries, is_test=False):
        body = {'token': token, 'Titulo': title, 'Queries': queries, 'Test': is_test}
        try:
            return json.dumps(body)
        except TypeError as exc:
            raise SocialNetworkInvalidRequestError(message=str(exc), request=str(body), response=None)


class SocialNetworksTokenResponse(object):
    def __init__(self, http_response):
        self.http_response = http_response
        self._parser_response(http_response)

    def _parser_response(self, http_response):
        # {
        #     "Id": 253,
        #     "Error": null
        # }
        http_content = http_response.content
        try:
            start = http_content.index('{')
            end = http_content.index('}') + 1
            string_response = http_content[start:end]
            json_response = json.loads(string_response)
        except ValueError as exc:
            raise SocialNetworksParseResponseError(message=str(exc), request=None, response=http_content)
        else:
            self._initialize_from_json(json_response)

    def _initialize_from_json(self, json_response):
        try:
            self.request_id = json_response['Id']
            self.error_message = json_response['Error']
            self.has_error = json_response['Error'] is not None
        except KeyError as exc:
            raise SocialNetworksParseResponseError(message=f'Key {str(exc)} not found',
                                                   request=None,
                                                   response=json.dumps(json_response))


class SocialNetworksDataResponse(object):
    def __init__(self, http_response):
        self.http_response = http_response
        self._parser_response(http_response)

    def _parser_response(self, http_response):
        # [{
        #     "Referencia": "11",
        #     "Result": {}
        # }]
        try:
            json_response = []
            for item in http_response.content:
                json_response.append(json.loads(item))
        except ValueError as exc:
            raise SocialNetworksParseResponseError(message=exc,
                                                   request=None,
                                                   response=http_response)
        else:
            self._initialize_from_json(json_response)

    def _initialize_from_json(self, json_response):
        try:
            self.data = json_response
        except KeyError as exc:
            raise SocialNetworksParseResponseError(message=f'Key {str(exc)} not found',
                                                   request=None,
                                                   response=json.dumps(json_response))


class SocialNetworksCover(object):
    def __init__(self, service=SocialNetworksService(), token=None):
        self.service = service
        self.token = token

    def evaluate(self, title, queries):
        response = self.service.call(self.token, title, queries)
        checker_response = SocialNetworksTokenResponse(response)
        if checker_response.has_error:
            raise SocialNetworksTokenResponseError(message=checker_response.error_message,
                                                   request=self.token,
                                                   response=checker_response.http_response.content)
        return checker_response
