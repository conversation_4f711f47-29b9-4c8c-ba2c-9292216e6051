import factory
from django.contrib.auth import get_user_model
from django.utils.timezone import now

from campanias.models import Campania, CategoriaDeCampania, TipoDeOrigen
from concesionarias.models import Concesionaria, ConfiguracionDeServiciosDeConcesionaria, <PERSON><PERSON><PERSON>
from conversaciones.models.mensajes_whatsapp import MensajesWhatsapp
from equipos.models import Equipo
from gerentes.models import <PERSON><PERSON>nte
from objetivos.models import PeriodoPersistido, Objetivo
from occ.models import CreditoDeSMS, Compulsa
from prospectos.models import PedidoDeProspecto, FiltroDePedido, CampoExtra, InformacionDeRedesSociales, Marca
from prospectos.models import (Prospecto, Comentario, Llamado, MotivoDeFinalizacion, Finalizacion, Venta,
                               EmailExtra, TelefonoExtra, Tag, Proveedor, Compra, CargaFallidaDeJotform, Rechazo,
                               AsignacionDeProspecto)
from prospectos.models.adicionales import Geolocalizacion
from reportes.models import TipoDeReporte
from users.models import PermisoDePersonificacion
from vendedores.models import Vendedor, VentasAprobadasPorMes, LogActividad, ConfiguracionDeServicios


class UsersFactory(factory.DjangoModelFactory):
    FACTORY_FOR = get_user_model()
    FACTORY_DJANGO_GET_OR_CREATE = ('username',)


class PermisoDePersonificacionFactory(factory.DjangoModelFactory):
    FACTORY_FOR = PermisoDePersonificacion


class ConfiguracionDeServiciosFactory(factory.DjangoModelFactory):
    FACTORY_FOR = ConfiguracionDeServicios


class VendedoresFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Vendedor

    @classmethod
    def _create(cls, target_class, *args, **kwargs):
        """Create an instance of the model, and save it to the database."""
        concesionaria = kwargs.pop('concesionaria', None)
        vendedor = super(VendedoresFactory, cls)._create(target_class, *args, **kwargs)
        if concesionaria is not None:
            vendedor.concesionaria = concesionaria
        ConfiguracionDeServicios.nuevo(vendedor=vendedor)
        vendedor.save()
        return vendedor


class CompulsasFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Compulsa


class LogActividadFactory(factory.DjangoModelFactory):
    FACTORY_FOR = LogActividad


class CreditoFactory(factory.DjangoModelFactory):
    FACTORY_FOR = CreditoDeSMS


class VentasAprobadasPorMesFactory(factory.DjangoModelFactory):
    FACTORY_FOR = VentasAprobadasPorMes


class ProspectosFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Prospecto

    @classmethod
    def _create(cls, target_class, *args, **kwargs):
        fecha_creacion = kwargs.pop('fecha_creacion', None)
        nombre_de_marca = kwargs.pop('marca', '')
        fecha_de_asignacion_a_vendedor = kwargs.pop('fecha_de_asignacion_a_vendedor', now())
        fecha_de_asignacion_a_vendedor = fecha_de_asignacion_a_vendedor or now()
        fecha_de_asignacion_a_supervisor = kwargs.pop('fecha_de_asignacion_a_supervisor', now())
        concesionaria = kwargs.pop('concesionaria', None)
        vendedor = kwargs.pop('vendedor', None)
        obj = super(ProspectosFactory, cls)._create(target_class, *args, **kwargs)
        if fecha_creacion is not None:
            obj.fecha_creacion = fecha_creacion
        if concesionaria is not None:
            obj.concesionaria = concesionaria
        obj.vendedor = vendedor
        obj.save()
        if vendedor is None:
            fecha_de_asignacion_a_vendedor = None
        AsignacionDeProspecto.nuevo(
            prospecto=obj,
            vendedor=obj.obtener_vendedor(),
            fecha_de_asignacion_a_vendedor=fecha_de_asignacion_a_vendedor,
            fecha_de_asignacion_a_supervisor=fecha_de_asignacion_a_supervisor)
        Geolocalizacion.nueva(prospecto=obj, ip=None, provincia='', localidad='', latitud=None, longitud=None)

        marca = Marca.obtener_or_crear_con_nombre(nombre_de_marca)
        obj.cambiar_marca_por(marca)
        return obj

    nombre = factory.Sequence(lambda n: 'Prospecto {0}'.format(n))
    telefono = factory.Sequence(lambda n: '444000{0}'.format(n))
    email = factory.Sequence(lambda n: 'email_{0}@email.com'.format(n))


class RechazosFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Rechazo


class CampoExtraFactory(factory.DjangoModelFactory):
    FACTORY_FOR = CampoExtra


class ComentariosFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Comentario


class LlamadosFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Llamado


class TipoDeOrigenFactory(factory.DjangoModelFactory):
    FACTORY_FOR = TipoDeOrigen
    FACTORY_DJANGO_GET_OR_CREATE = ('codigo', )


class CategoriasDeCampaniaFactory(factory.DjangoModelFactory):
    FACTORY_FOR = CategoriaDeCampania
    FACTORY_DJANGO_GET_OR_CREATE = ('tipo_de_origen', 'nombre', 'es_externa')
    nombre = ''
    es_externa = False


class CampaniasFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Campania
    FACTORY_DJANGO_GET_OR_CREATE = ('nombre', 'concesionaria')
    concesionaria = None


class MotivoDeFinalizacionFactory(factory.DjangoModelFactory):
    FACTORY_FOR = MotivoDeFinalizacion


class EquiposFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Equipo


class FinalizacionesFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Finalizacion


class VentasFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Venta


class EmailsExtraFactory(factory.DjangoModelFactory):
    FACTORY_FOR = EmailExtra


class TelefonosExtraFactory(factory.DjangoModelFactory):
    FACTORY_FOR = TelefonoExtra
    telefono = factory.Sequence(lambda n: '544000{0}'.format(n))


class TagsFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Tag


class PedidosDeProspectoFactory(factory.DjangoModelFactory):
    FACTORY_FOR = PedidoDeProspecto


class FiltrosDePedidoFactory(factory.DjangoModelFactory):
    FACTORY_FOR = FiltroDePedido


class GerentesFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Gerente


class ConcesionariasFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Concesionaria

    @classmethod
    def _create(cls, target_class, rubro=None, *args, **kwargs):
        if rubro is None:
            rubro, _ = Rubro.objects.get_or_create(_nombre='Automotriz')

        obj = super(ConcesionariasFactory, cls)._create(target_class, rubro=rubro, *args, **kwargs)
        ConfiguracionDeServiciosDeConcesionaria.nuevo(concesionaria=obj)
        obj.save()
        return obj


class ProveedoresFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Proveedor


class ComprasFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Compra


class CargaFallidaDeJotformFactory(factory.DjangoModelFactory):
    FACTORY_FOR = CargaFallidaDeJotform


class MensajesWhatsappFactory(factory.DjangoModelFactory):
    FACTORY_FOR = MensajesWhatsapp


class PeriodosPersistidosFactory(factory.DjangoModelFactory):
    FACTORY_FOR = PeriodoPersistido


class ObjetivosFactory(factory.DjangoModelFactory):
    FACTORY_FOR = Objetivo


class InformacionDeRedesSocialesFactory(factory.DjangoModelFactory):
    FACTORY_FOR = InformacionDeRedesSociales


class TipoDeReporteFactory(factory.DjangoModelFactory):
    FACTORY_FOR = TipoDeReporte
    FACTORY_DJANGO_GET_OR_CREATE = ('nombre', )
