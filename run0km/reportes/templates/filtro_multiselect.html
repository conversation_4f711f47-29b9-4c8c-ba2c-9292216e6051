{%load reportes_utils %}
{{ field.errors }}
{% with multiselect_id=field.id_for_label seleccionados=field.value %}
<div class="row filtro-multiselect">
    <div class="col-xs-5">
        <select name="{{ multiselect_id }}_from[]" id="{{ multiselect_id }}" class="form-control" size="10" multiple="multiple">
        {% for estado in field.field.choices %}
            {% if not estado.0|es_item_seleccionado:seleccionados %}
            <option value="{{ estado.0 }}">{{ estado.1 }}</option>
            {%  endif %}
        {% endfor %}
        </select>
    </div>

    <div class="col-xs-2">
        <button type="button" id="{{ multiselect_id }}_undo" class="btn btn-primary btn-block">Deshacer</button>
        <button type="button" id="{{ multiselect_id }}_rightAll" class="btn btn-block"><i class="glyphicon glyphicon-forward"></i></button>
        <button type="button" id="{{ multiselect_id }}_rightSelected" class="btn btn-block"><i class="glyphicon glyphicon-chevron-right"></i></button>
        <button type="button" id="{{ multiselect_id }}_leftSelected" class="btn btn-block"><i class="glyphicon glyphicon-chevron-left"></i></button>
        <button type="button" id="{{ multiselect_id }}_leftAll" class="btn btn-block"><i class="glyphicon glyphicon-backward"></i></button>
        <button type="button" id="{{ multiselect_id }}_redo" class="btn btn-warning btn-block">Rehacer</button>
    </div>

    <div class="col-xs-5">
        <select name="{{ field.name }}" id="{{ multiselect_id }}_to" class="form-control" size="10" multiple="multiple">
            {% for estado in field.field.choices %}
                {% if estado.0|es_item_seleccionado:seleccionados %}
                <option value="{{ estado.0 }}">{{ estado.1 }}</option>
                {% endif %}
            {% endfor %}
        </select>
    </div>
</div>
<script type="text/javascript">
    jQuery(document).ready(function($) {
        $('#{{ multiselect_id }}').multiselect({
            submitAllLeft: false,
            submitAllRight: true,
            search: {
                left: '<input type="text" name="q" class="form-control" placeholder="Buscar..." />',
                right: '<input type="text" name="q" class="form-control" placeholder="Buscar..." />'
            }
        });
    });
</script>
{% endwith %}