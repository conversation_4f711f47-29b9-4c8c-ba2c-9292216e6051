{%load reportes_utils %}

<div class="titulo-lista-rvReportes">
    <h3>Reporte de Entrega</h3>
</div>
<div class="lista">
    {%  if not reporte_de_entrega.datos %}
        <p style="text-align:center">Sin datos</p>
    {% else %}
        <table>

            {% for datos_de_origen in reporte_de_entrega.datos %}
                <tr class="origen">
                    <td colspan="4">Origen: {{ datos_de_origen.origen.nombre }}</td>
                </tr>
                {% for datos_de_supervisor in datos_de_origen.datos %}
                  {% if es_reporte_de_gerente %}
                      <tr class="supervisor">
                          <td colspan="4"><a href="{{DOMAIN}}{% url 'editar-vendedor' datos_de_supervisor.supervisor.id %}">
                              Supervisor: {{ datos_de_supervisor.supervisor }}
                          </a></td>
                      </tr>
                  {% endif %}
                  {% if agrupado_por_equipos %}
                    {% for datos_de_equipo in datos_de_supervisor.datos %}
                      <tr class="equipo">
                          <td colspan="4">Equipo: {{ datos_de_equipo.equipo }}</td>
                      </tr>

                      <tr class="titulo">
                        <td>VENDEDOR</td><td>PEDIDO aprox.</td><td>ENTREGADOS</td><td>FALTANTE aprox.</td>
                      </tr>
                      {% for datos_de_vendedor in datos_de_equipo.datos%}
                          <tr>
                              <td>&nbsp;&nbsp; <a href="{{DOMAIN}}{% url 'editar-vendedor' datos_de_vendedor.vendedor.id %}">{{ datos_de_vendedor.vendedor }}</a></td>
                              {% if datos_de_vendedor.entregado != '?' and datos_de_vendedor.pedido != '?' %}
                                  <td>{{ datos_de_vendedor.pedido|floatformat:"0" }}</td>
                                  <td>{{ datos_de_vendedor.entregado|floatformat:"0" }}</td>
                                  <td>
                                      <img style="vertical-align: middle;" src="{{ STATIC_URL }}img/bullet_{{ datos_de_vendedor.pedido|color_porcentaje_entregado:datos_de_vendedor.entregado }}.png"/>
                                      {{ datos_de_vendedor.faltante|floatformat:"0" }}
                                  </td>
                              {% else %}
                                  <td>{{ datos_de_vendedor.pedido }}</td>
                                  <td>{{ datos_de_vendedor.entregado }}</td>
                                  <td>{{ datos_de_vendedor.faltante }}</td>
                              {% endif %}
                          </tr>
                      {% endfor %}
                    {% endfor %}
                  {% else %}
                      <tr class="titulo">
                        <td>VENDEDOR</td><td>PEDIDO aprox.</td><td>ENTREGADOS</td><td>FALTANTE aprox.</td>
                      </tr>
                      {% for datos_de_vendedor in datos_de_supervisor.datos%}
                          <tr>
                              <td>&nbsp;&nbsp;<a href="{{DOMAIN}}{% url 'editar-vendedor' datos_de_vendedor.vendedor.id %}"> {{ datos_de_vendedor.vendedor }}</a></td>
                              {% if datos_de_vendedor.entregado != '?' and datos_de_vendedor.pedido != '?' %}
                                  <td>{{ datos_de_vendedor.pedido|floatformat:"0" }}</td>
                                  <td>{{ datos_de_vendedor.entregado|floatformat:"0" }}</td>
                                  <td>
                                      <img style="vertical-align: middle;" src="{{ STATIC_URL }}img/bullet_{{ datos_de_vendedor.pedido|color_porcentaje_entregado:datos_de_vendedor.entregado }}.png"/>
                                      {{ datos_de_vendedor.faltante|floatformat:"0" }}
                                  </td>
                              {% else %}

                                  <td>{{ datos_de_vendedor.pedido }}</td>
                                  <td>{{ datos_de_vendedor.entregado }}</td>
                                  <td>{{ datos_de_vendedor.faltante }}</td>
                              {% endif %}
                          </tr>
                      {% endfor %}
                  {% endif %}

                  <tr class="total">
                    <td>Total</td>
                    <td>{{ datos_de_supervisor.total.pedido|floatformat:"0" }}</td>
                    <td>{{ datos_de_supervisor.total.entregado|floatformat:"0" }}</td>
                    <td style="padding-left: 40px;">
                        {{ datos_de_supervisor.total.faltante|floatformat:"0" }}
                    </td>
                  </tr>
                  <tr class="supervisor">
                      <td>Sin Asignar</td>
                      <td colspan="3">{{ datos_de_supervisor.sin_asignar|floatformat:"0" }}</td>
                  </tr>
                  <tr><td colspan="4"></td></tr>
                {% endfor %}
            {% endfor %}
        </table>
    {% endif %}
</div>
