{%extends "base.html"%}

{% block js%}
  {{block.super}}
  <script type="text/javascript" src="{{ STATIC_URL }}lib/DataTables/media/js/jquery.dataTables.js"></script>
  <script type="text/javascript" src="{{ STATIC_URL }}js/staff.js"></script>
  <link href="{{ STATIC_URL }}lib/DataTables/media/css/jquery.dataTables.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="{{ STATIC_URL }}lib/DataTables/media/js/natural.js"></script>
  <link href="{{ STATIC_URL }}css/table.css" type="text/css" rel="stylesheet" />
  <link href="{{ STATIC_URL }}css/staff.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript">
        $(document).ready(function() {
            inizializarTablaParaGerentes();
        });
  </script>
{% endblock %}

{% block content %}
<form id="deshabilitar-form" action="{% url 'deshabilitar-vendedor' %}" method="POST">
    {% csrf_token %}
    <input name="pk" id="deshabilitar-pk" type="hidden" value="">
</form>
<form id="habilitar-form" action="{% url 'habilitar-vendedor' %}" method="POST">
    {% csrf_token %}
    <input name="pk" id="habilitar-pk" type="hidden" value="">
</form>
<div id="pregunta" title="pregunta?" style="display:none;"></div>
<div class="principal prospecto-resumen contenido">
    {% include "vendedores_list.html" with user=user %}
</div>

{%endblock%}

