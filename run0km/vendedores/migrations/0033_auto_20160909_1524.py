# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2016-09-09 18:24


import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('vendedores', '0032_auto_20160906_1040'),
    ]

    operations = [
        migrations.AlterField(
            model_name='vendedor',
            name='celular',
            field=models.CharField(blank=True, default=b'', max_length=45, null=True, validators=[django.core.validators.RegexValidator(b'^[0-9]*$', 'Solo se permiten valores num\xe9ricos.')]),
        ),
        migrations.AlterField(
            model_name='vendedor',
            name='prefijo_celular',
            field=models.CharField(blank=True, default=b'', max_length=5, null=True, validators=[django.core.validators.RegexValidator(b'^[0-9]*$', 'Solo se permiten valores num\xe9ricos.')]),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='vendedor',
            name='prefijo_telefono',
            field=models.CharField(blank=True, default=b'', max_length=5, null=True, validators=[django.core.validators.RegexValidator(b'^[0-9]*$', 'Solo se permiten valores num\xe9ricos.')]),
        ),
        migrations.AlterField(
            model_name='vendedor',
            name='telefono',
            field=models.CharField(blank=True, default=b'', max_length=45, null=True, validators=[django.core.validators.RegexValidator(b'^[0-9]*$', 'Solo se permiten valores num\xe9ricos.')]),
        ),
    ]
