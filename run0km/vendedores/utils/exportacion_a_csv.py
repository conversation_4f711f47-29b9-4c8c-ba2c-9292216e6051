# coding=utf-8
import csv
import re
from django.http.response import HttpResponse
from django.utils.encoding import smart_str

CAMPOS_DE_VENTA_PUBLICOS = {'fecha', 'vendedor', 'comprador', 'marca', 'modelo', 'precio', 'estado', 'supervisor',
                            'numero_de_contrato', 'calidad','distribuidor'}


CAMPOS_DE_VENTA_PRIVADOS = set()


class ExportadorDeVentas(object):
    def __init__(self, ventas, puede_acceder_a_los_proveedores_de_datos=False):
        self.ventas = ventas
        self.puede_acceder_a_los_proveedores_de_datos = puede_acceder_a_los_proveedores_de_datos

    def response_para_exportar_a_csv(self):
        (campos, info_ventas) = self.procesar_info_para_exportar()

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename=ventas.csv'
        writer = csv.writer(response, csv.excel)
        response.write('\\ufeff'.encode('utf8'))  # BOM (optional...Excel needs it to open UTF-8 file properly)

        writer.writerow([smart_str(x.title()) for x in campos])
        exp = re.compile('\r\n|\n|\r')

        for info in info_ventas:
            writer.writerow([re.sub(exp, ' ', smart_str(info.get(x, ''))) for x in campos])

        return response

    def _agregar_campos_para_exportar(self):
        campos = list(CAMPOS_DE_VENTA_PUBLICOS)
        campos.remove('supervisor')
        if not self.puede_acceder_a_los_proveedores_de_datos:
            campos.remove('distribuidor')
        return campos

    def _generar_info_para_exportar(self, venta):
        info = {}
        fecha = venta.fecha()
        info['fecha'] = fecha.strftime('%d/%m/%Y %H:%M') if fecha else ''
        info['vendedor'] = venta.vendedor
        info['comprador'] = venta.nombre_de_comprador()
        info['marca'] = venta.marca
        info['modelo'] = venta.modelo
        info['precio'] = venta.precio
        info['estado'] = venta.get_estado_display()
        info['numero_de_contrato'] = venta.numero_de_contrato if venta.numero_de_contrato else ''
        info['calidad'] = venta.calidad()
        info['distribuidor'] = venta.prospecto.obtener_distribuidor()
        return info

    def _generar_info_de_ventas_para_exportar(self, campos, ventas):
        info_ventas = []
        for venta in ventas:
            info = self._generar_info_para_exportar(venta)
            info_ventas.append(info)
        return info_ventas

    def _consulta_optimizada(self, ventas):
        consulta_optimizada = ventas.select_related('vendedor',
                                                    'prospecto',
                                                    'prospecto__campania__categoria__tipo_de_origen',
                                                    'prospecto__campania__categoria__distribuidor'
                                                    )
        return consulta_optimizada

    def procesar_info_para_exportar(self):
        campos = self._agregar_campos_para_exportar()
        ventas = self._consulta_optimizada(ventas=self.ventas)
        info_prospectos = self._generar_info_de_ventas_para_exportar(campos=campos, ventas=ventas)
        return campos, info_prospectos


class ExportadorDeVentasParaGerentes(ExportadorDeVentas):

    def __init__(self, ventas, puede_acceder_a_los_proveedores_de_datos=None):
        self.ventas = ventas
        self.puede_acceder_a_los_proveedores_de_datos = puede_acceder_a_los_proveedores_de_datos

    def _generar_info_para_exportar(self, venta):
        info_venta = super(ExportadorDeVentasParaGerentes, self)._generar_info_para_exportar(venta)
        info_venta['supervisor'] = venta.vendedor.supervisor

        return info_venta

    def _agregar_campos_para_exportar(self):
        campos = list(CAMPOS_DE_VENTA_PUBLICOS)
        if not self.puede_acceder_a_los_proveedores_de_datos:
            campos.remove('distribuidor')
        return campos


class ExportadorDeVendedores(object):
    def exportar_en_archivo(self, nombre_de_archivo, vendedores):
        try:
            with open(nombre_de_archivo, 'w') as archivo:
                self._escribir_en(archivo, vendedores)
        except EnvironmentError:  # parent of IOError, OSError *and* WindowsError where available
            raise

    def _escribir_en(self, archivo, vendedores):
        campos, filas = self.generar_datos_para(vendedores)
        writer = csv.writer(archivo)
        self._escribir(campos, filas, writer)

    def generar_datos_para(self, vendedores):
        campos = self._generar_campos()
        filas = self._generar_lista_de_filas_para(vendedores)
        return campos, filas

    def _generar_campos(self):
        return ['nombre', 'email', 'telefono', 'cargo', 'activo', 'eliminado']

    def _generar_lista_de_filas_para(self, vendedores):
        filas = []
        optimizados = self._consulta_optmizada_para(vendedores)
        for vendedor in optimizados.all():
            fila = self._generar_fila_para(vendedor)
            filas.append(fila)
        return filas

    def _escribir(self, campos, filas, writer):
        writer.writerow(campos)
        for cada_fila in filas:
            fila = [celda.encode("utf-8") for celda in cada_fila]
            writer.writerow(fila)

    def _consulta_optmizada_para(self, vendedores):
        return vendedores.select_related('user')

    def _generar_fila_para(self, vendedor):
        return [
            vendedor.full_name(),
            vendedor.email(),
            vendedor.telefono,
            vendedor.cargo,
            self._bool_como_texto(vendedor.esta_activo()),
            self._bool_como_texto(vendedor.esta_eliminado())
        ]

    def _bool_como_texto(self, un_bool):
        return 'SI' if un_bool else 'NO'
