# -*- coding: utf-8 -*-
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core import mail
from django.core.management import call_command
from django.utils.timezone import now, timedelta

from core.tests.validators.django_email import EmailOutboxValidator
from prospectos.models import Prospecto
from prospectos.models.gestor.repartidor_de_prospectos import RepartidorDeProspectos
from testing.base import BaseFixturedTest
from testing.factories import ProspectosFactory
from vendedores.detector_inactividad import DetectorDeInactividad
from vendedores.models import LogActividad, Vendedor
from vendedores.utils.emisor_de_notificaciones import EnviadorDeMailsDeAlerta


class AlertaInActividadTest(BaseFixturedTest):

    def setUp(self):
        super(AlertaInActividadTest, self).setUp()
        usernames = ['vend1', 'vend2', 'vend3', 'vend4', 'vend5', ]
        self.users = get_user_model().objects.filter(username__in=usernames).order_by('username')
        self.repartidor_de_prospectos = RepartidorDeProspectos.nuevo()
        self.crear_logs_de_actividad()
        self.asignar_prospectos_a_usuarios()

    def crear_logs_de_actividad(self):
        pasado = now() + timedelta(hours=-172)
        for i in range(4):
            LogActividad(vendedor=self.users[i].vendedor, anio=pasado.year, mes=pasado.month, ultima=pasado, cantidad=2).save()

    def asignar_prospectos_a_usuarios(self):
        Prospecto.objects.all().delete()
        #[admin, sup1, sup2, vend1, vend2, vend3, vend4, vend5]

        # vend1: hoy - t < 24 hs
        self.crear_prospecto(vendedor=self.users[0].vendedor, asignacion=now() + timedelta(hours=-12), telefono='123',
                             supervisor=self.users[0].vendedor.supervisor, nombre='prospecto_test_1')
        # vend2: 24 < x < 96
        self.crear_prospecto(vendedor=self.users[1].vendedor, asignacion=now() + timedelta(hours=-32), telefono='234',
                             supervisor=self.users[1].vendedor.supervisor, nombre='prospecto_test_2')
        # vend3: 96 < x < 24*7
        self.crear_prospecto(vendedor=self.users[2].vendedor, asignacion=now() + timedelta(hours=-100), telefono='345',
                             supervisor=self.users[2].vendedor.supervisor, nombre='prospecto_test_3')
        # vend4: 24*7 < x
        self.crear_prospecto(vendedor=self.users[3].vendedor, asignacion=now() + timedelta(hours=-170), telefono='456',
                             supervisor=self.users[3].vendedor.supervisor, nombre='prospecto_test_4')
        # vend5: Sin actividad
        self.crear_prospecto(vendedor=self.users[4].vendedor, asignacion=now() + timedelta(hours=-170), telefono='567',
                             supervisor=self.users[4].vendedor.supervisor, nombre='prospecto_test_5')

    def crear_prospecto(self, vendedor, asignacion, telefono, supervisor, nombre):
        datos = self._obtener_datos_para_prospecto(campania=self.fixture['camp_1'], vendedor=vendedor, nombre=nombre,
                                                   supervisor=supervisor, telefono=telefono)
        resultado = self.repartidor_de_prospectos.crear_nuevo_prospecto_desde(datos=datos, validar_email=False)
        self._hook_cambiar_fecha_de_asignacion_a_supervisor(resultado.prospecto(), fecha=asignacion)

    def _hook_cambiar_fecha_de_asignacion_a_supervisor(self, prospecto, fecha):
        asignacion = prospecto.asignacion
        asignacion.cambiar_fecha_para_supervisor(fecha)
        asignacion.full_clean()
        asignacion.save()

    def test_envio_diario_activado(self):
        """
        Testea que mande mail a usuarios con alarma diaria activada y:
         - que no registra ninguna actividad
         - que hace mas de 2 dias no registra actividad
         - que hace apenas mas de 1 dia que no registra actividad
        Testea que NO mande mail a usuario con alarma activada que hace menos de un dia registra actividad
        """
        call_command('alarma_inactividad')
        # 4 mails de Alarmas individuales. 2 a supervisores. 1 a logistica
        outbox = []
        for email in mail.outbox:
            outbox.append((email.subject, email.to, email.bcc))

    def test_envio_diario_desactivado(self):
        """
        Testea que NO mande mail a usuarios con alarma diaria desactivada y:
         - que no registra ninguna actividad
         - que hace mas de 2 dias no registra actividad
         - que hace apenas mas de 1 dia que no registra actividad
        Testea que NO mande mail a usuario con alarma desactivada que hace menos de un dia registra actividad
        """
        Vendedor.objects.all().update(alerta_diaria=False, alerta_a_supervisor=False)
        call_command('alarma_inactividad')


class EnviadorDeMailsDeAlertaTest(BaseFixturedTest):

    def setUp(self):
        super(EnviadorDeMailsDeAlertaTest, self).setUp()
        self._email_validator = EmailOutboxValidator.new_for(self)

    def test_enviar_mail_por_inactividad_a_vendedor(self):
        vendedor = self.fixture['vend_1']
        ProspectosFactory(vendedor=vendedor, campania=self.fixture['camp_2'])
        ProspectosFactory(vendedor=vendedor, campania=self.fixture['camp_2'])
        ProspectosFactory(vendedor=vendedor, campania=self.fixture['camp_2'])
        ProspectosFactory(vendedor=vendedor, campania=self.fixture['camp_2'])
        prospectos = vendedor.prospectos
        cantidad = prospectos.count()
        enviador = EnviadorDeMailsDeAlerta()
        detector = DetectorDeInactividad()
        cantidades_por_origen = detector.cantidades_por_origen_y_campania(prospectos)

        enviador.enviar_mail_por_inactividad_a_vendedor(vendedor, cantidad, cantidades_por_origen)

        self._email_validator.assert_outbox_with_size(size=1)
        email_enviado = self._email_validator.email_at(0)

        concesionaria = vendedor.obtener_concesionaria()
        subject = settings.SUBJECT_ALERTA_INACTIVIDAD % {'concesionaria': concesionaria.nombre, '#': cantidad}
        self._email_validator.assert_email_with_subject(expected_subject=subject, out_email=email_enviado)
        self._email_validator.assert_receiver(vendedor.email(), email_enviado)
        self._email_validator.assert_contains_html_content('<td>Mailing</td><td>Mail</td><td>4</td>', email_enviado)

    def test_enviar_mail_por_inactividad_a_supervisor(self):
        supervisor = self.fixture['sup_1']
        cantidad = 5
        vendedores = [{'vendedor':self.fixture['vend_1'], 'cantidad':3},
                      {'vendedor':self.fixture['vend_2'], 'cantidad':2}]

        enviador = EnviadorDeMailsDeAlerta()
        enviador.enviar_mail_por_inactividad_a_supervisor(supervisor, cantidad, vendedores)

        self._email_validator.assert_outbox_with_size(size=1)
        email_enviado = self._email_validator.email_at(0)

        subject = settings.SUBJECT_ALERTA_SUPERVISOR % supervisor.concesionaria.nombre
        self._email_validator.assert_email_with_subject(expected_subject=subject, out_email=email_enviado)
        self._email_validator.assert_receiver(supervisor.email(), email_enviado)
        self._email_validator.assert_contains_html_content(
            'Sus vendedores tienen %d Prospectos sin trabajar' % cantidad, email_enviado)
        self._email_validator.assert_contains_html_content(
            '<td>%s</td><td>3</td>'% self.fixture['vend_1'].full_name(), email_enviado)
        self._email_validator.assert_contains_html_content(
            '<td>%s</td><td>2</td>'%self.fixture['vend_2'].full_name(), email_enviado)

    def test_enviar_mail_por_prospectos_sin_asignar(self):
        supervisor = self.fixture['sup_1']
        supervisor.prospectos_a_cargo.all().delete()
        ProspectosFactory(responsable=supervisor, campania=self.fixture['camp_1'])
        ProspectosFactory(responsable=supervisor, campania=self.fixture['camp_1'])
        ProspectosFactory(responsable=supervisor, campania=self.fixture['camp_2'])
        ProspectosFactory(responsable=supervisor, campania=self.fixture['camp_2'])
        ProspectosFactory(responsable=supervisor, campania=self.fixture['camp_2'])

        detector = DetectorDeInactividad()
        cantidades_por_origen = detector.cantidades_por_origen_y_campania(supervisor.prospectos_a_cargo.all())
        enviador = EnviadorDeMailsDeAlerta()
        enviador.enviar_mail_por_prospectos_sin_asignar(supervisor, 5, cantidades_por_origen)

        self._email_validator.assert_outbox_with_size(size=1)
        email_enviado = self._email_validator.email_at(0)

        subject = settings.SUBJECT_ALERTA_SIN_ASIGNAR % {'concesionaria': supervisor.concesionaria.nombre, '#': 5}
        self._email_validator.assert_email_with_subject(expected_subject=subject, out_email=email_enviado)
        self._email_validator.assert_receiver(supervisor.email(), email_enviado)

        self._email_validator.assert_contains_html_content('Tiene 5 Prospectos sin asignar', email_enviado)
        self._email_validator.assert_contains_html_content(
            '<td>%s</td><td>2</td>' % self.fixture['camp_1'].nombre, email_enviado)
        self._email_validator.assert_contains_html_content(
            '<td>%s</td><td>3</td>' % self.fixture['camp_2'].nombre, email_enviado)

    def test_enviar_mails_de_alarma_a_logistica(self):
        prospectos_no_asignados = {'cantidad': 5,
                                   'campanias':
                                       [{'origen': 'SMS', 'nombre': 'Basica SMS', 'cantidad': 3},
                                        {'origen': 'Mail', 'nombre': 'Basica Mail', 'cantidad': 2}]}
        prospectos_sin_trabajar_x_7_dias = {'cantidad': 5,
                                            'vendedores': [
                                                {'vendedor': {'user':{'get_full_name': 'Nombre 1'}},
                                                 'prospectos': [{'cantidad': 4, 'origen': 'SMS'},
                                                               {'cantidad': 4, 'origen': 'Mail'}]},
                                                {'vendedor': {'user':{'get_full_name': 'Nombre 2'}},
                                                 'prospectos': [{'cantidad': 1, 'origen': 'SMS'},
                                                               {'cantidad': 2, 'origen': 'Web'}]}
                                            ]}
        supervisores_con_no_asignados = [{'supervisor': {'user': {'get_full_name': 'Nombre 3'}},
                                          'prospectos': [{'cantidad': 1, 'origen': 'SMS'},
                                                         {'cantidad': 2, 'origen': 'Web'}]},
                                         {'supervisor': {'user': {'get_full_name': 'Nombre 4'}},
                                          'prospectos': [{'cantidad': 3, 'origen': 'SMS'}]},
                                         ]
        usuarios_sin_actividad_x_30 = [{'usuario': {'cargo': 'Vendedor', 'user': {'get_full_name': 'Nombre 5'}},
                                        'fecha': '---'},
                                       {'usuario': {'cargo': 'Vendedor', 'user': {'get_full_name': 'Nombre 6'}},
                                        'fecha': now()}]

        usuarios_sin_actividad_x_7 = [{'usuario': {'cargo': 'Supervisor', 'user': {'get_full_name': 'Nombre 7'}},
                                        'fecha': '---'},
                                       {'usuario': {'cargo': 'Vendedor', 'user': {'get_full_name': 'Nombre 8'}},
                                        'fecha': now()},
                                       {'usuario': {'cargo': 'Vendedor', 'user': {'get_full_name': 'Nombre 9'}},
                                        'fecha': now()}
                                       ]
        enviador = EnviadorDeMailsDeAlerta()
        enviador.enviar_mails_a_logistica(prospectos_no_asignados,
                                          prospectos_sin_trabajar_x_7_dias,
                                          supervisores_con_no_asignados,
                                          usuarios_sin_actividad_x_30,
                                          usuarios_sin_actividad_x_7)

        self._email_validator.assert_outbox_with_size(size=1)
        email_enviado = self._email_validator.email_at(0)

        self._email_validator.assert_alternatives_content_includes(
            '5 Prospectos sin supervisor a cargo por mas de 24hs:', email_enviado)
        self._email_validator.assert_alternatives_content_includes(
            '<tr><td>SMS</td><tr><td>Basica SMS</td><td>3</td></tr>', email_enviado)
        self._email_validator.assert_alternatives_content_includes(
            '5 Prospectos de mas de 7 días asignados a vendedores (sin logueo):', email_enviado)
        self._email_validator.assert_alternatives_content_includes(
            'Prospectos de Supervisores sin asignar por mas de 4 días:', email_enviado)
        self._email_validator.assert_alternatives_content_includes(
            '3 Usuarios sin actividad por mas de 7 días', email_enviado)
        self._email_validator.assert_alternatives_content_includes(
            '2 Usuarios sin actividad por mas de 30 días:', email_enviado)
