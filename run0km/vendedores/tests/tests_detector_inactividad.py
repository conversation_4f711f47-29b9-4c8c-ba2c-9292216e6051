# coding=utf-8
from django.utils import timezone
from django.utils.timezone import now, timedelta
from django.conf import settings

from testing.base import BaseFixturedTest
from testing.factories import ProspectosFactory
from vendedores.detector_inactividad import DetectorDeInactividad
from vendedores.models import LogActividad, Vendedor
from vendedores.control_de_actividad import ControladorDeActividad


class DetectorDeInactividadTest(BaseFixturedTest):

    def setUp(self):
        super(DetectorDeInactividadTest, self).setUp()
        self.supervisor = self.fixture['sup_1']
        self.detector = DetectorDeInactividad()

    def poner_en_falta_al_vend_1(self):
        v1 = self.v1 = self.fixture['vend_1']
        v2 = self.v2 = self.fixture['vend_2']
        controlador = ControladorDeActividad()
        controlador.registrar_actividad(v1.user)
        controlador.registrar_actividad(v2.user)

        self.p1 = ProspectosFactory(vendedor=v1, campania=self.fixture['camp_1'],
                                    fecha_de_asignacion_a_vendedor=now() + timedelta(hours=23))
        self.p2 = ProspectosFactory(vendedor=v1, campania=self.fixture['camp_1'],
                                    fecha_de_asignacion_a_vendedor=now() + timedelta(days=2))
        self.p3 = ProspectosFactory(vendedor=v1, campania=self.fixture['camp_2'],
                                    fecha_de_asignacion_a_vendedor=now() + timedelta(days=2))
        self.p4 = ProspectosFactory(vendedor=v1, campania=self.fixture['camp_2'],
                                    fecha_de_asignacion_a_vendedor=now() + timedelta(days=2))

    def poner_en_falta_al_vend_3(self):
        v3 = self.v3 = self.fixture['vend_3']
        controlador = ControladorDeActividad()
        controlador.registrar_actividad(v3.user)
        p5 = self.p5 = ProspectosFactory(vendedor=v3, campania=self.fixture['camp_1'],
                                         fecha_de_asignacion_a_vendedor=now() + timedelta(days=2))

    def poner_en_falta_al_vendedor(self, vendedor, delta_a_considerar_en_dias):
        """
        :param vendedor: El vendedor a poner en falta
        :param delta_a_considerar_en_dias: Posterior a este tiempo, el vendedor estará en falta.

        Ejemplo: V1 tiene un delta de 5 días, entonces se registra actividad de logeo hace 5 dias y se crea Prospecto
        con fecha de asignación hoy, Como pasaron 5 días desde su último logeo y tiene un Prospecto sin
        trabajar, está en falta para ese delta.

        :return:
        """
        self.creador_de_contexto.crear_log_de_actividad_para_vendedor_con_fecha(
            vendedor=vendedor, fecha=now() - timedelta(days=delta_a_considerar_en_dias))
        self.creador_de_contexto.asignar_prospecto_nuevo_a(
            vendedor=vendedor, fecha_de_asignacion_a_vendedor=now())

    def test_cantidades_por_origen_y_campania(self):
        detector = DetectorDeInactividad()
        supervisor = self.fixture['sup_1']
        supervisor.prospectos_a_cargo.all().delete()
        c1 = self.fixture['camp_1']
        c2 = self.fixture['camp_2']
        ProspectosFactory(responsable=supervisor, campania=c1)
        ProspectosFactory(responsable=supervisor, campania=c1)
        ProspectosFactory(responsable=supervisor, campania=c2)
        ProspectosFactory(responsable=supervisor, campania=c2)
        ProspectosFactory(responsable=supervisor, campania=c2)
        cantidades_por_campania = detector.cantidades_por_origen_y_campania(supervisor.prospectos_a_cargo.all())
        self.assertTrue({'origen': c1.categoria.tipo_de_origen.nombre, 'campania': c1.nombre, 'cantidad': 2} in
                        cantidades_por_campania)
        self.assertTrue({'origen': c2.categoria.tipo_de_origen.nombre, 'campania': c2.nombre, 'cantidad': 3} in
                        cantidades_por_campania)

        cantidades_por_origen = detector.cantidades_por_origen(supervisor.prospectos_a_cargo.all())
        self.assertTrue({'origen': c1.categoria.tipo_de_origen.nombre, 'cantidad': 2} in cantidades_por_origen)
        self.assertTrue({'origen': c2.categoria.tipo_de_origen.nombre, 'cantidad': 3} in cantidades_por_origen)

    def test_vendedores_en_falta(self):
        self.poner_en_falta_al_vend_1()

        detector = DetectorDeInactividad()
        un_dia = timedelta(days=1)
        en_falta = detector.datos_de_vendedores_en_falta_por([self.v1, self.v2], un_dia)

        self.assertEqual(3, en_falta['cantidad'])
        self.assertEqual(1, len(en_falta['vendedores']))
        datos = en_falta['vendedores'][0]
        self.assertEqual(self.v1, datos['vendedor'])
        self.assertEqual(3, datos['cantidad'])
        self.assertEqual(datos['cantidad'], datos['prospectos'].count())
        self.assertTrue(self.p2 in datos['prospectos'])
        self.assertTrue(self.p3 in datos['prospectos'])
        self.assertTrue(self.p4 in datos['prospectos'])

    def test_vendedor_creado_hoy_que_nunca_logeo_no_aplica_para_envio_de_mail_de_inactividad_al_supervisor_con_delta_de_4_dias(
            self):
        # Dado
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(supervisor=self.supervisor,
                                                                          first_name='Elver')

        # Cuando
        self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=vendedor, fecha_de_asignacion_a_vendedor=now())

        # Entonces
        vendedores_en_falta = self.detector.datos_de_vendedores_en_falta_por([vendedor],
                                                                             timezone.timedelta(days=4))['vendedores']
        self.assertEqual(len(vendedores_en_falta), 0)

    def _validar_supervisor_con_vendedores_en_falta(self, supervisor, vendedores=None, delta_a_considerar_en_dias=1):
        datos_de_falta = self.detector.datos_de_supervisores_con_vendedores_en_falta_por(
            [supervisor], timedelta(days=delta_a_considerar_en_dias))
        self.assertEqual(len(datos_de_falta), 1)
        datos_de_supervisor_en_falta = datos_de_falta[0]
        self.assertEqual(datos_de_supervisor_en_falta['supervisor'], supervisor)

        self.assertEqual(len(datos_de_supervisor_en_falta['vendedores']), len(vendedores))
        vendedores_en_falta = [dato['vendedor'] for dato in datos_de_supervisor_en_falta['vendedores']]
        for vendedor in vendedores:
            self.assertTrue(vendedor in vendedores_en_falta)

    def _validar_vendedor_en_falta(self, vendedor, delta_a_considerar_en_dias=1, cantidad_de_prospectos_en_falta=None):
        vendedores_en_falta = self.detector.datos_de_vendedores_en_falta_por(
            [vendedor], timezone.timedelta(days=delta_a_considerar_en_dias))['vendedores']
        self.assertEqual(len(vendedores_en_falta), 1)
        self.assertEqual(vendedores_en_falta[0]['vendedor'], vendedor)
        if cantidad_de_prospectos_en_falta is not None:
            self.assertEqual(vendedores_en_falta[0]['cantidad'], cantidad_de_prospectos_en_falta)

    def _validar_vendedor_no_en_falta(self, vendedor, delta_a_considerar_en_dias=1):
        vendedores_en_falta = self.detector.datos_de_vendedores_en_falta_por(
            [vendedor], timezone.timedelta(days=delta_a_considerar_en_dias))['vendedores']
        self.assertEqual(len(vendedores_en_falta), 0)

    def test_vendedor_con_actividad_hace_1_dia_esta_en_falta_para_prospectos_que_ingresaron_hoy_con_delta_de_1_dia(self):
        # Dado
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(supervisor=self.supervisor,
                                                                          first_name='Elver')
        # Cuando
        self.poner_en_falta_al_vendedor(vendedor=vendedor, delta_a_considerar_en_dias=1)

        # Entonces
        self._validar_supervisor_con_vendedores_en_falta(supervisor=self.supervisor, vendedores=[vendedor],
                                                         delta_a_considerar_en_dias=1)

    def test_vendedor_con_actividad_hace_2_dias_esta_en_falta_para_prospectos_que_ingresaron_hoy_con_delta_de_2_dias(
            self):
        # Dado
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(supervisor=self.supervisor,
                                                                          first_name='Elver')

        # Cuando
        self.poner_en_falta_al_vendedor(vendedor=vendedor, delta_a_considerar_en_dias=2)

        # Entonces
        self._validar_vendedor_en_falta(vendedor=vendedor, delta_a_considerar_en_dias=2,
                                        cantidad_de_prospectos_en_falta=1)

    def test_vendedor_con_actividad_hace_3_dias_no_esta_en_falta_para_prospectos_que_ingresaron_hoy_con_delta_de_4_dias(
            self):
        # Dado
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(supervisor=self.supervisor,
                                                                          first_name='Elver')

        # Cuando
        self.poner_en_falta_al_vendedor(vendedor=vendedor, delta_a_considerar_en_dias=3)

        # Entonces
        self._validar_vendedor_no_en_falta(vendedor=vendedor, delta_a_considerar_en_dias=4)

    def test_vendedor_con_actividad_hoy_tiene_prospectos_en_falta_y_prospectos_en_regla(self):
        vendedor = self.creador_de_contexto.agregar_vendedor_a_supervisor(supervisor=self.supervisor,
                                                                          first_name='Elver')
        self.creador_de_contexto.asignar_prospecto_nuevo_a(vendedor=vendedor,
                                                           fecha_de_asignacion_a_vendedor=now() - timedelta(days=1))

        # Cuando
        self.poner_en_falta_al_vendedor(vendedor=vendedor, delta_a_considerar_en_dias=8)

        # Entonces
        self._validar_vendedor_en_falta(vendedor=vendedor, delta_a_considerar_en_dias=8,
                                        cantidad_de_prospectos_en_falta=1)

    def test_datos_de_supervisores_con_prospectos_sin_asignar(self):
        sup_1 = self.fixture['sup_1']
        sup_2 = self.fixture['sup_2']
        un_dia = timedelta(days=1)
        p1 = ProspectosFactory(responsable=sup_1, campania=self.fixture['camp_1'])
        p1.fecha_creacion = now() - un_dia
        p1.save()
        p2 = ProspectosFactory(responsable=sup_1, campania=self.fixture['camp_1'])
        p2.fecha_creacion = now() - un_dia
        p2.save()
        ProspectosFactory(responsable=sup_1, campania=self.fixture['camp_2'])

        p4 = ProspectosFactory(responsable=sup_2, campania=self.fixture['camp_1'], vendedor=sup_2.vendedores.all()[0])
        p4.fecha_creacion = now() - un_dia
        p4.save()
        p5 = ProspectosFactory(responsable=sup_2, campania=self.fixture['camp_1'])

        detector = DetectorDeInactividad()
        data_sin_asignar = detector.datos_de_supervisores_con_prospectos_sin_asignar_por([sup_1, sup_2], un_dia)

        self.assertEqual(1, len(data_sin_asignar))
        data_sup_1 = data_sin_asignar[0]
        self.assertEqual(sup_1, data_sup_1['supervisor'])
        self.assertEqual(data_sup_1['cantidad'], 2)
        self.assertEqual(data_sup_1['cantidad'], len(data_sup_1['prospectos']))
        self.assertTrue(p1 in data_sup_1['prospectos'])
        self.assertTrue(p2 in data_sup_1['prospectos'])

        p5.fecha_creacion = now() - un_dia
        p5.save()
        data_sin_asignar = detector.datos_de_supervisores_con_prospectos_sin_asignar_por([sup_1, sup_2], un_dia)
        self.assertEqual(2, len(data_sin_asignar))
        self.assertTrue(sup_2 == data_sin_asignar[0]['supervisor'] or sup_2 == data_sin_asignar[1]['supervisor'])

    def test_datos_de_prospectos_sin_responsable_por_mas_de(self):
        medio_dia = timedelta(hours=12)
        un_dia = timedelta(days=1)
        dos_dias = timedelta(days=2)
        detector = DetectorDeInactividad()

        p1 = ProspectosFactory(campania=self.fixture['camp_1'])
        p1.fecha_creacion = now() - un_dia
        p1.save()
        p2 = ProspectosFactory(campania=self.fixture['camp_1'])
        p2.fecha_creacion = now() - un_dia
        p2.save()
        p3 = ProspectosFactory(campania=self.fixture['camp_1'])
        p3.fecha_creacion = now() - dos_dias
        p3.save()

        ProspectosFactory(campania=self.fixture['camp_2'])
        p5 = ProspectosFactory(campania=self.fixture['camp_2'])
        p5.fecha_creacion = now() - medio_dia
        p5.save()
        p6 = ProspectosFactory(campania=self.fixture['camp_2'])
        p6.fecha_creacion = now() - dos_dias
        p6.save()
        p7 = ProspectosFactory(campania=self.fixture['camp_2'], responsable=self.fixture['sup_1'])
        p7.fecha_creacion = now() - dos_dias
        p7.save()

        datos_sin_responsable = detector.datos_de_prospectos_sin_responsable_por_mas_de(un_dia)
        self.assertEqual(datos_sin_responsable['cantidad'], 4)
        campanias = datos_sin_responsable['campanias']
        self.assertEqual(len(campanias), 2)

        campania_1 = campanias[0]
        campania_2 = campanias[1]
        self.assertTrue(campania_1['nombre'] == self.fixture['camp_1'].nombre and campania_1['cantidad'] == 3 or
                        campania_1['nombre'] == self.fixture['camp_2'].nombre and campania_1['cantidad'] == 1)

        self.assertTrue(campania_2['nombre'] == self.fixture['camp_1'].nombre and campania_2['cantidad'] == 3 or
                        campania_2['nombre'] == self.fixture['camp_2'].nombre and campania_2['cantidad'] == 1)

    def test_usuarios_sin_actividad_por(self):
        detector = DetectorDeInactividad()
        un_dia = timedelta(days=1)
        dos_dias = timedelta(days=2)
        tres_dias = timedelta(days=3)

        usuarios = Vendedor.objects.exclude(user__username__in=settings.BACKEND_USERS)
        sin_actividad = detector.usuarios_sin_actividad_por(usuarios, dos_dias)
        self.assertEqual(9, len(sin_actividad))
        controlador = ControladorDeActividad()
        controlador.registrar_actividad(self.fixture['vend_1'].user)
        l1 = LogActividad.objects.ultimo_log_del_vendedor(self.fixture['vend_1'])
        l1.ultima = now() - tres_dias
        l1.save()
        controlador.registrar_actividad(self.fixture['vend_2'].user)
        l2 = LogActividad.objects.ultimo_log_del_vendedor(self.fixture['vend_2'])
        l2.ultima = now() - un_dia
        l2.save()
        controlador.registrar_actividad(self.fixture['vend_3'].user)

        sin_actividad = detector.usuarios_sin_actividad_por(usuarios, dos_dias)
        self.assertEqual(7, len(sin_actividad))
