# -*- coding: utf-8 -*-
from unittest import skipIf

from django.conf import settings
from django.core import mail
from django.utils import timezone

from concesionarias.models import Sitio
from occ.servicio_de_facebook import ServicioDeFacebook
from testing.base import BaseLoggedAdminTest
from testing.test_utils import reload_model
from users.personificacion import ControladorDePermisosDePersonificacion
from vendedores.gestor import GestorDeVendedores
from vendedores.models import Vendedor

MJ_ERRORS_DETECTED = 'Por favor, corrija los siguientes errores.'
MJ_ERRORS_DETECTED_2 = 'Por favor corrija los errores detallados abajo.'


class AddEditVendedorAdminContext(object):
    @classmethod
    def nuevo(cls):
        contexto = cls()
        return contexto

    def vendedores_url(self):
        return '/admin/vendedores/vendedor/'

    def agregar_vendedor_url(self):
        return '/admin/vendedores/vendedor/add/'

    def editar_vendedor_url(self, vendedor_id):
        return '/admin/vendedores/vendedor/%s/change/' % vendedor_id

    def agregar_vendedor_default_post_data(self, supervisor_id, concesionaria_id, usuario='new_usr',
                                           first_name='Nombre', last_name='Apellido',
                                           email='<EMAIL>', password='unapass', password2='unapass',
                                           cargo='Vendedor', factor_de_asignacion=1,
                                           ajuste_factor_de_asignacion_administrador=0, eliminado=False,
                                           cantidad_de_ventas_aprobadas=0, dias_para_atender_prospecto=3,
                                           equipo_id=''):
        agregar_vendedor_default_post_data = {
            'usuario': usuario, 'first_name': first_name, 'last_name': last_name,
            'email': email, 'supervisor': supervisor_id, 'concesionaria': concesionaria_id,
            'password': password, 'password2': password2,
            'cargo': cargo, 'factor_de_asignacion': factor_de_asignacion,
            'ajuste_factor_de_asignacion_administrador': ajuste_factor_de_asignacion_administrador,
            'cantidad_de_ventas_aprobadas': cantidad_de_ventas_aprobadas,
            'dias_para_atender_prospecto': dias_para_atender_prospecto,
            'equipo_id': equipo_id, 'eliminado': eliminado,
            '_minutos_de_inactividad_maximos_para_circular_sus_prospectos': 20
        }
        agregar_vendedor_default_post_data.update(self.default_form_data_base())
        return agregar_vendedor_default_post_data

    def __conversor(self, boolean):
        conversor = {True: 'on', False: 'off'}
        return conversor[boolean]

    def _default_form_data_para_agregar_config_de_servicio_a_vendedor(self, vendedor_id='',
                                          chat_habilitado=True, facebook_habilitado=False, notificaciones_habilitadas=True,
                                          permiso_para_chatear=True, redes_habilitado=False,
                                          servicio_incontactables_habilitado=True, sms_habilitado=True,
                                          mensaje_bienvenida_habilitado=True, vendedores_pueden_crear_propuestas=True,
                                          whatsapp_habilitado=False, app_habilitada=True):
        if vendedor_id:
            config_servicios_id = Vendedor.objects.get(id=vendedor_id).configuracion_de_servicios().id
        else:
            config_servicios_id = ''
        return {
            'configuracion_servicios-0-_chat_habilitado': [self.__conversor(chat_habilitado)],
            'configuracion_servicios-0-_facebook_habilitado': [self.__conversor(facebook_habilitado)],
            'configuracion_servicios-0-_notificaciones_habilitadas': [self.__conversor(notificaciones_habilitadas)],
            'configuracion_servicios-0-_permiso_para_chatear': [self.__conversor(permiso_para_chatear)],
            'configuracion_servicios-0-_redes_habilitado': [self.__conversor(redes_habilitado)],
            'configuracion_servicios-0-_servicio_incontactables_habilitado': [self.__conversor(servicio_incontactables_habilitado)],
            'configuracion_servicios-0-_sms_habilitado': [self.__conversor(sms_habilitado)],
            'configuracion_servicios-0-_vendedor': [str(vendedor_id)],
            'configuracion_servicios-0-_vendedores_pueden_crear_propuestas': [self.__conversor(vendedores_pueden_crear_propuestas)],
            'configuracion_servicios-0-_whatsapp_habilitado': [self.__conversor(whatsapp_habilitado)],
            'configuracion_servicios-0-_app_habilitada': [self.__conversor(app_habilitada)],
            'configuracion_servicios-0-_mensaje_bienvenida_habilitado': [self.__conversor(mensaje_bienvenida_habilitado)],
            'configuracion_servicios-0-id': [str(config_servicios_id)],
            'configuracion_servicios-__prefix__-_app_habilitada': [self.__conversor(app_habilitada)],
            'configuracion_servicios-__prefix__-_chat_habilitado': [self.__conversor(chat_habilitado)],
            'configuracion_servicios-__prefix__-_facebook_habilitado': [self.__conversor(facebook_habilitado)],
            'configuracion_servicios-__prefix__-_mensaje_bienvenida_habilitado': [self.__conversor(mensaje_bienvenida_habilitado)],
            'configuracion_servicios-__prefix__-_notificaciones_habilitadas': [self.__conversor(notificaciones_habilitadas)],
            'configuracion_servicios-__prefix__-_permiso_para_chatear': [self.__conversor(permiso_para_chatear)],
            'configuracion_servicios-__prefix__-_servicio_incontactables_habilitado': [self.__conversor(servicio_incontactables_habilitado)],
            'configuracion_servicios-__prefix__-_sms_habilitado': [self.__conversor(sms_habilitado)],
            'configuracion_servicios-__prefix__-_vendedor': [str(vendedor_id)],
            'configuracion_servicios-__prefix__-_vendedores_pueden_crear_propuestas': [self.__conversor(vendedores_pueden_crear_propuestas)],
            'configuracion_servicios-__prefix__-id': [''],
            }

    def _default_form_data_para_agregar_permisos_a_vendedor(self, puede_exportar_prospectos=True, vendedor_id=''):
        return {
            '_permisos-0-_vendedor': [str(vendedor_id)],
            '_permisos-0-id': [''],
            '_permisos-__prefix__-_exportacion_de_prospectos': [self.__conversor(puede_exportar_prospectos)],
            '_permisos-__prefix__-_vendedor': [str(vendedor_id)],
            '_permisos-__prefix__-id': ['']
        }

    def editar_vendedor_initial_post_data(self, concesionaria_id, supervisor_id, vendedor_id='', celular='', equipo_id='', factor_de_asignacion=10,
                                          limite_datos_diarios_al_sup='', limite_datos_diarios='', limite_datos_nuevos='',
                                          prefijo_celular='', prefijo_telefono='', telefono='', eliminado=False,
                                          chat_habilitado=True, facebook_habilitado=False, notificaciones_habilitadas=True,
                                          permiso_para_chatear=True, redes_habilitado=False, cargo='Vendedor',
                                          servicio_incontactables_habilitado=True, sms_habilitado=True,
                                          mensaje_bienvenida_habilitado=True, vendedores_pueden_crear_propuestas=True,
                                          whatsapp_habilitado=False, app_habilitada=True, puede_exportar_prospectos=True,
                                          ajuste_factor_de_asignacion_administrador=0, dias_para_atender_prospecto=3):
        if vendedor_id:
            vendedor = Vendedor.objects.get(id=vendedor_id)
            if vendedor.es_supervisor():
                supervisor_id = ''

        initial_post_data = {'_save': 'Guardar',
                             '_encoding': 'utf-8',
                             'encoding': 'utf-8',
                             '_ultima_actividad_0': '04/04/2017',
                             '_ultima_actividad_1': '17:36:02',
                             'cargo': str(cargo),
                             'celular': str(celular),
                             'concesionaria': str(concesionaria_id),
                             'equipo': str(equipo_id),
                             'factor_de_asignacion': str(factor_de_asignacion),
                             'ajuste_factor_de_asignacion_administrador': str(ajuste_factor_de_asignacion_administrador),
                             'limite_de_datos_diarios_al_supervisor_en_pedidos': str(limite_datos_diarios_al_sup),
                             'limite_de_datos_diarios_en_pedidos': str(limite_datos_diarios),
                             'limite_de_datos_nuevos_en_pedidos': str(limite_datos_nuevos),
                             'prefijo_celular': str(prefijo_celular),
                             'prefijo_telefono': str(prefijo_telefono),
                             'supervisor': str(supervisor_id),
                             'telefono': str(telefono),
                             'eliminado': str(eliminado),
                             'dias_para_atender_prospecto': str(dias_para_atender_prospecto),
                             '_minutos_de_inactividad_maximos_para_circular_sus_prospectos': 20
                             }
        initial_post_data.update(self.default_form_data_configurada(
            vendedor_id=vendedor_id, chat_habilitado=chat_habilitado, facebook_habilitado=facebook_habilitado,
            notificaciones_habilitadas=notificaciones_habilitadas, permiso_para_chatear=permiso_para_chatear,
            redes_habilitado=redes_habilitado, servicio_incontactables_habilitado=servicio_incontactables_habilitado,
            sms_habilitado=sms_habilitado, mensaje_bienvenida_habilitado=mensaje_bienvenida_habilitado,
            vendedores_pueden_crear_propuestas=vendedores_pueden_crear_propuestas,
            whatsapp_habilitado=whatsapp_habilitado, app_habilitada=app_habilitada,
            puede_exportar_prospectos=puede_exportar_prospectos))
        return initial_post_data

    def default_form_data_configurada(self, vendedor_id='',
                                      chat_habilitado=True, facebook_habilitado=False, notificaciones_habilitadas=True,
                                      permiso_para_chatear=True, redes_habilitado=False,
                                      servicio_incontactables_habilitado=True, sms_habilitado=True,
                                      mensaje_bienvenida_habilitado=True, vendedores_pueden_crear_propuestas=True,
                                      whatsapp_habilitado=False, app_habilitada=True, puede_exportar_prospectos=True):
        config_servicio = self._default_form_data_para_agregar_config_de_servicio_a_vendedor(
            vendedor_id=vendedor_id, chat_habilitado=chat_habilitado, facebook_habilitado=facebook_habilitado,
            notificaciones_habilitadas=notificaciones_habilitadas, permiso_para_chatear=permiso_para_chatear,
            redes_habilitado=redes_habilitado, servicio_incontactables_habilitado=servicio_incontactables_habilitado,
            sms_habilitado=sms_habilitado, mensaje_bienvenida_habilitado=mensaje_bienvenida_habilitado,
            vendedores_pueden_crear_propuestas=vendedores_pueden_crear_propuestas,
            whatsapp_habilitado=whatsapp_habilitado, app_habilitada=app_habilitada)
        permisos = self._default_form_data_para_agregar_permisos_a_vendedor(
            puede_exportar_prospectos=puede_exportar_prospectos, vendedor_id=vendedor_id)
        default_form_data = config_servicio.copy()
        default_form_data.update(permisos)

        if self._esta_editando_configuracion_de_servicio(vendedor_id):
            cantidad_de_initial_forms = 1
        else:
            cantidad_de_initial_forms = 0
        default_form_data.update(self.default_form_data_base(cantidad_de_initial_forms=cantidad_de_initial_forms))
        return default_form_data

    def _esta_editando_configuracion_de_servicio(self, vendedor_id):
        if vendedor_id:
            vendedor = Vendedor.objects.get(id=vendedor_id)
            configuracion_de_servicios = vendedor.configuracion_de_servicios()
            return configuracion_de_servicios is not None
        return False

    def default_form_data_base(self, cantidad_de_initial_forms=0):
        return {
            'configuracion_servicios-TOTAL_FORMS': '1',
            'configuracion_servicios-INITIAL_FORMS': str(cantidad_de_initial_forms),
            'configuracion_servicios-MIN_NUM_FORMS': '0',
            'configuracion_servicios-MAX_NUM_FORMS': '1',
            '_permisos-INITIAL_FORMS': '0',
            '_permisos-MAX_NUM_FORMS': '1',
            '_permisos-MIN_NUM_FORMS': '0',
            '_permisos-TOTAL_FORMS': '1'
        }


class EditVendedorAdminTest(BaseLoggedAdminTest):
    def setUp(self):
        super(EditVendedorAdminTest, self).setUp()
        self.contexto_test = AddEditVendedorAdminContext.nuevo()
        self.supervisor_del_vendedor = self.fixture['sup_1']
        self.otro_supervisor = self.fixture['sup_2']
        self.vendedor = self._agregar_vendedor()

    @skipIf(True, 'Test pendiente de borrado o refactor')
    def test_cambiar_supervisor_de_vendedor_traslada_permisos_de_personificacion_del_supervisor_viejo_al_nuevo(self):
        editar_vendedor_url = self.contexto_test.editar_vendedor_url(vendedor_id=self.vendedor.id)
        edit_data = self.editar_post_data(concesionaria_id=self.otro_supervisor.obtener_concesionaria().id,
                                          supervisor_id=self.otro_supervisor.id)
        response = self.client.post(editar_vendedor_url, edit_data, follow=True)

        self.assertEqual(response.status_code, 200)
        controlador = ControladorDePermisosDePersonificacion()
        self.assertTrue(controlador.puede_personificar(usuario=self.otro_supervisor.usuario(),
                                                       alias=self.vendedor.usuario()))
        self.assertFalse(controlador.puede_personificar(usuario=self.supervisor_del_vendedor.usuario(),
                                                        alias=self.vendedor.usuario()))

    def editar_post_data(self, concesionaria_id, supervisor_id):
        edit_data = self.contexto_test.editar_vendedor_initial_post_data(concesionaria_id=concesionaria_id,
                                                                         supervisor_id=supervisor_id)
        return edit_data

    def _agregar_vendedor(self):
        concesionaria = self.supervisor_del_vendedor.obtener_concesionaria()
        data = self.contexto_test.agregar_vendedor_default_post_data(supervisor_id=self.supervisor_del_vendedor.id,
                                                                     concesionaria_id=concesionaria.id,
                                                                     first_name='Sauron')
        data.update(self.contexto_test.default_form_data_configurada())
        response = self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        self.assertRedirects(response, self.contexto_test.vendedores_url(), status_code=302, target_status_code=200,
                             msg_prefix='')
        self.assertContains(response, 'Se agregó con éxito vendedor')
        vendedor = Vendedor.objects.get(user__first_name='Sauron')
        return vendedor


class AddEditVendedorAdminTest(BaseLoggedAdminTest):
    def setUp(self):
        super(AddEditVendedorAdminTest, self).setUp()
        self.contexto_test = AddEditVendedorAdminContext.nuevo()
        self._default_data = self.contexto_test.agregar_vendedor_default_post_data(supervisor_id='',
                                                                                   concesionaria_id='')

    def test_al_agregar_vendedor_sin_supervisor_debia_notificar_error(self):
        response = self.client.post(self.contexto_test.agregar_vendedor_url(), self._default_data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, MJ_ERRORS_DETECTED)
        self.assertContains(response, 'Un Vendedor debe tener un Supervisor asignado.')

    def test_al_agregar_vendedor_con_equipo_de_otro_supervisor_debia_notificar_error(self):
        data = self._default_data
        data['supervisor'] = self.fixture['sup_1'].id
        data['equipo'] = self.fixture['sup_2'].equipos.first().id
        response = self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, MJ_ERRORS_DETECTED)
        self.assertContains(response, 'Un Vendedor solo puede pertenecer a equipos de su supervisor.')

    def test_vendedor_eleminado_no_puede_incorporarse_a_un_equipo(self):
        data = self._default_data
        data['supervisor'] = self.fixture['sup_1'].id
        data['equipo'] = self.fixture['sup_1'].equipos.first().id
        data['eliminado'] = True
        response = self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, MJ_ERRORS_DETECTED)
        self.assertContains(response, 'Un Vendedor eliminado no puede puede pertenecer a un equipo.')

    def test_vendedor_debe_pertenecer_misma_concesionaria_que_su_supervisor(self):
        data = self._default_data
        data['supervisor'] = self.fixture['sup_1'].id
        data['concesionaria'] = self.fixture['conce_2'].id
        response = self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, MJ_ERRORS_DETECTED)
        self.assertContains(response, 'Un Vendedor debe pertenecer a la misma Concesionaria que su Supervisor.')

    @skipIf(True, 'Test pendiente de borrado o refactor')
    def test_agregar_vendedor_deberia_notificar_exito(self):
        data = self._default_data
        supervisor = self.fixture['sup_1']
        data['supervisor'] = supervisor.id
        data['concesionaria'] = supervisor.concesionaria.id
        data.update(self.contexto_test.default_form_data_configurada())
        response = self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        self.assertRedirects(response, self.contexto_test.vendedores_url(), status_code=302, target_status_code=200,
                             msg_prefix='')
        self.assertContains(response, 'Se agregó con éxito vendedor')

        self.assertEqual(supervisor.vendedores.filter(user__last_name='Apellido').count(), 1)
        nuevo_vendedor = supervisor.vendedores.filter(user__last_name='Apellido').first()
        configuracion = nuevo_vendedor.configuracion_servicios
        self.assertFalse(configuracion.whatsapp_habilitado())
        self.assertFalse(configuracion.redes_habilitado())
        self.assertTrue(configuracion.chat_habilitado())
        self.assertTrue(configuracion.sms_habilitado())
        self.assertTrue(configuracion.puede_modificar_servicio_de_chat())
        self.assertTrue(configuracion.notificaciones_habilitadas())
        self.assertTrue(configuracion.mensaje_bienvenida_habilitado())
        self.assertTrue(configuracion.servicio_incontactables_habilitado())

    def test_al_agregar_vendedor_por_ventana_admin_se_le_asigna_credito_inicial_para_campania(self):
        data = self._default_data
        data['supervisor'] = self.fixture['sup_1'].id
        data['concesionaria'] = self.fixture['sup_1'].concesionaria.id
        data['credito_inicial_para_campania'] = settings.CREDITO_INICIAL_PARA_CAMPANIA
        self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        nuevo_vendedor = Vendedor.objects.get(user__username='new_usr')
        self.assertEqual(nuevo_vendedor.credito.credito_base, settings.CREDITO_INICIAL_PARA_CAMPANIA)

    @skipIf(True, 'Test pendiente de borrado o refactor')
    def test_envio_de_mail_al_crear_vendedor(self):
        data = {'usuario': 'new_usr', 'first_name': 'Nombre', 'last_name': 'Apellido', 'email': '<EMAIL>',
                'password': 'unapass', 'password2': 'unapass',
                'cargo': 'Vendedor', 'factor_de_asignacion': 1, 'ajuste_factor_de_asignacion_administrador ': 0,
                'supervisor': self.fixture['sup_1'].id,
                'concesionaria': self.fixture['sup_1'].concesionaria.id,
                'cantidad_de_ventas_aprobadas': 0, 'eliminado': False,
                'configuracion_servicios-TOTAL_FORMS': '1',
                'configuracion_servicios-INITIAL_FORMS': '0',
                'configuracion_servicios-MIN_NUM_FORMS': '0',
                'configuracion_servicios-MAX_NUM_FORMS': '1',
                }

        response = self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        self.assertRedirects(response, self.contexto_test.vendedores_url(), status_code=302, target_status_code=200, msg_prefix='')
        self.assertContains(response, 'Se agregó con éxito vendedor')
        new_usr = Vendedor.objects.get(user__username='new_usr')

        email = mail.outbox[0]
        self.assertEqual(email.subject, 'Bienvenido a Run0Km.com')
        self.assertEqual(email.to, ['<EMAIL>'])
        self.assertEqual(email.from_email, new_usr.concesionaria.email_de_origen_para_notificaciones())
        self.assertEqual(email.bcc, [])
        self.assertIn('Contraseña: unapass', email.body)
        self.assertIn('Te esperamos en %s' % settings.HOST, email.body)

        response = self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, MJ_ERRORS_DETECTED)
        self.assertContains(response, 'Ya existe una cuenta con ese Usuario')

        data['usuario'] = 'new_usr2'
        concesionaria = self.fixture['sup_1'].concesionaria
        sitio = Sitio(url='sarasa.com.ar')
        sitio.save()
        concesionaria.sitio = sitio
        concesionaria.save()
        response = self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        self.assertRedirects(response, self.contexto_test.vendedores_url(), status_code=302, target_status_code=200, msg_prefix='')
        self.assertContains(response, 'Se agregó con éxito vendedor')
        new_usr = Vendedor.objects.get(user__username='new_usr')

        email = mail.outbox[1]
        self.assertEqual(email.subject, 'Bienvenido a conce_1')
        self.assertEqual(email.to, ['<EMAIL>'])
        self.assertEqual(email.from_email, new_usr.concesionaria.email_de_origen_para_notificaciones())
        self.assertEqual(email.bcc, [])
        self.assertIn('Contraseña: unapass', email.body)
        self.assertIn('Te esperamos en %s' % concesionaria.url_concesionaria(), email.body)


class AddEditSupervisorAdminTest(BaseLoggedAdminTest):
    def setUp(self):
        super(AddEditSupervisorAdminTest, self).setUp()
        self.contexto_test = AddEditVendedorAdminContext.nuevo()
        self.gestor_de_vendedores = GestorDeVendedores.nuevo()
        self.supervisor = self.fixture['sup_1']
        self.vendedor_de_supervisor = self.fixture['vend_1']
        self.supervisor_dos = self.fixture['sup_2']
        self.servicio_facebook = ServicioDeFacebook.nuevo()
        self.concesionaria = self.supervisor.obtener_concesionaria()
        self.vendedor_url = '/admin/vendedores/vendedor/%s/change/'

    def _post_edit_data(self, concesionaria_id='', supervisor_id='', equipo_id='', eliminado=False):
        data = {'usuario': 'new_usr', 'first_name': 'Nombre', 'last_name': 'Apellido', 'email': '<EMAIL>',
                'password': 'unapass', 'password2': 'unapass',
                'cargo': 'Supervisor', 'factor_de_asignacion': 1, 'ajuste_factor_de_asignacion_administrador': 0,
                'cantidad_de_ventas_aprobadas': 0, 'dias_para_atender_prospecto': 3,
                'configuracion_servicios-TOTAL_FORMS': '1',
                'configuracion_servicios-INITIAL_FORMS': '0',
                'configuracion_servicios-MIN_NUM_FORMS': '0',
                'configuracion_servicios-MAX_NUM_FORMS': '1',
                'concesionaria': concesionaria_id,
                'supervisor': supervisor_id,
                'equipo': equipo_id,
                'eliminado': eliminado
                }
        return data

    def _agregar_configuracion_de_servicios_a_edit_post_data(self, supervisor, data, facebook_habilitado=False,
                                                             app_habilitada=False):
        data['configuracion_servicios-INITIAL_FORMS'] = 1

        data['configuracion_servicios-0-id'] = supervisor.configuracion_servicios.id
        data['configuracion_servicios-0-_permiso_para_chatear'] = True
        data['configuracion_servicios-0-_chat_habilitado'] = True
        data['configuracion_servicios-0-_whatsapp_habilitado'] = True
        data['configuracion_servicios-0-_sms_habilitado'] = True
        data['configuracion_servicios-0-_servicio_incontactables_habilitado'] = True
        data['configuracion_servicios-0-_mensaje_para_incontactables'] = True
        data['configuracion_servicios-0-_redes_habilitado'] = True
        data['configuracion_servicios-0-_mensaje_bienvenida_habilitado'] = True
        data['configuracion_servicios-0-_mensaje_bienvenida'] = True
        data['configuracion_servicios-0-_notificaciones_habilitadas'] = True
        data['configuracion_servicios-0-_facebook_habilitado'] = facebook_habilitado
        data['configuracion_servicios-0-_app_habilitada'] = app_habilitada
        return data

    def test_no_se_puede_eliminar_supervisor_con_pedidos_activos(self):
        self._sacar_vendedores_a_cargo_y_trasladar(supervisor_viejo=self.supervisor,
                                                   supervisor_nuevo=self.supervisor_dos)
        data = self.contexto_test.editar_vendedor_initial_post_data(
            supervisor_id=self.supervisor.id, concesionaria_id=self.concesionaria.id, eliminado=True)
        self.supervisor.habilitar()
        calidades = [self.fixture['tipo_s'],]
        self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores(
            supervisor=self.supervisor, credito=10, yapa=5, consumido=2,
            fecha=timezone.now().date(), calidades=calidades)
        self.supervisor.refresh_from_db()
        response = self.client.post(self.contexto_test.editar_vendedor_url(vendedor_id=self.supervisor.id),
                                    data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No puede eliminarse un Supervisor con Pedidos activos.')
        self.assertFalse(self.supervisor.esta_eliminado())

    def test_habilitar_app_de_supervisor_con_concesionaria_habilitada_habilita_la_app_de_su_vendedor(self):
        self.supervisor.configuracion_de_servicios().delete()
        concesionaria = self.supervisor.obtener_concesionaria()
        config_de_servicios_de_concesionaria = concesionaria.configuracion_de_servicios()
        config_de_servicios_de_concesionaria.habilitar_app()
        config_de_servicios_de_vendedor = self.vendedor_de_supervisor.configuracion_de_servicios()
        self.gestor_de_vendedores.configurar_habilitacion_de_la_app(
            vendedor=self.vendedor_de_supervisor, app_habilitada=False)
        self.gestor_de_vendedores.configurar_habilitacion_de_la_app(vendedor=self.supervisor, app_habilitada=False)
        self.assertFalse(config_de_servicios_de_vendedor.app_habilitada())
        data = self.contexto_test.editar_vendedor_initial_post_data(
            vendedor_id=self.supervisor.id, concesionaria_id=self.concesionaria.id, eliminado=False,
            app_habilitada=True, supervisor_id='')
        self.supervisor.habilitar()
        response = self.client.post(self.contexto_test.editar_vendedor_url(vendedor_id=self.supervisor.id),
                                    data, follow=True)
        self.assertEqual(response.status_code, 200)
        vendedor_de_supervisor = reload_model(self.vendedor_de_supervisor)
        self.assertTrue(vendedor_de_supervisor.configuracion_de_servicios().app_habilitada())

    def test_deshabilitar_app_de_supervisor_con_concesionaria_habilitada_deshabilita_la_app_de_su_vendedor(self):
        concesionaria = self.supervisor.obtener_concesionaria()
        config_de_servicios_de_concesionaria = concesionaria.configuracion_de_servicios()
        config_de_servicios_de_concesionaria.habilitar_app()
        config_de_servicios_de_vendedor = self.vendedor_de_supervisor.configuracion_de_servicios()
        config_de_servicios_de_vendedor = reload_model(config_de_servicios_de_vendedor)
        self.assertTrue(config_de_servicios_de_vendedor.app_habilitada())
        data = self.contexto_test.editar_vendedor_initial_post_data(concesionaria_id=self.concesionaria.id,
                                                                    supervisor_id=self.supervisor.id,
                                                                    app_habilitada=False, cargo='Supervisor')
        self.supervisor.habilitar()
        response = self.client.post(self.contexto_test.editar_vendedor_url(vendedor_id=self.supervisor.id),
                                    data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertFalse(self.vendedor_de_supervisor.configuracion_de_servicios().app_habilitada())

    def test_no_se_puede_habilitar_facebook_para_un_supervisor_no_activo(self):
        self.supervisor.deshabilitar()
        data = self.contexto_test.editar_vendedor_initial_post_data(concesionaria_id=self.concesionaria.id,
                                                                    supervisor_id=self.supervisor.id,
                                                                    facebook_habilitado=True)
        response = self.client.post(self.contexto_test.editar_vendedor_url(vendedor_id=self.supervisor.id),
                                    data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertFalse(self.servicio_facebook.esta_habilitado(supervisor=self.supervisor))
        self.assertContains(
            response, 'Solo los supervisores activos y no eliminados pueden tener habilitado el servicio de Facebook.')

    @skipIf(True, 'Test pendiente de borrado o refactor')
    def test_no_se_puede_habilitar_facebook_para_un_supervisor_eliminado(self):
        self._sacar_vendedores_a_cargo_y_trasladar(supervisor_viejo=self.supervisor,
                                                   supervisor_nuevo=self.supervisor_dos)
        self.supervisor.eliminar()
        data = self.contexto_test.editar_vendedor_initial_post_data(
            concesionaria_id=self.concesionaria.id, supervisor_id=self.supervisor.id, facebook_habilitado=True)
        response = self.client.post(self.contexto_test.editar_vendedor_url(vendedor_id=self.supervisor.id),
                                    data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertFalse(self.servicio_facebook.esta_habilitado(supervisor=self.supervisor))
        self.assertContains(
            response, 'Solo los supervisores activos y no eliminados pueden tener habilitado el servicio de Facebook.')

    @skipIf(True, 'Test pendiente de borrado o refactor')
    def test_se_puede_habilitar_facebook_para_supervisor_no_eliminado_y_activo(self):
        self.assertTrue(self.supervisor.esta_activo())
        self.assertFalse(self.supervisor.esta_eliminado())
        data = self.contexto_test.editar_vendedor_initial_post_data(
            concesionaria_id=self.concesionaria.id, supervisor_id=self.supervisor.id, facebook_habilitado=True)
        response = self.client.post(self.contexto_test.editar_vendedor_url(vendedor_id=self.supervisor.id),
                                    data, follow=True)
        self.assertRedirects(response, self.contexto_test.vendedores_url(), status_code=302, target_status_code=200,
                             msg_prefix='')
        self.assertContains(response, 'Se modificó con éxito vendedor')
        self.assertTrue(self.servicio_facebook.esta_habilitado(supervisor=self.supervisor))

    @skipIf(True, 'Test pendiente de borrado o refactor')
    def test_no_se_puede_agregar_supervisor_sin_concesionaria(self):
        data = self.contexto_test.agregar_vendedor_default_post_data(
            concesionaria_id='', supervisor_id='', cargo='Supervisor', usuario='supervisortest', first_name='supervisor',
            last_name='test')
        data.update(self.contexto_test.default_form_data_configurada())
        cantidad_de_supervisores = Vendedor.objects.con_cargo_supervisor().count()
        response = self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, MJ_ERRORS_DETECTED)
        self.assertContains(response, 'La concesionaria es obligatoria para los supervisores')
        nueva_cantidad_de_supervisores = Vendedor.objects.con_cargo_supervisor().count()
        self.assertEqual(cantidad_de_supervisores, nueva_cantidad_de_supervisores)

    @skipIf(True, 'Test pendiente de borrado o refactor')
    def test_no_se_puede_agregar_un_supervisor_que_tiene_otro_supervisor_asignado(self):
        data = self.contexto_test.agregar_vendedor_default_post_data(
            concesionaria_id=self.concesionaria.id, supervisor_id=self.supervisor.id, cargo='Supervisor',
            usuario='supervisortest', first_name='supervisor', last_name='test')
        data.update(self.contexto_test.default_form_data_configurada())
        cantidad_de_supervisores = Vendedor.objects.con_cargo_supervisor().count()
        response = self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, MJ_ERRORS_DETECTED)
        self.assertContains(response, 'Un Supervisor no puede tener un Supervisor asignado.')
        nueva_cantidad_de_supervisores = Vendedor.objects.con_cargo_supervisor().count()
        self.assertEqual(cantidad_de_supervisores, nueva_cantidad_de_supervisores)

    @skipIf(True, 'Test pendiente de borrado o refactor')
    def test_no_se_puede_agregar_un_supervisor_que_pertenece_a_un_equipo(self):
        data = self.contexto_test.agregar_vendedor_default_post_data(
            concesionaria_id=self.concesionaria.id, equipo_id=self.supervisor.obtener_equipos()[0].id,
            supervisor_id='')
        cantidad_de_supervisores = Vendedor.objects.con_cargo_supervisor().count()
        response = self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, MJ_ERRORS_DETECTED)
        self.assertContains(response, 'Un Supervisor no puede pertenecer a un Equipo.')
        nueva_cantidad_de_supervisores = Vendedor.objects.con_cargo_supervisor().count()
        self.assertEqual(cantidad_de_supervisores, nueva_cantidad_de_supervisores)

    def _sacar_vendedores_a_cargo_y_trasladar(self, supervisor_viejo, supervisor_nuevo):
        for vendedor in self.supervisor.vendedores_a_cargo():
            self.gestor_de_vendedores.reasignar_supervisor(vendedor=vendedor, viejo_supervisor=self.supervisor,
                                                           nuevo_supervisor=self.supervisor_dos)

    @skipIf(True, 'Test pendiente de borrado o refactor')
    def test_se_modifica_supervisor_exitosamente(self):
        data = self._post_edit_data(concesionaria_id=self.concesionaria.id)
        data = self._agregar_configuracion_de_servicios_a_edit_post_data(supervisor=self.supervisor, data=data)
        response = self.client.post(self.contexto_test.editar_vendedor_url(vendedor_id=self.supervisor.id),
                                    data, follow=True)
        self.assertRedirects(response, self.contexto_test.vendedores_url(), status_code=302, target_status_code=200,
                             msg_prefix='')
        self.assertContains(response, 'Se modificó con éxito vendedor')
        self.assertTrue(self.supervisor.esta_activo())
        self.assertFalse(self.supervisor.esta_eliminado())

    @skipIf(True, 'Test pendiente de borrado o refactor')
    def test_se_agrega_supervisor_exitosamente(self):
        data = self.contexto_test.agregar_vendedor_default_post_data(concesionaria_id=self.concesionaria.id,
                                                                     supervisor_id='', cargo='Supervisor')
        cantidad_de_supervisores = Vendedor.objects.con_cargo_supervisor().count()
        response = self.client.post(self.contexto_test.agregar_vendedor_url(), data, follow=True)
        self.assertRedirects(response, self.contexto_test.vendedores_url(), status_code=302, target_status_code=200,
                             msg_prefix='')
        self.assertContains(response, 'Se agregó con éxito vendedor')
        nueva_cantidad_de_supervisores = Vendedor.objects.con_cargo_supervisor().count()
        self.assertEqual(cantidad_de_supervisores+1, nueva_cantidad_de_supervisores)