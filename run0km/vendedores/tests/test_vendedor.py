from equipos.models import Equipo
from prospectos.models import PedidoDeProspecto, FiltroDePedido
from testing.creador_de_contexto import CreadorDeContexto
from testing.base import BaseFixturedTest


class ProspectosPedidosParaVendedorTest(BaseFixturedTest):
    def setUp(self):
        super(ProspectosPedidosParaVendedorTest, self).setUp()
        self.supervisor = self.fixture['sup_1']
        self.creador_de_contexto = CreadorDeContexto(supervisor=self.supervisor, fixture=self.fixture)
        self.vendedor_uno = self.creador_de_contexto.agregar_vendedor_a_supervisor()
        self.vendedor_dos = self.creador_de_contexto.agregar_vendedor_a_supervisor()
        PedidoDeProspecto.objects.all().delete()

    def test_supervisor_sin_pedidos_deberia_responder_que_vendedor_no_tiene_pedidos_activos(self):
        self.assertFalse(self.vendedor_uno.es_vendedor_con_pedidos_activos())

    def test_supervisor_con_pedidos_a_vendedor_deberia_responder_tiene_pedidos_activos(self):
        self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(self.vendedor_uno)
        self.assertTrue(self.vendedor_uno.es_vendedor_con_pedidos_activos())

    def test_supervisor_con_pedidos_a_otro_vendedor_deberia_responder_que_vendedor_no_tiene_pedidos_activos(self):
        self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(self.vendedor_dos)
        self.assertFalse(self.vendedor_uno.es_vendedor_con_pedidos_activos())

    def test_supervisor_con_pedidos_a_todos_sus_vendedores_deberia_responder_tiene_pedidos_activos(self):
        self.creador_de_contexto.agregar_pedido_para_todos_los_vendedores()
        self.assertTrue(self.vendedor_uno.es_vendedor_con_pedidos_activos())

    def test_supervisor_con_pedidos_para_equipo_sin_el_vendedor_deberia_responder_no_tiene_pedidos_activos(self):
        equipo = self._crear_equipo_para(self.supervisor, integrantes=[self.vendedor_dos])
        self.creador_de_contexto.agregar_pedido_asignado_a_equipo(equipo)
        self.assertFalse(self.vendedor_uno.es_vendedor_con_pedidos_activos())

    def test_supervisor_con_pedidos_para_equipo_del_vendedor_deberia_responder_tiene_pedidos_activos(self):
        equipo = self._crear_equipo_para(self.supervisor, integrantes=[self.vendedor_uno])
        self.creador_de_contexto.agregar_pedido_asignado_a_equipo(equipo)
        self.assertTrue(self.vendedor_uno.es_vendedor_con_pedidos_activos())

    def test_supervisor_sin_pedidos_deberia_responder_marcas_pedidas_vacias(self):
        self.assertEqual(self.vendedor_uno.marcas_pedidas(), [])

    def test_supervisor_con_pedidos_deberia_responder_la_marca_del_pedido(self):
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(self.vendedor_uno)
        marca = 'Ford'
        FiltroDePedido.nuevo_para_inclusion(pedido=pedido, campo='marca', selector=FiltroDePedido.CONTIENE,
                                            valor=marca)
        self.assertEqual(self.vendedor_uno.marcas_pedidas(), [marca])

    def test_supervisor_con_pedidos_que_excluye_marca_no_deberia_responder_la_marca(self):
        pedido = self.creador_de_contexto.agregar_pedido_asignado_a_vendedor(self.vendedor_uno)
        marca = 'Ford'
        FiltroDePedido.nuevo_para_exclusion(pedido=pedido, campo='marca', selector=FiltroDePedido.CONTIENE,
                                            valor=marca)
        self.assertEqual(self.vendedor_uno.marcas_pedidas(), [])

    def _crear_equipo_para(self, supervisor, integrantes):
        equipo = Equipo.objects.create(nombre='equipo de %s' % supervisor.full_name(), supervisor=supervisor)
        equipo.integrantes = integrantes
        equipo.save()
        return equipo
