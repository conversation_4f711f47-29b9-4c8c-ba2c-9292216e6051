from prospectos.models import Prospecto
from testing.base import BaseLoggedGerenteTest
from users.personificacion import ControladorDePermisosDePersonificacion
from vendedores.models import Vendedor


class ReasignarSupervisorTest(BaseLoggedGerenteTest):
    def post_data(self, vendedor, supervisor_id, equipo=''):
        nuevos_datos = {
            'email': vendedor.user.email,
            'first_name': vendedor.user.first_name,
            'last_name': vendedor.user.last_name,
            'equipo': equipo,
            'factor_de_asignacion': vendedor.obtener_factor_de_asignacion(),
            'supervisor': supervisor_id
        }
        return nuevos_datos

    def reasignar_url(self, vendedor_id):
        return '/vendedores/vendedor/%s/' % vendedor_id

    def lista_staff_url(self):
        return '/vendedores/'

    def test_reasignar_supervisor_a_vendedor(self):
        id_vend = self.fixture['vend_1'].id
        vendedor_original = Vendedor.objects.get(id=id_vend)
        id_sup = self.fixture['sup_2'].id

        nuevos_datos = self.post_data(vendedor=vendedor_original, supervisor_id=id_sup)
        url = self.reasignar_url(vendedor_id=id_vend)
        response = self.client.post(url, nuevos_datos)
        self.assertRedirects(response, self.lista_staff_url(), status_code=302, target_status_code=200, msg_prefix='')

        vendedor = Vendedor.objects.get(id=id_vend)
        self.assertEqual(vendedor.equipo, None)
        self.assertEqual(vendedor.supervisor.id, id_sup)

        id_pros = self.fixture['p_1'].id
        prospecto = Prospecto.objects.get(id=id_pros)
        self.assertEqual(prospecto.vendedor, vendedor_original)
        self.assertEqual(prospecto.responsable.id, id_sup)

    def test_reasignar_supervisor_a_vendedor_saca_personificacion_de_supervisor_viejo_y_crea_para_el_nuevo(self):
        id_vend = self.fixture['vend_1'].id
        vendedor_original = Vendedor.objects.get(id=id_vend)
        supervisor_original = vendedor_original.supervisor
        nuevo_supervisor = self.fixture['sup_2']
        id_sup = nuevo_supervisor.id
        url = self.reasignar_url(vendedor_id=id_vend)
        nuevos_datos = self.post_data(vendedor=vendedor_original, supervisor_id=id_sup)
        response = self.client.post(url, nuevos_datos)
        self.assertRedirects(response, self.lista_staff_url(), status_code=302, target_status_code=200, msg_prefix='')

        vendedor_actualizado = Vendedor.objects.get(id=id_vend)
        controlador = ControladorDePermisosDePersonificacion.nuevo()
        self.assertTrue(controlador.puede_personificar(usuario=nuevo_supervisor.usuario(),
                                                       alias=vendedor_actualizado.usuario()))
        self.assertFalse(controlador.puede_personificar(usuario=supervisor_original.usuario(),
                                                        alias=vendedor_actualizado.usuario()))

