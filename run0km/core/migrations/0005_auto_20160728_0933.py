# -*- coding: utf-8 -*-
# Generated by Django 1.9.7 on 2016-07-28 12:33


import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_auto_20160703_0401'),
    ]

    operations = [
        migrations.AddField(
            model_name='sistema',
            name='cantidad_de_participantes_compulsa',
            field=models.IntegerField(default=20, help_text='Cantidad de participantes para una compulsa de chat'),
        ),
        migrations.AddField(
            model_name='sistema',
            name='cantidad_de_reintentos_compulsa',
            field=models.PositiveSmallIntegerField(default=5, help_text='Cantidad de intentos a realizar para atender un pedido de operador ante un error. Ejemplo: 5. Valor de 0 a 15.', validators=[django.core.validators.MaxValueValidator(15)]),
        ),
    ]
