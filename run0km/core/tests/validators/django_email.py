from django.core import mail

from core.tests.validators.base import Validator


class EmailOutboxValidator(Validator):
    """
        TODO: por ahora mezcla tanto la casilla de salida como las validaciones de los contenidos de los email. Ademas
        falta mejorar los mensajes para los asserts fallidos.
    """

    def outbox(self):
        return mail.outbox

    def email_at(self, index):
        return self.outbox()[index]

    def email_sent_to(self, receiver):
        for out_email in self.outbox():
            if receiver in out_email.to:
                return out_email
        return None

    def assert_includes_receiver(self, receiver_email, out_email):
        self.assertIn(receiver_email, out_email.to)

    def assert_receivers(self, receivers, out_email):
        self.assertEqual(set(receivers),  set(out_email.to))

    def assert_bcc_receivers(self, bcc_receivers, out_email):
        self.assertEqual(set(bcc_receivers),  set(out_email.bcc))

    def assert_sender(self, sender_email, out_email):
        self.assertEqual(sender_email, out_email.from_email)

    def assert_receiver(self, receiver_email, out_email):
        self.assert_receivers([receiver_email],  out_email)

    def assert_email_with_subject(self, expected_subject, out_email):
        self.assertEqual(expected_subject, out_email.subject)

    def assert_email_with_body(self, expected_content, out_email):
        self.assertIn(expected_content, out_email.body)

    def assert_outbox_is_empty(self):
        self.assert_outbox_with_size(size=0)

    def assert_outbox_with_size(self, size):
        self.assertEqual(size, len(self.outbox()))

    def assert_alternatives_content_includes(self, expected_content, out_email):
        # TODO: manejar mejor las partes del email
        html = out_email.alternatives[0][0]
        self.assertIn(expected_content, html)

    def assert_contains_html_content(self, expected_content, out_email):
        self.assert_email_with_body(expected_content, out_email)
        self.assertEqual(out_email.content_subtype, 'html')

    def assert_attachments_with_files_names(self, files_names, out_email):
        self.assertEqual(len(out_email.attachments), len(files_names))

        # TODO: no estoy seguro como se arman esos attachments, parece ser una tupla donde el primer elemento es
        # el nombre de archivo
        for attachments in out_email.attachments:
            file_name = attachments[0]
            self.assertIn(file_name, files_names)

