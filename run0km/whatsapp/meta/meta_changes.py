
class MetaChanges(object):
    def __init__(self, response_as_dict):
        self._response_as_dict = response_as_dict

    @classmethod
    def new_from(cls, response_as_dict):
        if 'mensaje' in response_as_dict:
            from whatsapp.meta.meta_answers import MetaAnswer
            return MetaAnswer(response_as_dict)
        else:
            from whatsapp.meta.meta_status_changes import MetaStatusChanges
            return MetaStatusChanges(response_as_dict)

    def is_change_of_statuses(self):
        raise NotImplementedError('subclass responsibility')