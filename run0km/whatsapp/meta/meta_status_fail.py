
class MetaStatusFailError(object):

    def __init__(self, response_as_dict):
        self._response_as_dict = response_as_dict

    @classmethod
    def new_from(cls, response_as_dict):
        return cls(response_as_dict)

    def code(self):
        return self._response_as_dict.get('code','')

    def title(self):
        return self._response_as_dict.get('title','')

    def detail(self):
        return self._response_as_dict.get('error_data',{}).get('details','')

    def href(self):
        return self._response_as_dict.get('href','')