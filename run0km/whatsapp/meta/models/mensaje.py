from django.db import models
from whatsapp.meta.models.conversacion import Conversacion

class Mensaje(models.Model):
    ACCEPTED = "accepted"
    DELIVERED = "delivered"
    SENT = "sent"
    READ = "read"
    FAILED = "failed"


    STATUS_DE_MENSAJE = [(ACCEPTED, "accepted"), (DELIVERED, "delivered"), (SENT, "sent"),  (READ, "read"), (FAILED, "failed")]

    id_meta = models.CharField(max_length=64)
    status = models.CharField(
        max_length=24,
        choices=STATUS_DE_MENSAJE,
        default=ACCEPTED
    )
    
    fecha_alta = models.DateTimeField(auto_now_add=True)
    numero_telefono_receptor = models.Char<PERSON><PERSON>(max_length=14)
    conversacion = models.ForeignKey(Conversacion, on_delete=models.CASCADE)
    mensaje = models.TextField(null=True)

    def tiene_estado_aceptado(self):
        return self.status == self.ACCEPTED

